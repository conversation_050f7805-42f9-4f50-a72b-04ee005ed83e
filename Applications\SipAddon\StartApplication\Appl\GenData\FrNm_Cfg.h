/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: FrNm
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: FrNm_Cfg.h
 *   Generation Time: 2025-08-05 10:37:17
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

#if !defined(FRNM_CFG_H)
#define FRNM_CFG_H

/**********************************************************************************************************************
 * INCLUDES
 *********************************************************************************************************************/
#include "ComStack_Types.h"
#include "NmStack_Types.h"

/**********************************************************************************************************************
 *  GLOBAL CONSTANT MACROS
 *********************************************************************************************************************/

#define FRNM_NUMBER_OF_SYS_CHANNELS                       3u
#define FRNM_NUMBER_OF_FRNM_CHANNELS                      1u

/* Symbolic name defines for FrNmTxPdus */



/**
 * \defgroup FrNmHandleIds Handle IDs.
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define FrNmConf_FrNmTxPdu_PDU_nm_MyECU_Fr_50a02afb_Tx                0u
/**\} */

/* Symbolic name defines for FrNmTxUserDataPdus */



/**
 * \defgroup FrNmHandleIds Handle IDs.
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define FrNmConf_FrNmUserDataTxPdu_PDU_nm_MyECU_Fr_ae963333_Tx        0u
/**\} */

/* Symbolic name defines for FrNmRxPdus */



/**
 * \defgroup FrNmHandleIds Handle IDs.
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define FrNmConf_FrNmRxPdu_PDU_nm_OtherECU_Fr_30557908_Rx             0u
#define FrNmConf_FrNmRxPdu_PDU_nm_RearECU_Fr_2fa98195_Rx              0u
/**\} */


#define FRNM_VERSION_INFO_API                             STD_OFF
#define FRNM_USER_DATA_ENABLED                            STD_ON
#define FRNM_NODE_DETECTION_ENABLED                       STD_OFF
#define FRNM_SOURCE_NODE_IDENTIFIER_ENABLED               STD_ON
#define FRNM_PASSIVE_MODE_ENABLED                         STD_OFF
#define FRNM_PDU_RX_INDICATION_ENABLED                    STD_OFF
#define FRNM_STATE_CHANGE_INDICATION_ENABLED              STD_OFF
#define FRNM_REMOTE_SLEEP_INDICATION_ENABLED              STD_OFF
#define FRNM_BUS_SYNCHRONIZATION_ENABLED                  STD_OFF
#define FRNM_CONTROL_BIT_VECTOR_ENABLED                   STD_ON
#define FRNM_COORDINATOR_SYNC_SUPPORT                     STD_OFF
#define FRNM_CYCLE_COUNTER_EMULATION                      STD_OFF
#define FRNM_REPEAT_MESSAGE_BIT_ENABLED                   STD_OFF
#define FRNM_HW_VOTE_ENABLED                              STD_OFF
#define FRNM_DUAL_CHANNEL_PDU_ENABLED                     STD_OFF
#define FRNM_IMMEDIATE_TX_ENABLED                         STD_OFF
#define FRNM_COM_USER_DATA_SUPPORT                        STD_ON
#define FRNM_VOTING_NEXT_TO_LAST_REPETITION_CYCLE_DISABLE STD_OFF
#define FRNM_RUNTIME_MEASUREMENT_SUPPORT                  STD_OFF
#define FRNM_PDUR_FRNM_TXCONFIRMATION                     STD_OFF

/* Pre-compile Optimization Switches */
#define FRNM_CAR_WUP_FILTER_FEATURE_ENABLED               STD_OFF
#define FRNM_PN_FEATURE_ENABLED                           STD_OFF
#define FRNM_PN_ERA_CALC_FEATURE_ENABLED                  STD_OFF
#define FRNM_PN_EIRA_CALC_FEATURE_ENABLED                 STD_OFF
#define FRNM_SYNC_PDU_FEATURE_ENABLED                     STD_OFF

/* Schedule Variant Switches */
#define FRNM_PDU_SCHEDULE_VARIANT_ONLY                    0x02u
#define FRNM_PDU_SCHEDULE_VARIANT_1                       STD_OFF
#define FRNM_PDU_SCHEDULE_VARIANT_2                       STD_ON
#define FRNM_PDU_SCHEDULE_VARIANT_3                       STD_OFF
#define FRNM_PDU_SCHEDULE_VARIANT_4                       STD_OFF
#define FRNM_PDU_SCHEDULE_VARIANT_5                       STD_OFF
#define FRNM_PDU_SCHEDULE_VARIANT_6                       STD_OFF
#define FRNM_PDU_SCHEDULE_VARIANT_7                       STD_OFF

#define FRNM_PDU_SCHEDULE_VARIANTS_256                    STD_ON /* ScheduleVariants 2 || 5 || 6 */
#define FRNM_PDU_SCHEDULE_VARIANTS_347                    STD_OFF /* ScheduleVariants 3 || 4 || 7 */


/* Derived Nm Switches */
#define FRNM_COM_CONTROL_ENABLED                          STD_OFF


#define FRNM_MAX_PDU_LENGTH                               8u


#define FRNM_EXPECTED_GENERATOR_COMPATIBILITY_VERSION     0x0800u

#define FRNM_CFG_MAJOR_VERSION                            0x08u
#define FRNM_CFG_MINOR_VERSION                            0x00u

/**********************************************************************************************************************
    General Defines
**********************************************************************************************************************/
#ifndef FRNM_DEV_ERROR_DETECT
#define FRNM_DEV_ERROR_DETECT STD_ON
#endif
#ifndef FRNM_DEV_ERROR_REPORT
#define FRNM_DEV_ERROR_REPORT STD_ON
#endif
#ifndef FRNM_USE_DUMMY_STATEMENT
#define FRNM_USE_DUMMY_STATEMENT STD_OFF /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef FRNM_DUMMY_STATEMENT
#define FRNM_DUMMY_STATEMENT(v)  /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */  /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef FRNM_DUMMY_STATEMENT_CONST
#define FRNM_DUMMY_STATEMENT_CONST(v)  /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */  /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef FRNM_ATOMIC_BIT_ACCESS_IN_BITFIELD
#define FRNM_ATOMIC_BIT_ACCESS_IN_BITFIELD STD_OFF /* /MICROSAR/EcuC/EcucGeneral/AtomicBitAccessInBitfield */
#endif
#ifndef FRNM_ATOMIC_VARIABLE_ACCESS
#define FRNM_ATOMIC_VARIABLE_ACCESS 32u /* /MICROSAR/EcuC/EcucGeneral/AtomicVariableAccess */
#endif
#ifndef FRNM_PROCESSOR_TC377T
#define FRNM_PROCESSOR_TC377T
#endif
#ifndef FRNM_COMP_TASKING
#define FRNM_COMP_TASKING
#endif
#ifndef FRNM_GEN_GENERATOR_MSR
#define FRNM_GEN_GENERATOR_MSR
#endif
#ifndef FRNM_CPUTYPE_BITORDER_LSB2MSB
#define FRNM_CPUTYPE_BITORDER_LSB2MSB /* /MICROSAR/vSet/vSetPlatform/vSetBitOrder */
#endif
#ifndef FRNM_CONFIGURATION_VARIANT_PRECOMPILE
#define FRNM_CONFIGURATION_VARIANT_PRECOMPILE 1
#endif
#ifndef FRNM_CONFIGURATION_VARIANT_LINKTIME
#define FRNM_CONFIGURATION_VARIANT_LINKTIME 2
#endif
#ifndef FRNM_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE
#define FRNM_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE 3
#endif
#ifndef FRNM_CONFIGURATION_VARIANT
#define FRNM_CONFIGURATION_VARIANT FRNM_CONFIGURATION_VARIANT_PRECOMPILE
#endif
#ifndef FRNM_POSTBUILD_VARIANT_SUPPORT
#define FRNM_POSTBUILD_VARIANT_SUPPORT STD_OFF
#endif
 

/**********************************************************************************************************************
  GLOBAL CONSTANT MACROS
**********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL CONSTANT MACROS
**********************************************************************************************************************/
/** 
  \defgroup  FrNmPCDataSwitches  FrNm Data Switches  (PRE_COMPILE)
  \brief  These defines are used to deactivate data and their processing.
  \{
*/ 
#define FRNM_CHANNELCONFIG                                            STD_ON
#define FRNM_ACTIVEWAKEUPBITENABLEDOFCHANNELCONFIG                    STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.ActiveWakeupBitEnabled' Reason: 'the value of FrNm_ActiveWakeupBitEnabledOfChannelConfig is always 'false' due to this, the array is deactivated.' */
#define FRNM_CARWAKEUPBITMASKOFCHANNELCONFIG                          STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.CarWakeUpBitMask' Reason: 'Wakeup Rx feature not enabled' */
#define FRNM_CARWAKEUPFILTERENABLEDOFCHANNELCONFIG                    STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.CarWakeUpFilterEnabled' Reason: 'Wakeup Filter feature not enabled' */
#define FRNM_CARWAKEUPFILTERNODEIDOFCHANNELCONFIG                     STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.CarWakeUpFilterNodeId' Reason: 'Wakeup Filter feature not enabled' */
#define FRNM_CARWAKEUPRXENABLEDOFCHANNELCONFIG                        STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.CarWakeUpRxEnabled' Reason: 'Wakeup Rx feature not enabled' */
#define FRNM_CHANNELHANDLEOFCHANNELCONFIG                             STD_ON
#define FRNM_CHANNELIDOFCHANNELCONFIG                                 STD_ON
#define FRNM_CONTROLBITVECTORACTIVEOFCHANNELCONFIG                    STD_ON
#define FRNM_CRIBITALWAYSENABLEDOFCHANNELCONFIG                       STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.CriBitAlwaysEnabled' Reason: 'the value of FrNm_CriBitAlwaysEnabledOfChannelConfig is always 'false' due to this, the array is deactivated.' */
#define FRNM_DATACYCLEMASKOFCHANNELCONFIG                             STD_ON
#define FRNM_HWREPEATMSGREQUESTBITOFCHANNELCONFIG                     STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.HwRepeatMsgRequestBit' Reason: 'the value of FrNm_HwRepeatMsgRequestBitOfChannelConfig is always 'false' due to this, the array is deactivated.' */
#define FRNM_HWVOTESUPPORTINSCHEDULEVARIANTOFCHANNELCONFIG            STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.HwVoteSupportInScheduleVariant' Reason: 'HwVoteEnabled is disabled' */
#define FRNM_PDUSCHEDULEVARIANTOFCHANNELCONFIG                        STD_ON
#define FRNM_PNERACALCENABLEDOFCHANNELCONFIG                          STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.PnEraCalcEnabled' Reason: 'EraCalcFeature is disabled' */
#define FRNM_PNERARXPDUIDOFCHANNELCONFIG                              STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.PnEraRxPduId' Reason: 'EraCalcFeature is disabled' */
#define FRNM_READYSLEEPCNTOFCHANNELCONFIG                             STD_ON
#define FRNM_REMOTESLEEPINDTIMEOFCHANNELCONFIG                        STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.RemoteSleepIndTime' Reason: 'RemoteSleepIndication is disabled' */
#define FRNM_REPEATMESSAGEREQUESTBITACTIVEOFCHANNELCONFIG             STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.RepeatMessageRequestBitActive' Reason: 'NodeDetection is disabled' */
#define FRNM_REPEATMESSAGETIMEOFCHANNELCONFIG                         STD_ON
#define FRNM_REPETITIONCYCLEOFCHANNELCONFIG                           STD_ON
#define FRNM_SYNCPDUMASTERENABLEDOFCHANNELCONFIG                      STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.SyncPduMasterEnabled' Reason: 'SyncPduFeature is disabled' */
#define FRNM_SYNCPDUTXREQUESTCYCLEOFFSETOFCHANNELCONFIG               STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.SyncPduTxRequestCycleOffset' Reason: 'SyncPduFeature is disabled' */
#define FRNM_SYNCHRONIZATIONPOINTENABLEDOFCHANNELCONFIG               STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.SynchronizationPointEnabled' Reason: 'CoordinatorSupport is OFF' */
#define FRNM_TRANSMITDATAINSTATICSLOTOFCHANNELCONFIG                  STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.TransmitDataInStaticSlot' Reason: 'the value of FrNm_TransmitDataInStaticSlotOfChannelConfig is always 'false' due to this, the array is deactivated.' */
#define FRNM_TRANSMITSEPARATEVOTEOFCHANNELCONFIG                      STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.TransmitSeparateVote' Reason: 'the value of FrNm_TransmitSeparateVoteOfChannelConfig is always 'false' due to this, the array is deactivated.' */
#define FRNM_TRANSMITSEPERATEVOTEDYNAMICOFCHANNELCONFIG               STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.TransmitSeperateVoteDynamic' Reason: 'the value of FrNm_TransmitSeperateVoteDynamicOfChannelConfig is always 'false' due to this, the array is deactivated.' */
#define FRNM_VOTEINHIBITIONENABLEDOFCHANNELCONFIG                     STD_OFF  /**< Deactivateable: 'FrNm_ChannelConfig.VoteInhibitionEnabled' Reason: 'VotingNextToLastRepetitionCycleDisable is OFF' */
#define FRNM_VOTINGCYCLEMASKOFCHANNELCONFIG                           STD_ON
#define FRNM_COORDREADYTOSLEEPSTATE                                   STD_OFF  /**< Deactivateable: 'FrNm_CoordReadyToSleepState' Reason: 'CoordinatorSyncSupport is OFF' */
#define FRNM_CURRENTFRCYCLE                                           STD_ON
#define FRNM_FINALMAGICNUMBER                                         STD_OFF  /**< Deactivateable: 'FrNm_FinalMagicNumber' Reason: 'the module configuration does not support flashing of data.' */
#define FRNM_GENERATORCOMPATIBILITYVERSION                            STD_OFF  /**< Deactivateable: 'FrNm_GeneratorCompatibilityVersion' Reason: 'Variant is not VARIANT-POST-BUILD-LOADABLE' */
#define FRNM_INITDATAHASHCODE                                         STD_OFF  /**< Deactivateable: 'FrNm_InitDataHashCode' Reason: 'the module configuration does not support flashing of data.' */
#define FRNM_LASTNETWORKREQUESTED                                     STD_ON
#define FRNM_MAINACROSSFRCYCLE                                        STD_ON
#define FRNM_MODULEINITIALIZED                                        STD_ON
#define FRNM_MSGCONFIRMATIONFLAG                                      STD_ON
#define FRNM_MSGINDICATIONFLAG                                        STD_ON
#define FRNM_NETWORKREQUESTED                                         STD_ON
#define FRNM_NETWORKRESTARTFLAG                                       STD_ON
#define FRNM_NETWORKTIMER                                             STD_ON
#define FRNM_NMSTATE                                                  STD_ON
#define FRNM_PBCHANNELCONFIG                                          STD_ON
#define FRNM_MSGTIMEOUTTIMEOFPBCHANNELCONFIG                          STD_ON
#define FRNM_NODEIDOFPBCHANNELCONFIG                                  STD_ON
#define FRNM_NODEIDPOSITIONOFPBCHANNELCONFIG                          STD_ON
#define FRNM_PNENABLEDOFPBCHANNELCONFIG                               STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.PnEnabled' Reason: 'Pn feature is disabled' */
#define FRNM_PNRESETTIMEOFPBCHANNELCONFIG                             STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.PnResetTime' Reason: 'Pn feature is disabled' */
#define FRNM_RXMESSAGEDATAENDIDXOFPBCHANNELCONFIG                     STD_ON
#define FRNM_RXMESSAGEDATALENGTHOFPBCHANNELCONFIG                     STD_ON
#define FRNM_RXMESSAGEDATASTARTIDXOFPBCHANNELCONFIG                   STD_ON
#define FRNM_RXMESSAGEDATAUSEDOFPBCHANNELCONFIG                       STD_ON
#define FRNM_RXMESSAGEDATA_CBVIDXOFPBCHANNELCONFIG                    STD_ON
#define FRNM_RXMESSAGEDATA_CBVUSEDOFPBCHANNELCONFIG                   STD_ON
#define FRNM_RXMESSAGEDATA_CARWAKEUPFILTERBYTEIDXOFPBCHANNELCONFIG    STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.RxMessageData_CarWakeUpFilterByteIdx' Reason: 'the optional indirection is deactivated because RxMessageData_CarWakeUpFilterByteUsedOfPbChannelConfig is always 'FALSE' and the target of the indirection is of the Configuration Class 'PRE_COMPILE'.' */
#define FRNM_RXMESSAGEDATA_CARWAKEUPFILTERBYTEUSEDOFPBCHANNELCONFIG   STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.RxMessageData_CarWakeUpFilterByteUsed' Reason: 'the optional indirection is deactivated because RxMessageData_CarWakeUpFilterByteUsedOfPbChannelConfig is always 'FALSE' and the target of the indirection is of the Configuration Class 'PRE_COMPILE'.' */
#define FRNM_RXMESSAGEDATA_NIDIDXOFPBCHANNELCONFIG                    STD_ON
#define FRNM_RXMESSAGEDATA_NIDUSEDOFPBCHANNELCONFIG                   STD_ON
#define FRNM_RXMESSAGEDATA_USERDATAENDIDXOFPBCHANNELCONFIG            STD_ON
#define FRNM_RXMESSAGEDATA_USERDATALENGTHOFPBCHANNELCONFIG            STD_ON
#define FRNM_RXMESSAGEDATA_USERDATASTARTIDXOFPBCHANNELCONFIG          STD_ON
#define FRNM_RXMESSAGEDATA_USERDATAUSEDOFPBCHANNELCONFIG              STD_ON
#define FRNM_TXDATAPDUIDENDIDXOFPBCHANNELCONFIG                       STD_ON
#define FRNM_TXDATAPDUIDSTARTIDXOFPBCHANNELCONFIG                     STD_ON
#define FRNM_TXDATAPDUIDUSEDOFPBCHANNELCONFIG                         STD_ON
#define FRNM_TXMESSAGEDATAENDIDXOFPBCHANNELCONFIG                     STD_ON
#define FRNM_TXMESSAGEDATALENGTHOFPBCHANNELCONFIG                     STD_ON
#define FRNM_TXMESSAGEDATASTARTIDXOFPBCHANNELCONFIG                   STD_ON
#define FRNM_TXMESSAGEDATAUSEDOFPBCHANNELCONFIG                       STD_ON
#define FRNM_TXMESSAGEDATA_CBVIDXOFPBCHANNELCONFIG                    STD_ON
#define FRNM_TXMESSAGEDATA_CBVUSEDOFPBCHANNELCONFIG                   STD_ON
#define FRNM_TXMESSAGEDATA_NIDIDXOFPBCHANNELCONFIG                    STD_ON
#define FRNM_TXMESSAGEDATA_NIDUSEDOFPBCHANNELCONFIG                   STD_ON
#define FRNM_TXMESSAGEDATA_PNFILTERMASKENDIDXOFPBCHANNELCONFIG        STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxMessageData_PnFilterMaskEndIdx' Reason: 'the optional indirection is deactivated because TxMessageData_PnFilterMaskUsedOfPbChannelConfig is always 'FALSE' and the target of the indirection is of the Configuration Class 'PRE_COMPILE'.' */
#define FRNM_TXMESSAGEDATA_PNFILTERMASKSTARTIDXOFPBCHANNELCONFIG      STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxMessageData_PnFilterMaskStartIdx' Reason: 'the optional indirection is deactivated because TxMessageData_PnFilterMaskUsedOfPbChannelConfig is always 'FALSE' and the target of the indirection is of the Configuration Class 'PRE_COMPILE'.' */
#define FRNM_TXMESSAGEDATA_PNFILTERMASKUSEDOFPBCHANNELCONFIG          STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxMessageData_PnFilterMaskUsed' Reason: 'the optional indirection is deactivated because TxMessageData_PnFilterMaskUsedOfPbChannelConfig is always 'FALSE' and the target of the indirection is of the Configuration Class 'PRE_COMPILE'.' */
#define FRNM_TXMESSAGEDATA_USERDATAENDIDXOFPBCHANNELCONFIG            STD_ON
#define FRNM_TXMESSAGEDATA_USERDATALENGTHOFPBCHANNELCONFIG            STD_ON
#define FRNM_TXMESSAGEDATA_USERDATASTARTIDXOFPBCHANNELCONFIG          STD_ON
#define FRNM_TXMESSAGEDATA_USERDATAUSEDOFPBCHANNELCONFIG              STD_ON
#define FRNM_TXMESSAGEDATA_VOTEIDXOFPBCHANNELCONFIG                   STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxMessageData_VOTEIdx' Reason: 'the optional indirection is deactivated because TxMessageData_VOTEUsedOfPbChannelConfig is always 'FALSE' and the target of the indirection is of the Configuration Class 'PRE_COMPILE'.' */
#define FRNM_TXMESSAGEDATA_VOTEUSEDOFPBCHANNELCONFIG                  STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxMessageData_VOTEUsed' Reason: 'the optional indirection is deactivated because TxMessageData_VOTEUsedOfPbChannelConfig is always 'FALSE' and the target of the indirection is of the Configuration Class 'PRE_COMPILE'.' */
#define FRNM_TXSYNCPDUIDENDIDXOFPBCHANNELCONFIG                       STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxSyncPduIdEndIdx' Reason: 'SyncPduFeature is disabled' */
#define FRNM_TXSYNCPDUIDSTARTIDXOFPBCHANNELCONFIG                     STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxSyncPduIdStartIdx' Reason: 'SyncPduFeature is disabled' */
#define FRNM_TXSYNCPDUIDUSEDOFPBCHANNELCONFIG                         STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxSyncPduIdUsed' Reason: 'SyncPduFeature is disabled' */
#define FRNM_TXSYNCPDUMESSAGEDATAENDIDXOFPBCHANNELCONFIG              STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxSyncPduMessageDataEndIdx' Reason: 'SyncPduFeature is disabled' */
#define FRNM_TXSYNCPDUMESSAGEDATALENGTHOFPBCHANNELCONFIG              STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxSyncPduMessageDataLength' Reason: 'SyncPduFeature is disabled' */
#define FRNM_TXSYNCPDUMESSAGEDATASTARTIDXOFPBCHANNELCONFIG            STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxSyncPduMessageDataStartIdx' Reason: 'SyncPduFeature is disabled' */
#define FRNM_TXSYNCPDUMESSAGEDATAUSEDOFPBCHANNELCONFIG                STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxSyncPduMessageDataUsed' Reason: 'SyncPduFeature is disabled' */
#define FRNM_TXUSERDATAPDUIDOFPBCHANNELCONFIG                         STD_ON
#define FRNM_TXVOTEPDUIDENDIDXOFPBCHANNELCONFIG                       STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxVotePduIdEndIdx' Reason: 'the optional indirection is deactivated because TxVotePduIdUsedOfPbChannelConfig is always 'FALSE' and the target of the indirection is of the Configuration Class 'PRE_COMPILE'.' */
#define FRNM_TXVOTEPDUIDSTARTIDXOFPBCHANNELCONFIG                     STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxVotePduIdStartIdx' Reason: 'the optional indirection is deactivated because TxVotePduIdUsedOfPbChannelConfig is always 'FALSE' and the target of the indirection is of the Configuration Class 'PRE_COMPILE'.' */
#define FRNM_TXVOTEPDUIDUSEDOFPBCHANNELCONFIG                         STD_OFF  /**< Deactivateable: 'FrNm_PbChannelConfig.TxVotePduIdUsed' Reason: 'the optional indirection is deactivated because TxVotePduIdUsedOfPbChannelConfig is always 'FALSE' and the target of the indirection is of the Configuration Class 'PRE_COMPILE'.' */
#define FRNM_PBPNCCLUSTERCONFIG                                       STD_OFF  /**< Deactivateable: 'FrNm_PbPncClusterConfig' Reason: 'the struct is deactivated because all elements are deactivated.' */
#define FRNM_PBPNCCLUSTERCONFIGEXT                                    STD_OFF  /**< Deactivateable: 'FrNm_PbPncClusterConfigExt' Reason: 'EraCalcFeature is disabled' */
#define FRNM_PDUIDTOCHINDEX                                           STD_OFF  /**< Deactivateable: 'FrNm_PduIdToChIndex' Reason: '(PB-S is not active or channel reference is invariant over all channels) and PB-L is not active' */
#define FRNM_PNCLUSTERREQ                                             STD_OFF  /**< Deactivateable: 'FrNm_PnClusterReq' Reason: 'EiraCalcFeature is disabled' */
#define FRNM_PNCLUSTERREQEXT                                          STD_OFF  /**< Deactivateable: 'FrNm_PnClusterReqExt' Reason: 'EraCalcFeature is disabled' */
#define FRNM_PNCLUSTERREQEXTNEW                                       STD_OFF  /**< Deactivateable: 'FrNm_PnClusterReqExtNew' Reason: 'EraCalcFeature is disabled' */
#define FRNM_PNCLUSTERREQNEW                                          STD_OFF  /**< Deactivateable: 'FrNm_PnClusterReqNew' Reason: 'EiraCalcFeature is disabled' */
#define FRNM_PNCLUSTERRESETTIMER                                      STD_OFF  /**< Deactivateable: 'FrNm_PnClusterResetTimer' Reason: 'EiraCalcFeature is disabled' */
#define FRNM_PNCLUSTERRESETTIMEREXT                                   STD_OFF  /**< Deactivateable: 'FrNm_PnClusterResetTimerExt' Reason: 'EraCalcFeature is disabled' */
#define FRNM_PNEIRACALCENABLED                                        STD_OFF  /**< Deactivateable: 'FrNm_PnEiraCalcEnabled' Reason: 'EiraCalcFeature is disabled' */
#define FRNM_PNEIRARXPDUID                                            STD_OFF  /**< Deactivateable: 'FrNm_PnEiraRxPduId' Reason: 'EiraCalcFeature is disabled' */
#define FRNM_PNENABLEDINVARIANT                                       STD_OFF  /**< Deactivateable: 'FrNm_PnEnabledInVariant' Reason: 'EiraCalcFeature is disabled' */
#define FRNM_PNFILTERMASK                                             STD_OFF  /**< Deactivateable: 'FrNm_PnFilterMask' Reason: 'Pn feature is disabled' */
#define FRNM_PNINFOLENGTH                                             STD_OFF  /**< Deactivateable: 'FrNm_PnInfoLength' Reason: 'EiraCalcFeature and EraCalcFeature disabled' */
#define FRNM_PNINFOOFFSET                                             STD_OFF  /**< Deactivateable: 'FrNm_PnInfoOffset' Reason: 'EiraCalcFeature and EraCalcFeature disabled' */
#define FRNM_REMOTESLEEPINDSTATE                                      STD_OFF  /**< Deactivateable: 'FrNm_RemoteSleepIndState' Reason: 'RemoteSleepIndication is not enabled' */
#define FRNM_REMOTESLEEPTIMER                                         STD_OFF  /**< Deactivateable: 'FrNm_RemoteSleepTimer' Reason: 'RemoteSleepIndication is disabled' */
#define FRNM_REPEATMSGREQFLAG                                         STD_OFF  /**< Deactivateable: 'FrNm_RepeatMsgReqFlag' Reason: 'NodeDetection is disabled' */
#define FRNM_REPEATMSGREQUESTINDFLAG                                  STD_OFF  /**< Deactivateable: 'FrNm_RepeatMsgRequestIndFlag' Reason: 'NodeDetection is disabled' */
#define FRNM_RXMESSAGEDATA                                            STD_ON
#define FRNM_RXMESSAGEVOTE                                            STD_ON
#define FRNM_SENDSYNCPDUSFLAG                                         STD_OFF  /**< Deactivateable: 'FrNm_SendSyncPdusFlag' Reason: 'SyncPduFeature is disabled' */
#define FRNM_SIZEOFCHANNELCONFIG                                      STD_ON
#define FRNM_SIZEOFCURRENTFRCYCLE                                     STD_ON
#define FRNM_SIZEOFLASTNETWORKREQUESTED                               STD_ON
#define FRNM_SIZEOFMSGCONFIRMATIONFLAG                                STD_ON
#define FRNM_SIZEOFMSGINDICATIONFLAG                                  STD_ON
#define FRNM_SIZEOFNETWORKREQUESTED                                   STD_ON
#define FRNM_SIZEOFNETWORKRESTARTFLAG                                 STD_ON
#define FRNM_SIZEOFNETWORKTIMER                                       STD_ON
#define FRNM_SIZEOFNMSTATE                                            STD_ON
#define FRNM_SIZEOFPBCHANNELCONFIG                                    STD_ON
#define FRNM_SIZEOFRXMESSAGEVOTE                                      STD_ON
#define FRNM_SIZEOFSYSTONMCHIND                                       STD_ON
#define FRNM_SIZEOFTIMEOUTTIMER                                       STD_ON
#define FRNM_SIZEOFTXMESSAGEDATA                                      STD_ON
#define FRNM_SYSTONMCHIND                                             STD_ON
#define FRNM_TIMEOUTTIMER                                             STD_ON
#define FRNM_TXCONTROLSTATE                                           STD_OFF  /**< Deactivateable: 'FrNm_TxControlState' Reason: 'CommunicationControl is not enabled' */
#define FRNM_TXCONTROLSTATEREQUEST                                    STD_OFF  /**< Deactivateable: 'FrNm_TxControlStateRequest' Reason: 'CommunicationControl is not enabled' */
#define FRNM_TXDATAPDUID                                              STD_ON
#define FRNM_TXMESSAGEDATA                                            STD_ON
#define FRNM_TXMESSAGEVOTE                                            STD_OFF  /**< Deactivateable: 'FrNm_TxMessageVote' Reason: 'FRNM_TX_MESSAGE_VOTE_ENABLED is not defined' */
#define FRNM_TXSYNCPDUID                                              STD_OFF  /**< Deactivateable: 'FrNm_TxSyncPduId' Reason: 'SyncPduFeature is disabled' */
#define FRNM_TXSYNCPDUMESSAGEDATA                                     STD_OFF  /**< Deactivateable: 'FrNm_TxSyncPduMessageData' Reason: 'SyncPduFeature is disabled' */
#define FRNM_TXVOTEPDUID                                              STD_OFF  /**< Deactivateable: 'FrNm_TxVotePduId' Reason: 'the value of FrNm_TxVotePduId is always 'FRNM_NO_TXVOTEPDUID' due to this, the array is deactivated.' */
#define FRNM_PCCONFIG                                                 STD_ON
#define FRNM_CHANNELCONFIGOFPCCONFIG                                  STD_ON
#define FRNM_CURRENTFRCYCLEOFPCCONFIG                                 STD_ON
#define FRNM_FINALMAGICNUMBEROFPCCONFIG                               STD_OFF  /**< Deactivateable: 'FrNm_PCConfig.FinalMagicNumber' Reason: 'the module configuration does not support flashing of data.' */
#define FRNM_GENERATORCOMPATIBILITYVERSIONOFPCCONFIG                  STD_OFF  /**< Deactivateable: 'FrNm_PCConfig.GeneratorCompatibilityVersion' Reason: 'Variant is not VARIANT-POST-BUILD-LOADABLE' */
#define FRNM_INITDATAHASHCODEOFPCCONFIG                               STD_OFF  /**< Deactivateable: 'FrNm_PCConfig.InitDataHashCode' Reason: 'the module configuration does not support flashing of data.' */
#define FRNM_LASTNETWORKREQUESTEDOFPCCONFIG                           STD_ON
#define FRNM_MAINACROSSFRCYCLEOFPCCONFIG                              STD_ON
#define FRNM_MODULEINITIALIZEDOFPCCONFIG                              STD_ON
#define FRNM_MSGCONFIRMATIONFLAGOFPCCONFIG                            STD_ON
#define FRNM_MSGINDICATIONFLAGOFPCCONFIG                              STD_ON
#define FRNM_NETWORKREQUESTEDOFPCCONFIG                               STD_ON
#define FRNM_NETWORKRESTARTFLAGOFPCCONFIG                             STD_ON
#define FRNM_NETWORKTIMEROFPCCONFIG                                   STD_ON
#define FRNM_NMSTATEOFPCCONFIG                                        STD_ON
#define FRNM_PBCHANNELCONFIGOFPCCONFIG                                STD_ON
#define FRNM_PNEIRACALCENABLEDOFPCCONFIG                              STD_OFF  /**< Deactivateable: 'FrNm_PCConfig.PnEiraCalcEnabled' Reason: 'EiraCalcFeature is disabled' */
#define FRNM_PNEIRARXPDUIDOFPCCONFIG                                  STD_OFF  /**< Deactivateable: 'FrNm_PCConfig.PnEiraRxPduId' Reason: 'EiraCalcFeature is disabled' */
#define FRNM_PNENABLEDINVARIANTOFPCCONFIG                             STD_OFF  /**< Deactivateable: 'FrNm_PCConfig.PnEnabledInVariant' Reason: 'EiraCalcFeature is disabled' */
#define FRNM_PNINFOLENGTHOFPCCONFIG                                   STD_OFF  /**< Deactivateable: 'FrNm_PCConfig.PnInfoLength' Reason: 'EiraCalcFeature and EraCalcFeature disabled' */
#define FRNM_PNINFOOFFSETOFPCCONFIG                                   STD_OFF  /**< Deactivateable: 'FrNm_PCConfig.PnInfoOffset' Reason: 'EiraCalcFeature and EraCalcFeature disabled' */
#define FRNM_RXMESSAGEDATAOFPCCONFIG                                  STD_ON
#define FRNM_RXMESSAGEVOTEOFPCCONFIG                                  STD_ON
#define FRNM_SIZEOFCHANNELCONFIGOFPCCONFIG                            STD_ON
#define FRNM_SIZEOFCURRENTFRCYCLEOFPCCONFIG                           STD_ON
#define FRNM_SIZEOFLASTNETWORKREQUESTEDOFPCCONFIG                     STD_ON
#define FRNM_SIZEOFMSGCONFIRMATIONFLAGOFPCCONFIG                      STD_ON
#define FRNM_SIZEOFMSGINDICATIONFLAGOFPCCONFIG                        STD_ON
#define FRNM_SIZEOFNETWORKREQUESTEDOFPCCONFIG                         STD_ON
#define FRNM_SIZEOFNETWORKRESTARTFLAGOFPCCONFIG                       STD_ON
#define FRNM_SIZEOFNETWORKTIMEROFPCCONFIG                             STD_ON
#define FRNM_SIZEOFNMSTATEOFPCCONFIG                                  STD_ON
#define FRNM_SIZEOFPBCHANNELCONFIGOFPCCONFIG                          STD_ON
#define FRNM_SIZEOFRXMESSAGEVOTEOFPCCONFIG                            STD_ON
#define FRNM_SIZEOFSYSTONMCHINDOFPCCONFIG                             STD_ON
#define FRNM_SIZEOFTIMEOUTTIMEROFPCCONFIG                             STD_ON
#define FRNM_SIZEOFTXMESSAGEDATAOFPCCONFIG                            STD_ON
#define FRNM_SYSTONMCHINDOFPCCONFIG                                   STD_ON
#define FRNM_TIMEOUTTIMEROFPCCONFIG                                   STD_ON
#define FRNM_TXDATAPDUIDOFPCCONFIG                                    STD_ON
#define FRNM_TXMESSAGEDATAOFPCCONFIG                                  STD_ON
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCMinNumericValueDefines  FrNm Min Numeric Value Defines (PRE_COMPILE)
  \brief  These defines are used to implement against the minimum value in numerical based data.
  \{
*/ 
#define FRNM_MIN_CURRENTFRCYCLE                                       0u
#define FRNM_MIN_NETWORKRESTARTFLAG                                   0u
#define FRNM_MIN_NETWORKTIMER                                         0u
#define FRNM_MIN_NMSTATE                                              0u
#define FRNM_MIN_RXMESSAGEDATA                                        0u
#define FRNM_MIN_RXMESSAGEVOTE                                        0u
#define FRNM_MIN_TIMEOUTTIMER                                         0u
#define FRNM_MIN_TXMESSAGEDATA                                        0u
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCMaxNumericValueDefines  FrNm Max Numeric Value Defines (PRE_COMPILE)
  \brief  These defines are used to implement against the maximum value in numerical based data.
  \{
*/ 
#define FRNM_MAX_CURRENTFRCYCLE                                       255u
#define FRNM_MAX_NETWORKRESTARTFLAG                                   255u
#define FRNM_MAX_NETWORKTIMER                                         65535u
#define FRNM_MAX_NMSTATE                                              255u
#define FRNM_MAX_RXMESSAGEDATA                                        255u
#define FRNM_MAX_RXMESSAGEVOTE                                        255u
#define FRNM_MAX_TIMEOUTTIMER                                         65535u
#define FRNM_MAX_TXMESSAGEDATA                                        255u
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCNoReferenceDefines  FrNm No Reference Defines (PRE_COMPILE)
  \brief  These defines are used to indicate unused indexes in data relations.
  \{
*/ 
#define FRNM_NO_RXMESSAGEDATAENDIDXOFPBCHANNELCONFIG                  255u
#define FRNM_NO_RXMESSAGEDATASTARTIDXOFPBCHANNELCONFIG                255u
#define FRNM_NO_RXMESSAGEDATA_CBVIDXOFPBCHANNELCONFIG                 255u
#define FRNM_NO_RXMESSAGEDATA_NIDIDXOFPBCHANNELCONFIG                 255u
#define FRNM_NO_RXMESSAGEDATA_USERDATAENDIDXOFPBCHANNELCONFIG         255u
#define FRNM_NO_RXMESSAGEDATA_USERDATASTARTIDXOFPBCHANNELCONFIG       255u
#define FRNM_NO_TXDATAPDUIDENDIDXOFPBCHANNELCONFIG                    255u
#define FRNM_NO_TXDATAPDUIDSTARTIDXOFPBCHANNELCONFIG                  255u
#define FRNM_NO_TXMESSAGEDATAENDIDXOFPBCHANNELCONFIG                  255u
#define FRNM_NO_TXMESSAGEDATASTARTIDXOFPBCHANNELCONFIG                255u
#define FRNM_NO_TXMESSAGEDATA_CBVIDXOFPBCHANNELCONFIG                 255u
#define FRNM_NO_TXMESSAGEDATA_NIDIDXOFPBCHANNELCONFIG                 255u
#define FRNM_NO_TXMESSAGEDATA_USERDATAENDIDXOFPBCHANNELCONFIG         255u
#define FRNM_NO_TXMESSAGEDATA_USERDATASTARTIDXOFPBCHANNELCONFIG       255u
#define FRNM_NO_TXUSERDATAPDUIDOFPBCHANNELCONFIG                      255u
#define FRNM_NO_SYSTONMCHIND                                          255u
#define FRNM_NO_TXDATAPDUID                                           255u
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCIsReducedToDefineDefines  FrNm Is Reduced To Define Defines (PRE_COMPILE)
  \brief  If all values in a CONST array or an element in a CONST array of structs are equal, the define is STD_ON else STD_OFF.
  \{
*/ 
#define FRNM_ISDEF_CHANNELHANDLEOFCHANNELCONFIG                       STD_ON
#define FRNM_ISDEF_CHANNELIDOFCHANNELCONFIG                           STD_ON
#define FRNM_ISDEF_CONTROLBITVECTORACTIVEOFCHANNELCONFIG              STD_ON
#define FRNM_ISDEF_DATACYCLEMASKOFCHANNELCONFIG                       STD_ON
#define FRNM_ISDEF_PDUSCHEDULEVARIANTOFCHANNELCONFIG                  STD_ON
#define FRNM_ISDEF_READYSLEEPCNTOFCHANNELCONFIG                       STD_ON
#define FRNM_ISDEF_REPEATMESSAGETIMEOFCHANNELCONFIG                   STD_ON
#define FRNM_ISDEF_REPETITIONCYCLEOFCHANNELCONFIG                     STD_ON
#define FRNM_ISDEF_VOTINGCYCLEMASKOFCHANNELCONFIG                     STD_ON
#define FRNM_ISDEF_MSGTIMEOUTTIMEOFPBCHANNELCONFIG                    STD_ON
#define FRNM_ISDEF_NODEIDOFPBCHANNELCONFIG                            STD_ON
#define FRNM_ISDEF_NODEIDPOSITIONOFPBCHANNELCONFIG                    STD_ON
#define FRNM_ISDEF_RXMESSAGEDATAENDIDXOFPBCHANNELCONFIG               STD_ON
#define FRNM_ISDEF_RXMESSAGEDATALENGTHOFPBCHANNELCONFIG               STD_ON
#define FRNM_ISDEF_RXMESSAGEDATASTARTIDXOFPBCHANNELCONFIG             STD_ON
#define FRNM_ISDEF_RXMESSAGEDATAUSEDOFPBCHANNELCONFIG                 STD_ON
#define FRNM_ISDEF_RXMESSAGEDATA_CBVIDXOFPBCHANNELCONFIG              STD_ON
#define FRNM_ISDEF_RXMESSAGEDATA_CBVUSEDOFPBCHANNELCONFIG             STD_ON
#define FRNM_ISDEF_RXMESSAGEDATA_NIDIDXOFPBCHANNELCONFIG              STD_ON
#define FRNM_ISDEF_RXMESSAGEDATA_NIDUSEDOFPBCHANNELCONFIG             STD_ON
#define FRNM_ISDEF_RXMESSAGEDATA_USERDATAENDIDXOFPBCHANNELCONFIG      STD_ON
#define FRNM_ISDEF_RXMESSAGEDATA_USERDATALENGTHOFPBCHANNELCONFIG      STD_ON
#define FRNM_ISDEF_RXMESSAGEDATA_USERDATASTARTIDXOFPBCHANNELCONFIG    STD_ON
#define FRNM_ISDEF_RXMESSAGEDATA_USERDATAUSEDOFPBCHANNELCONFIG        STD_ON
#define FRNM_ISDEF_TXDATAPDUIDENDIDXOFPBCHANNELCONFIG                 STD_ON
#define FRNM_ISDEF_TXDATAPDUIDSTARTIDXOFPBCHANNELCONFIG               STD_ON
#define FRNM_ISDEF_TXDATAPDUIDUSEDOFPBCHANNELCONFIG                   STD_ON
#define FRNM_ISDEF_TXMESSAGEDATAENDIDXOFPBCHANNELCONFIG               STD_ON
#define FRNM_ISDEF_TXMESSAGEDATALENGTHOFPBCHANNELCONFIG               STD_ON
#define FRNM_ISDEF_TXMESSAGEDATASTARTIDXOFPBCHANNELCONFIG             STD_ON
#define FRNM_ISDEF_TXMESSAGEDATAUSEDOFPBCHANNELCONFIG                 STD_ON
#define FRNM_ISDEF_TXMESSAGEDATA_CBVIDXOFPBCHANNELCONFIG              STD_ON
#define FRNM_ISDEF_TXMESSAGEDATA_CBVUSEDOFPBCHANNELCONFIG             STD_ON
#define FRNM_ISDEF_TXMESSAGEDATA_NIDIDXOFPBCHANNELCONFIG              STD_ON
#define FRNM_ISDEF_TXMESSAGEDATA_NIDUSEDOFPBCHANNELCONFIG             STD_ON
#define FRNM_ISDEF_TXMESSAGEDATA_USERDATAENDIDXOFPBCHANNELCONFIG      STD_ON
#define FRNM_ISDEF_TXMESSAGEDATA_USERDATALENGTHOFPBCHANNELCONFIG      STD_ON
#define FRNM_ISDEF_TXMESSAGEDATA_USERDATASTARTIDXOFPBCHANNELCONFIG    STD_ON
#define FRNM_ISDEF_TXMESSAGEDATA_USERDATAUSEDOFPBCHANNELCONFIG        STD_ON
#define FRNM_ISDEF_TXUSERDATAPDUIDOFPBCHANNELCONFIG                   STD_ON
#define FRNM_ISDEF_SYSTONMCHIND                                       STD_OFF
#define FRNM_ISDEF_TXDATAPDUID                                        STD_ON
#define FRNM_ISDEF_CHANNELCONFIGOFPCCONFIG                            STD_ON
#define FRNM_ISDEF_CURRENTFRCYCLEOFPCCONFIG                           STD_ON
#define FRNM_ISDEF_LASTNETWORKREQUESTEDOFPCCONFIG                     STD_ON
#define FRNM_ISDEF_MODULEINITIALIZEDOFPCCONFIG                        STD_ON
#define FRNM_ISDEF_MSGCONFIRMATIONFLAGOFPCCONFIG                      STD_ON
#define FRNM_ISDEF_MSGINDICATIONFLAGOFPCCONFIG                        STD_ON
#define FRNM_ISDEF_NETWORKREQUESTEDOFPCCONFIG                         STD_ON
#define FRNM_ISDEF_NETWORKRESTARTFLAGOFPCCONFIG                       STD_ON
#define FRNM_ISDEF_NETWORKTIMEROFPCCONFIG                             STD_ON
#define FRNM_ISDEF_NMSTATEOFPCCONFIG                                  STD_ON
#define FRNM_ISDEF_PBCHANNELCONFIGOFPCCONFIG                          STD_ON
#define FRNM_ISDEF_RXMESSAGEDATAOFPCCONFIG                            STD_ON
#define FRNM_ISDEF_RXMESSAGEVOTEOFPCCONFIG                            STD_ON
#define FRNM_ISDEF_SYSTONMCHINDOFPCCONFIG                             STD_ON
#define FRNM_ISDEF_TIMEOUTTIMEROFPCCONFIG                             STD_ON
#define FRNM_ISDEF_TXDATAPDUIDOFPCCONFIG                              STD_ON
#define FRNM_ISDEF_TXMESSAGEDATAOFPCCONFIG                            STD_ON
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCEqualsAlwaysToDefines  FrNm Equals Always To Defines (PRE_COMPILE)
  \brief  If all values in a CONST array or an element in a CONST array of structs are equal, the define contains the always equals value.
  \{
*/ 
#define FRNM_EQ2_CHANNELHANDLEOFCHANNELCONFIG                         0u
#define FRNM_EQ2_CHANNELIDOFCHANNELCONFIG                             NmConf_NmChannelConfig_CN_FlexRay_oChannel_A_24bd889a
#define FRNM_EQ2_CONTROLBITVECTORACTIVEOFCHANNELCONFIG                TRUE
#define FRNM_EQ2_DATACYCLEMASKOFCHANNELCONFIG                         0x3Fu
#define FRNM_EQ2_PDUSCHEDULEVARIANTOFCHANNELCONFIG                    2u
#define FRNM_EQ2_READYSLEEPCNTOFCHANNELCONFIG                         2u
#define FRNM_EQ2_REPEATMESSAGETIMEOFCHANNELCONFIG                     80u
#define FRNM_EQ2_REPETITIONCYCLEOFCHANNELCONFIG                       64u
#define FRNM_EQ2_VOTINGCYCLEMASKOFCHANNELCONFIG                       0x3Fu
#define FRNM_EQ2_MSGTIMEOUTTIMEOFPBCHANNELCONFIG                      80u
#define FRNM_EQ2_NODEIDOFPBCHANNELCONFIG                              0u
#define FRNM_EQ2_NODEIDPOSITIONOFPBCHANNELCONFIG                      1u
#define FRNM_EQ2_RXMESSAGEDATAENDIDXOFPBCHANNELCONFIG                 8u
#define FRNM_EQ2_RXMESSAGEDATALENGTHOFPBCHANNELCONFIG                 8u
#define FRNM_EQ2_RXMESSAGEDATASTARTIDXOFPBCHANNELCONFIG               0u
#define FRNM_EQ2_RXMESSAGEDATAUSEDOFPBCHANNELCONFIG                   TRUE
#define FRNM_EQ2_RXMESSAGEDATA_CBVIDXOFPBCHANNELCONFIG                0u
#define FRNM_EQ2_RXMESSAGEDATA_CBVUSEDOFPBCHANNELCONFIG               TRUE
#define FRNM_EQ2_RXMESSAGEDATA_NIDIDXOFPBCHANNELCONFIG                1u
#define FRNM_EQ2_RXMESSAGEDATA_NIDUSEDOFPBCHANNELCONFIG               TRUE
#define FRNM_EQ2_RXMESSAGEDATA_USERDATAENDIDXOFPBCHANNELCONFIG        8u
#define FRNM_EQ2_RXMESSAGEDATA_USERDATALENGTHOFPBCHANNELCONFIG        6u
#define FRNM_EQ2_RXMESSAGEDATA_USERDATASTARTIDXOFPBCHANNELCONFIG      2u
#define FRNM_EQ2_RXMESSAGEDATA_USERDATAUSEDOFPBCHANNELCONFIG          TRUE
#define FRNM_EQ2_TXDATAPDUIDENDIDXOFPBCHANNELCONFIG                   1u
#define FRNM_EQ2_TXDATAPDUIDSTARTIDXOFPBCHANNELCONFIG                 0u
#define FRNM_EQ2_TXDATAPDUIDUSEDOFPBCHANNELCONFIG                     TRUE
#define FRNM_EQ2_TXMESSAGEDATAENDIDXOFPBCHANNELCONFIG                 8u
#define FRNM_EQ2_TXMESSAGEDATALENGTHOFPBCHANNELCONFIG                 8u
#define FRNM_EQ2_TXMESSAGEDATASTARTIDXOFPBCHANNELCONFIG               0u
#define FRNM_EQ2_TXMESSAGEDATAUSEDOFPBCHANNELCONFIG                   TRUE
#define FRNM_EQ2_TXMESSAGEDATA_CBVIDXOFPBCHANNELCONFIG                0u
#define FRNM_EQ2_TXMESSAGEDATA_CBVUSEDOFPBCHANNELCONFIG               TRUE
#define FRNM_EQ2_TXMESSAGEDATA_NIDIDXOFPBCHANNELCONFIG                1u
#define FRNM_EQ2_TXMESSAGEDATA_NIDUSEDOFPBCHANNELCONFIG               TRUE
#define FRNM_EQ2_TXMESSAGEDATA_USERDATAENDIDXOFPBCHANNELCONFIG        8u
#define FRNM_EQ2_TXMESSAGEDATA_USERDATALENGTHOFPBCHANNELCONFIG        6u
#define FRNM_EQ2_TXMESSAGEDATA_USERDATASTARTIDXOFPBCHANNELCONFIG      2u
#define FRNM_EQ2_TXMESSAGEDATA_USERDATAUSEDOFPBCHANNELCONFIG          TRUE
#define FRNM_EQ2_TXUSERDATAPDUIDOFPBCHANNELCONFIG                     PduRConf_PduRDestPdu_PDU_nm_MyECU_Fr_ae963333_Tx_c844becc_Tx
#define FRNM_EQ2_SYSTONMCHIND                                         
#define FRNM_EQ2_TXDATAPDUID                                          FrIfConf_FrIfTxPdu_PDU_nm_MyECU_Fr_50a02afb_Tx
#define FRNM_EQ2_CHANNELCONFIGOFPCCONFIG                              FrNm_ChannelConfig
#define FRNM_EQ2_CURRENTFRCYCLEOFPCCONFIG                             FrNm_CurrentFrCycle.raw
#define FRNM_EQ2_LASTNETWORKREQUESTEDOFPCCONFIG                       FrNm_LastNetworkRequested.raw
#define FRNM_EQ2_MODULEINITIALIZEDOFPCCONFIG                          (&(FrNm_ModuleInitialized))
#define FRNM_EQ2_MSGCONFIRMATIONFLAGOFPCCONFIG                        FrNm_MsgConfirmationFlag.raw
#define FRNM_EQ2_MSGINDICATIONFLAGOFPCCONFIG                          FrNm_MsgIndicationFlag.raw
#define FRNM_EQ2_NETWORKREQUESTEDOFPCCONFIG                           FrNm_NetworkRequested.raw
#define FRNM_EQ2_NETWORKRESTARTFLAGOFPCCONFIG                         FrNm_NetworkRestartFlag.raw
#define FRNM_EQ2_NETWORKTIMEROFPCCONFIG                               FrNm_NetworkTimer.raw
#define FRNM_EQ2_NMSTATEOFPCCONFIG                                    FrNm_NmState.raw
#define FRNM_EQ2_PBCHANNELCONFIGOFPCCONFIG                            FrNm_PbChannelConfig
#define FRNM_EQ2_RXMESSAGEDATAOFPCCONFIG                              FrNm_RxMessageData
#define FRNM_EQ2_RXMESSAGEVOTEOFPCCONFIG                              FrNm_RxMessageVote.raw
#define FRNM_EQ2_SYSTONMCHINDOFPCCONFIG                               FrNm_SysToNmChInd
#define FRNM_EQ2_TIMEOUTTIMEROFPCCONFIG                               FrNm_TimeoutTimer.raw
#define FRNM_EQ2_TXDATAPDUIDOFPCCONFIG                                FrNm_TxDataPduId
#define FRNM_EQ2_TXMESSAGEDATAOFPCCONFIG                              FrNm_TxMessageData
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCSymbolicInitializationPointers  FrNm Symbolic Initialization Pointers (PRE_COMPILE)
  \brief  Symbolic initialization pointers to be used in the call of a preinit or init function.
  \{
*/ 
#define FrNm_Config_Ptr                                               NULL_PTR  /**< symbolic identifier which shall be used to initialize 'FrNm' */
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCInitializationSymbols  FrNm Initialization Symbols (PRE_COMPILE)
  \brief  Symbolic initialization pointers which may be used in the call of a preinit or init function. Please note, that the defined value can be a 'NULL_PTR' and the address operator is not usable.
  \{
*/ 
#define FrNm_Config                                                   NULL_PTR  /**< symbolic identifier which could be used to initialize 'FrNm */
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCGeneral  FrNm General (PRE_COMPILE)
  \brief  General constant defines not associated with a group of defines.
  \{
*/ 
#define FRNM_CHECK_INIT_POINTER                                       STD_OFF  /**< STD_ON if the init pointer shall not be used as NULL_PTR and a check shall validate this. */
#define FRNM_FINAL_MAGIC_NUMBER                                       0x201Eu  /**< the precompile constant to validate the size of the initialization structure at initialization time of FrNm */
#define FRNM_INDIVIDUAL_POSTBUILD                                     STD_OFF  /**< the precompile constant to check, that the module is individual postbuildable. The module 'FrNm' is not configured to be postbuild capable. */
#define FRNM_INIT_DATA                                                FRNM_CONST  /**< CompilerMemClassDefine for the initialization data. */
#define FRNM_INIT_DATA_HASH_CODE                                      -1113964493  /**< the precompile constant to validate the initialization structure at initialization time of FrNm with a hashcode. The seed value is '0x201Eu' */
#define FRNM_USE_ECUM_BSW_ERROR_HOOK                                  STD_OFF  /**< STD_ON if the EcuM_BswErrorHook shall be called in the ConfigPtr check. */
#define FRNM_USE_INIT_POINTER                                         STD_OFF  /**< STD_ON if the init pointer FrNm shall be used. */
/** 
  \}
*/ 


/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: GLOBAL CONSTANT MACROS
**********************************************************************************************************************/
/** 
  \defgroup  FrNmLTDataSwitches  FrNm Data Switches  (LINK)
  \brief  These defines are used to deactivate data and their processing.
  \{
*/ 
#define FRNM_LTCONFIG                                                 STD_OFF  /**< Deactivateable: 'FrNm_LTConfig' Reason: 'the struct is deactivated because all elements are deactivated in all variants.' */
/** 
  \}
*/ 


/**********************************************************************************************************************
  CONFIGURATION CLASS: POST_BUILD
  SECTION: GLOBAL CONSTANT MACROS
**********************************************************************************************************************/
/** 
  \defgroup  FrNmPBDataSwitches  FrNm Data Switches  (POST_BUILD)
  \brief  These defines are used to deactivate data and their processing.
  \{
*/ 
#define FRNM_PBCONFIG                                                 STD_OFF  /**< Deactivateable: 'FrNm_PBConfig' Reason: 'the module configuration is VARIANT_PRE_COMPILE.' */
#define FRNM_LTCONFIGIDXOFPBCONFIG                                    STD_OFF  /**< Deactivateable: 'FrNm_PBConfig.LTConfigIdx' Reason: 'the module configuration is VARIANT_PRE_COMPILE.' */
#define FRNM_PCCONFIGIDXOFPBCONFIG                                    STD_OFF  /**< Deactivateable: 'FrNm_PBConfig.PCConfigIdx' Reason: 'the module configuration is VARIANT_PRE_COMPILE.' */
/** 
  \}
*/ 




/* PN Calculation */
#if ( FRNM_PN_EIRA_CALC_FEATURE_ENABLED == STD_ON ) || ( FRNM_PN_ERA_CALC_FEATURE_ENABLED == STD_ON )
# define FRNM_PN_CALC_FEATURE_ENABLED
# define FRNM_PN_CLUSTERS_PER_BYTE 8u
#endif

#if defined (FRNM_ISDEF_PNEIRACALCENABLED)
#else
# define FRNM_ISDEF_PNEIRACALCENABLED                     STD_ON
#endif

#if defined (FRNM_ISDEF_PNINFOLENGTH)
#else
# define FRNM_ISDEF_PNINFOLENGTH                          STD_ON
#endif

/**********************************************************************************************************************
 *  GLOBAL FUNCTION MACROS
 *********************************************************************************************************************/
/** 
  \defgroup  DataAccessMacros  Data Access Macros
  \brief  generated data access macros to abstract the generated data from the code to read and write CONST or VAR data.
  \{
*/ 
  /* PRQA S 3453 Macros_3453 */  /* MD_MSR_FctLikeMacro */
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL FUNCTION MACROS
**********************************************************************************************************************/
/** 
  \defgroup  FrNmPCGetConstantDuplicatedRootDataMacros  FrNm Get Constant Duplicated Root Data Macros (PRE_COMPILE)
  \brief  These macros can be used to read deduplicated by constance root data elements.
  \{
*/ 
#define FrNm_GetChannelConfigOfPCConfig()                             FrNm_ChannelConfig  /**< the pointer to FrNm_ChannelConfig */
#define FrNm_GetCurrentFrCycleOfPCConfig()                            FrNm_CurrentFrCycle.raw  /**< the pointer to FrNm_CurrentFrCycle */
#define FrNm_GetLastNetworkRequestedOfPCConfig()                      FrNm_LastNetworkRequested.raw  /**< the pointer to FrNm_LastNetworkRequested */
#define FrNm_IsMainAcrossFrCycleOfPCConfig()                          (((FALSE)) != FALSE)  /**< Execution of NM Task before (FALSE) or after (TRUE) Cycle Start */
#define FrNm_GetModuleInitializedOfPCConfig()                         (&(FrNm_ModuleInitialized))  /**< the pointer to FrNm_ModuleInitialized */
#define FrNm_GetMsgConfirmationFlagOfPCConfig()                       FrNm_MsgConfirmationFlag.raw  /**< the pointer to FrNm_MsgConfirmationFlag */
#define FrNm_GetMsgIndicationFlagOfPCConfig()                         FrNm_MsgIndicationFlag.raw  /**< the pointer to FrNm_MsgIndicationFlag */
#define FrNm_GetNetworkRequestedOfPCConfig()                          FrNm_NetworkRequested.raw  /**< the pointer to FrNm_NetworkRequested */
#define FrNm_GetNetworkRestartFlagOfPCConfig()                        FrNm_NetworkRestartFlag.raw  /**< the pointer to FrNm_NetworkRestartFlag */
#define FrNm_GetNetworkTimerOfPCConfig()                              FrNm_NetworkTimer.raw  /**< the pointer to FrNm_NetworkTimer */
#define FrNm_GetNmStateOfPCConfig()                                   FrNm_NmState.raw  /**< the pointer to FrNm_NmState */
#define FrNm_GetPbChannelConfigOfPCConfig()                           FrNm_PbChannelConfig  /**< the pointer to FrNm_PbChannelConfig */
#define FrNm_GetRxMessageDataOfPCConfig()                             FrNm_RxMessageData  /**< the pointer to FrNm_RxMessageData */
#define FrNm_GetRxMessageVoteOfPCConfig()                             FrNm_RxMessageVote.raw  /**< the pointer to FrNm_RxMessageVote */
#define FrNm_GetSizeOfChannelConfigOfPCConfig()                       1u  /**< the number of accomplishable value elements in FrNm_ChannelConfig */
#define FrNm_GetSizeOfPbChannelConfigOfPCConfig()                     1u  /**< the number of accomplishable value elements in FrNm_PbChannelConfig */
#define FrNm_GetSizeOfSysToNmChIndOfPCConfig()                        3u  /**< the number of accomplishable value elements in FrNm_SysToNmChInd */
#define FrNm_GetSizeOfTxMessageDataOfPCConfig()                       8u  /**< the number of accomplishable value elements in FrNm_TxMessageData */
#define FrNm_GetSysToNmChIndOfPCConfig()                              FrNm_SysToNmChInd  /**< the pointer to FrNm_SysToNmChInd */
#define FrNm_GetTimeoutTimerOfPCConfig()                              FrNm_TimeoutTimer.raw  /**< the pointer to FrNm_TimeoutTimer */
#define FrNm_GetTxDataPduIdOfPCConfig()                               FrNm_TxDataPduId  /**< the pointer to FrNm_TxDataPduId */
#define FrNm_GetTxMessageDataOfPCConfig()                             FrNm_TxMessageData  /**< the pointer to FrNm_TxMessageData */
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCGetDuplicatedRootDataMacros  FrNm Get Duplicated Root Data Macros (PRE_COMPILE)
  \brief  These macros can be used to read deduplicated root data elements.
  \{
*/ 
#define FrNm_GetSizeOfCurrentFrCycleOfPCConfig()                      FrNm_GetSizeOfChannelConfigOfPCConfig()  /**< the number of accomplishable value elements in FrNm_CurrentFrCycle */
#define FrNm_GetSizeOfLastNetworkRequestedOfPCConfig()                FrNm_GetSizeOfChannelConfigOfPCConfig()  /**< the number of accomplishable value elements in FrNm_LastNetworkRequested */
#define FrNm_GetSizeOfMsgConfirmationFlagOfPCConfig()                 FrNm_GetSizeOfChannelConfigOfPCConfig()  /**< the number of accomplishable value elements in FrNm_MsgConfirmationFlag */
#define FrNm_GetSizeOfMsgIndicationFlagOfPCConfig()                   FrNm_GetSizeOfChannelConfigOfPCConfig()  /**< the number of accomplishable value elements in FrNm_MsgIndicationFlag */
#define FrNm_GetSizeOfNetworkRequestedOfPCConfig()                    FrNm_GetSizeOfChannelConfigOfPCConfig()  /**< the number of accomplishable value elements in FrNm_NetworkRequested */
#define FrNm_GetSizeOfNetworkRestartFlagOfPCConfig()                  FrNm_GetSizeOfChannelConfigOfPCConfig()  /**< the number of accomplishable value elements in FrNm_NetworkRestartFlag */
#define FrNm_GetSizeOfNetworkTimerOfPCConfig()                        FrNm_GetSizeOfChannelConfigOfPCConfig()  /**< the number of accomplishable value elements in FrNm_NetworkTimer */
#define FrNm_GetSizeOfNmStateOfPCConfig()                             FrNm_GetSizeOfChannelConfigOfPCConfig()  /**< the number of accomplishable value elements in FrNm_NmState */
#define FrNm_GetSizeOfRxMessageVoteOfPCConfig()                       FrNm_GetSizeOfChannelConfigOfPCConfig()  /**< the number of accomplishable value elements in FrNm_RxMessageVote */
#define FrNm_GetSizeOfTimeoutTimerOfPCConfig()                        FrNm_GetSizeOfChannelConfigOfPCConfig()  /**< the number of accomplishable value elements in FrNm_TimeoutTimer */
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCGetDataMacros  FrNm Get Data Macros (PRE_COMPILE)
  \brief  These macros can be used to read CONST and VAR data.
  \{
*/ 
#define FrNm_GetCurrentFrCycle(Index)                                 (FrNm_GetCurrentFrCycleOfPCConfig()[(Index)])
#define FrNm_IsLastNetworkRequested(Index)                            ((FrNm_GetLastNetworkRequestedOfPCConfig()[(Index)]) != FALSE)
#define FrNm_IsModuleInitialized()                                    (((*(FrNm_GetModuleInitializedOfPCConfig()))) != FALSE)
#define FrNm_IsMsgConfirmationFlag(Index)                             ((FrNm_GetMsgConfirmationFlagOfPCConfig()[(Index)]) != FALSE)
#define FrNm_IsMsgIndicationFlag(Index)                               ((FrNm_GetMsgIndicationFlagOfPCConfig()[(Index)]) != FALSE)
#define FrNm_IsNetworkRequested(Index)                                ((FrNm_GetNetworkRequestedOfPCConfig()[(Index)]) != FALSE)
#define FrNm_GetNetworkRestartFlag(Index)                             (FrNm_GetNetworkRestartFlagOfPCConfig()[(Index)])
#define FrNm_GetNetworkTimer(Index)                                   (FrNm_GetNetworkTimerOfPCConfig()[(Index)])
#define FrNm_GetNmState(Index)                                        (FrNm_GetNmStateOfPCConfig()[(Index)])
#define FrNm_GetRxMessageData(Index)                                  (FrNm_GetRxMessageDataOfPCConfig()[(Index)])
#define FrNm_GetRxMessageVote(Index)                                  (FrNm_GetRxMessageVoteOfPCConfig()[(Index)])
#define FrNm_GetSysToNmChInd(Index)                                   (FrNm_GetSysToNmChIndOfPCConfig()[(Index)])
#define FrNm_GetTimeoutTimer(Index)                                   (FrNm_GetTimeoutTimerOfPCConfig()[(Index)])
#define FrNm_GetTxMessageData(Index)                                  (FrNm_GetTxMessageDataOfPCConfig()[(Index)])
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCGetDeduplicatedDataMacros  FrNm Get Deduplicated Data Macros (PRE_COMPILE)
  \brief  These macros can be used to read deduplicated data elements.
  \{
*/ 
#define FrNm_GetChannelHandleOfChannelConfig(Index)                   0u  /**< FR Cluster identifier configured for the respective FlexRay instance */
#define FrNm_GetChannelIdOfChannelConfig(Index)                       NmConf_NmChannelConfig_CN_FlexRay_oChannel_A_24bd889a  /**< NM Channel identifier configured for the respective FlexRay instance */
#define FrNm_IsControlBitVectorActiveOfChannelConfig(Index)           (((TRUE)) != FALSE)  /**< Determines whether the Control Bit Vector can be found inside the NM-Data PDU */
#define FrNm_GetDataCycleMaskOfChannelConfig(Index)                   0x3Fu  /**< Mask for the number of FlexRay Schedule Cycles needed to transmit the NM-Data of all ECUs on the FlexRay Bus. */
#define FrNm_GetPduScheduleVariantOfChannelConfig(Index)              2u  /**< Defines the PDU scheduling variant that should be used for this channel */
#define FrNm_GetReadySleepCntOfChannelConfig(Index)                   2u  /**< Numbers of repetitions in the ready sleep state before NM switches to bus sleep mode */
#define FrNm_GetRepeatMessageTimeOfChannelConfig(Index)               80u  /**< Timeout for Repeat Message State. Defines the time [task cycles] how long the NM shall stay in the Repeat Message State */
#define FrNm_GetRepetitionCycleOfChannelConfig(Index)                 64u  /**< Number of FlexRay Schedule Cycles used to repeat the transmission of the NM-Vote of all ECUs on the FlexRay Bus multiple times */
#define FrNm_GetVotingCycleMaskOfChannelConfig(Index)                 0x3Fu  /**< Mask for the number of FlexRay Schedule Cycles needed to transmit the NM-Vote of all ECUs on the FlexRay Bus */
#define FrNm_IsMainAcrossFrCycle()                                    FrNm_IsMainAcrossFrCycleOfPCConfig()
#define FrNm_GetMsgTimeoutTimeOfPbChannelConfig(Index)                80u  /**< Timeout time for the FlexRay NM PDU confirmation */
#define FrNm_GetNodeIdOfPbChannelConfig(Index)                        0u  /**< Node Identifier Byte */
#define FrNm_GetNodeIdPositionOfPbChannelConfig(Index)                1u
#define FrNm_GetRxMessageDataEndIdxOfPbChannelConfig(Index)           8u  /**< the end index of the 0:n relation pointing to FrNm_RxMessageData */
#define FrNm_GetRxMessageDataLengthOfPbChannelConfig(Index)           8u  /**< the number of relations pointing to FrNm_RxMessageData */
#define FrNm_GetRxMessageDataStartIdxOfPbChannelConfig(Index)         0u  /**< the start index of the 0:n relation pointing to FrNm_RxMessageData */
#define FrNm_IsRxMessageDataUsedOfPbChannelConfig(Index)              (((TRUE)) != FALSE)  /**< TRUE, if the 0:n relation has 1 relation pointing to FrNm_RxMessageData */
#define FrNm_GetRxMessageData_CBVIdxOfPbChannelConfig(Index)          0u  /**< the index of the 0:1 relation pointing to FrNm_RxMessageData */
#define FrNm_IsRxMessageData_CBVUsedOfPbChannelConfig(Index)          (((TRUE)) != FALSE)  /**< TRUE, if the 0:1 relation has minimum 1 relation pointing to FrNm_RxMessageData */
#define FrNm_GetRxMessageData_NIDIdxOfPbChannelConfig(Index)          1u  /**< the index of the 0:1 relation pointing to FrNm_RxMessageData */
#define FrNm_IsRxMessageData_NIDUsedOfPbChannelConfig(Index)          (((TRUE)) != FALSE)  /**< TRUE, if the 0:1 relation has minimum 1 relation pointing to FrNm_RxMessageData */
#define FrNm_GetRxMessageData_UserDataEndIdxOfPbChannelConfig(Index)  8u  /**< the end index of the 0:n relation pointing to FrNm_RxMessageData */
#define FrNm_GetRxMessageData_UserDataLengthOfPbChannelConfig(Index)  6u  /**< the number of relations pointing to FrNm_RxMessageData */
#define FrNm_GetRxMessageData_UserDataStartIdxOfPbChannelConfig(Index) 2u  /**< the start index of the 0:n relation pointing to FrNm_RxMessageData */
#define FrNm_IsRxMessageData_UserDataUsedOfPbChannelConfig(Index)     (((TRUE)) != FALSE)  /**< TRUE, if the 0:n relation has 1 relation pointing to FrNm_RxMessageData */
#define FrNm_GetTxDataPduIdEndIdxOfPbChannelConfig(Index)             1u  /**< the end index of the 0:n relation pointing to FrNm_TxDataPduId */
#define FrNm_GetTxDataPduIdStartIdxOfPbChannelConfig(Index)           0u  /**< the start index of the 0:n relation pointing to FrNm_TxDataPduId */
#define FrNm_IsTxDataPduIdUsedOfPbChannelConfig(Index)                (((TRUE)) != FALSE)  /**< TRUE, if the 0:n relation has 1 relation pointing to FrNm_TxDataPduId */
#define FrNm_GetTxMessageDataEndIdxOfPbChannelConfig(Index)           8u  /**< the end index of the 0:n relation pointing to FrNm_TxMessageData */
#define FrNm_GetTxMessageDataLengthOfPbChannelConfig(Index)           8u  /**< the number of relations pointing to FrNm_TxMessageData */
#define FrNm_GetTxMessageDataStartIdxOfPbChannelConfig(Index)         0u  /**< the start index of the 0:n relation pointing to FrNm_TxMessageData */
#define FrNm_IsTxMessageDataUsedOfPbChannelConfig(Index)              (((TRUE)) != FALSE)  /**< TRUE, if the 0:n relation has 1 relation pointing to FrNm_TxMessageData */
#define FrNm_GetTxMessageData_CBVIdxOfPbChannelConfig(Index)          0u  /**< the index of the 0:1 relation pointing to FrNm_TxMessageData */
#define FrNm_IsTxMessageData_CBVUsedOfPbChannelConfig(Index)          (((TRUE)) != FALSE)  /**< TRUE, if the 0:1 relation has minimum 1 relation pointing to FrNm_TxMessageData */
#define FrNm_GetTxMessageData_NIDIdxOfPbChannelConfig(Index)          1u  /**< the index of the 0:1 relation pointing to FrNm_TxMessageData */
#define FrNm_IsTxMessageData_NIDUsedOfPbChannelConfig(Index)          (((TRUE)) != FALSE)  /**< TRUE, if the 0:1 relation has minimum 1 relation pointing to FrNm_TxMessageData */
#define FrNm_GetTxMessageData_UserDataEndIdxOfPbChannelConfig(Index)  8u  /**< the end index of the 0:n relation pointing to FrNm_TxMessageData */
#define FrNm_GetTxMessageData_UserDataLengthOfPbChannelConfig(Index)  6u  /**< the number of relations pointing to FrNm_TxMessageData */
#define FrNm_GetTxMessageData_UserDataStartIdxOfPbChannelConfig(Index) 2u  /**< the start index of the 0:n relation pointing to FrNm_TxMessageData */
#define FrNm_IsTxMessageData_UserDataUsedOfPbChannelConfig(Index)     (((TRUE)) != FALSE)  /**< TRUE, if the 0:n relation has 1 relation pointing to FrNm_TxMessageData */
#define FrNm_GetTxUserDataPduIdOfPbChannelConfig(Index)               PduRConf_PduRDestPdu_PDU_nm_MyECU_Fr_ae963333_Tx_c844becc_Tx  /**< I-PDU handle for the user data PDU */
#define FrNm_GetSizeOfChannelConfig()                                 FrNm_GetSizeOfChannelConfigOfPCConfig()
#define FrNm_GetSizeOfCurrentFrCycle()                                FrNm_GetSizeOfCurrentFrCycleOfPCConfig()
#define FrNm_GetSizeOfLastNetworkRequested()                          FrNm_GetSizeOfLastNetworkRequestedOfPCConfig()
#define FrNm_GetSizeOfMsgConfirmationFlag()                           FrNm_GetSizeOfMsgConfirmationFlagOfPCConfig()
#define FrNm_GetSizeOfMsgIndicationFlag()                             FrNm_GetSizeOfMsgIndicationFlagOfPCConfig()
#define FrNm_GetSizeOfNetworkRequested()                              FrNm_GetSizeOfNetworkRequestedOfPCConfig()
#define FrNm_GetSizeOfNetworkRestartFlag()                            FrNm_GetSizeOfNetworkRestartFlagOfPCConfig()
#define FrNm_GetSizeOfNetworkTimer()                                  FrNm_GetSizeOfNetworkTimerOfPCConfig()
#define FrNm_GetSizeOfNmState()                                       FrNm_GetSizeOfNmStateOfPCConfig()
#define FrNm_GetSizeOfPbChannelConfig()                               FrNm_GetSizeOfPbChannelConfigOfPCConfig()
#define FrNm_GetSizeOfRxMessageVote()                                 FrNm_GetSizeOfRxMessageVoteOfPCConfig()
#define FrNm_GetSizeOfSysToNmChInd()                                  FrNm_GetSizeOfSysToNmChIndOfPCConfig()
#define FrNm_GetSizeOfTimeoutTimer()                                  FrNm_GetSizeOfTimeoutTimerOfPCConfig()
#define FrNm_GetSizeOfTxMessageData()                                 FrNm_GetSizeOfTxMessageDataOfPCConfig()
#define FrNm_GetTxDataPduId(Index)                                    FrIfConf_FrIfTxPdu_PDU_nm_MyECU_Fr_50a02afb_Tx  /**< PDU IDs for the FrNm TX Data PDUs */
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCSetDataMacros  FrNm Set Data Macros (PRE_COMPILE)
  \brief  These macros can be used to write data.
  \{
*/ 
#define FrNm_SetCurrentFrCycle(Index, Value)                          FrNm_GetCurrentFrCycleOfPCConfig()[(Index)] = (Value)
#define FrNm_SetLastNetworkRequested(Index, Value)                    FrNm_GetLastNetworkRequestedOfPCConfig()[(Index)] = (Value)
#define FrNm_SetModuleInitialized(Value)                              (*(FrNm_GetModuleInitializedOfPCConfig())) = (Value)
#define FrNm_SetMsgConfirmationFlag(Index, Value)                     FrNm_GetMsgConfirmationFlagOfPCConfig()[(Index)] = (Value)
#define FrNm_SetMsgIndicationFlag(Index, Value)                       FrNm_GetMsgIndicationFlagOfPCConfig()[(Index)] = (Value)
#define FrNm_SetNetworkRequested(Index, Value)                        FrNm_GetNetworkRequestedOfPCConfig()[(Index)] = (Value)
#define FrNm_SetNetworkRestartFlag(Index, Value)                      FrNm_GetNetworkRestartFlagOfPCConfig()[(Index)] = (Value)
#define FrNm_SetNetworkTimer(Index, Value)                            FrNm_GetNetworkTimerOfPCConfig()[(Index)] = (Value)
#define FrNm_SetNmState(Index, Value)                                 FrNm_GetNmStateOfPCConfig()[(Index)] = (Value)
#define FrNm_SetRxMessageData(Index, Value)                           FrNm_GetRxMessageDataOfPCConfig()[(Index)] = (Value)
#define FrNm_SetRxMessageVote(Index, Value)                           FrNm_GetRxMessageVoteOfPCConfig()[(Index)] = (Value)
#define FrNm_SetTimeoutTimer(Index, Value)                            FrNm_GetTimeoutTimerOfPCConfig()[(Index)] = (Value)
#define FrNm_SetTxMessageData(Index, Value)                           FrNm_GetTxMessageDataOfPCConfig()[(Index)] = (Value)
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCGetAddressOfDataMacros  FrNm Get Address Of Data Macros (PRE_COMPILE)
  \brief  These macros can be used to get the data by the address operator.
  \{
*/ 
#define FrNm_GetAddrCurrentFrCycle(Index)                             (&FrNm_GetCurrentFrCycle(Index))
#define FrNm_GetAddrNmState(Index)                                    (&FrNm_GetNmState(Index))
#define FrNm_GetAddrRxMessageData(Index)                              (&FrNm_GetRxMessageData(Index))
#define FrNm_GetAddrTxMessageData(Index)                              (&FrNm_GetTxMessageData(Index))
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCHasMacros  FrNm Has Macros (PRE_COMPILE)
  \brief  These macros can be used to detect at runtime a deactivated piece of information. TRUE in the CONFIGURATION_VARIANT PRE-COMPILE, TRUE or FALSE in the CONFIGURATION_VARIANT POST-BUILD.
  \{
*/ 
#define FrNm_HasChannelConfig()                                       (TRUE != FALSE)
#define FrNm_HasChannelHandleOfChannelConfig()                        (TRUE != FALSE)
#define FrNm_HasChannelIdOfChannelConfig()                            (TRUE != FALSE)
#define FrNm_HasControlBitVectorActiveOfChannelConfig()               (TRUE != FALSE)
#define FrNm_HasDataCycleMaskOfChannelConfig()                        (TRUE != FALSE)
#define FrNm_HasPduScheduleVariantOfChannelConfig()                   (TRUE != FALSE)
#define FrNm_HasReadySleepCntOfChannelConfig()                        (TRUE != FALSE)
#define FrNm_HasRepeatMessageTimeOfChannelConfig()                    (TRUE != FALSE)
#define FrNm_HasRepetitionCycleOfChannelConfig()                      (TRUE != FALSE)
#define FrNm_HasVotingCycleMaskOfChannelConfig()                      (TRUE != FALSE)
#define FrNm_HasCurrentFrCycle()                                      (TRUE != FALSE)
#define FrNm_HasLastNetworkRequested()                                (TRUE != FALSE)
#define FrNm_HasMainAcrossFrCycle()                                   (TRUE != FALSE)
#define FrNm_HasModuleInitialized()                                   (TRUE != FALSE)
#define FrNm_HasMsgConfirmationFlag()                                 (TRUE != FALSE)
#define FrNm_HasMsgIndicationFlag()                                   (TRUE != FALSE)
#define FrNm_HasNetworkRequested()                                    (TRUE != FALSE)
#define FrNm_HasNetworkRestartFlag()                                  (TRUE != FALSE)
#define FrNm_HasNetworkTimer()                                        (TRUE != FALSE)
#define FrNm_HasNmState()                                             (TRUE != FALSE)
#define FrNm_HasPbChannelConfig()                                     (TRUE != FALSE)
#define FrNm_HasMsgTimeoutTimeOfPbChannelConfig()                     (TRUE != FALSE)
#define FrNm_HasNodeIdOfPbChannelConfig()                             (TRUE != FALSE)
#define FrNm_HasNodeIdPositionOfPbChannelConfig()                     (TRUE != FALSE)
#define FrNm_HasRxMessageDataEndIdxOfPbChannelConfig()                (TRUE != FALSE)
#define FrNm_HasRxMessageDataLengthOfPbChannelConfig()                (TRUE != FALSE)
#define FrNm_HasRxMessageDataStartIdxOfPbChannelConfig()              (TRUE != FALSE)
#define FrNm_HasRxMessageDataUsedOfPbChannelConfig()                  (TRUE != FALSE)
#define FrNm_HasRxMessageData_CBVIdxOfPbChannelConfig()               (TRUE != FALSE)
#define FrNm_HasRxMessageData_CBVUsedOfPbChannelConfig()              (TRUE != FALSE)
#define FrNm_HasRxMessageData_NIDIdxOfPbChannelConfig()               (TRUE != FALSE)
#define FrNm_HasRxMessageData_NIDUsedOfPbChannelConfig()              (TRUE != FALSE)
#define FrNm_HasRxMessageData_UserDataEndIdxOfPbChannelConfig()       (TRUE != FALSE)
#define FrNm_HasRxMessageData_UserDataLengthOfPbChannelConfig()       (TRUE != FALSE)
#define FrNm_HasRxMessageData_UserDataStartIdxOfPbChannelConfig()     (TRUE != FALSE)
#define FrNm_HasRxMessageData_UserDataUsedOfPbChannelConfig()         (TRUE != FALSE)
#define FrNm_HasTxDataPduIdEndIdxOfPbChannelConfig()                  (TRUE != FALSE)
#define FrNm_HasTxDataPduIdStartIdxOfPbChannelConfig()                (TRUE != FALSE)
#define FrNm_HasTxDataPduIdUsedOfPbChannelConfig()                    (TRUE != FALSE)
#define FrNm_HasTxMessageDataEndIdxOfPbChannelConfig()                (TRUE != FALSE)
#define FrNm_HasTxMessageDataLengthOfPbChannelConfig()                (TRUE != FALSE)
#define FrNm_HasTxMessageDataStartIdxOfPbChannelConfig()              (TRUE != FALSE)
#define FrNm_HasTxMessageDataUsedOfPbChannelConfig()                  (TRUE != FALSE)
#define FrNm_HasTxMessageData_CBVIdxOfPbChannelConfig()               (TRUE != FALSE)
#define FrNm_HasTxMessageData_CBVUsedOfPbChannelConfig()              (TRUE != FALSE)
#define FrNm_HasTxMessageData_NIDIdxOfPbChannelConfig()               (TRUE != FALSE)
#define FrNm_HasTxMessageData_NIDUsedOfPbChannelConfig()              (TRUE != FALSE)
#define FrNm_HasTxMessageData_UserDataEndIdxOfPbChannelConfig()       (TRUE != FALSE)
#define FrNm_HasTxMessageData_UserDataLengthOfPbChannelConfig()       (TRUE != FALSE)
#define FrNm_HasTxMessageData_UserDataStartIdxOfPbChannelConfig()     (TRUE != FALSE)
#define FrNm_HasTxMessageData_UserDataUsedOfPbChannelConfig()         (TRUE != FALSE)
#define FrNm_HasTxUserDataPduIdOfPbChannelConfig()                    (TRUE != FALSE)
#define FrNm_HasRxMessageData()                                       (TRUE != FALSE)
#define FrNm_HasRxMessageVote()                                       (TRUE != FALSE)
#define FrNm_HasSizeOfChannelConfig()                                 (TRUE != FALSE)
#define FrNm_HasSizeOfCurrentFrCycle()                                (TRUE != FALSE)
#define FrNm_HasSizeOfLastNetworkRequested()                          (TRUE != FALSE)
#define FrNm_HasSizeOfMsgConfirmationFlag()                           (TRUE != FALSE)
#define FrNm_HasSizeOfMsgIndicationFlag()                             (TRUE != FALSE)
#define FrNm_HasSizeOfNetworkRequested()                              (TRUE != FALSE)
#define FrNm_HasSizeOfNetworkRestartFlag()                            (TRUE != FALSE)
#define FrNm_HasSizeOfNetworkTimer()                                  (TRUE != FALSE)
#define FrNm_HasSizeOfNmState()                                       (TRUE != FALSE)
#define FrNm_HasSizeOfPbChannelConfig()                               (TRUE != FALSE)
#define FrNm_HasSizeOfRxMessageVote()                                 (TRUE != FALSE)
#define FrNm_HasSizeOfSysToNmChInd()                                  (TRUE != FALSE)
#define FrNm_HasSizeOfTimeoutTimer()                                  (TRUE != FALSE)
#define FrNm_HasSizeOfTxMessageData()                                 (TRUE != FALSE)
#define FrNm_HasSysToNmChInd()                                        (TRUE != FALSE)
#define FrNm_HasTimeoutTimer()                                        (TRUE != FALSE)
#define FrNm_HasTxDataPduId()                                         (TRUE != FALSE)
#define FrNm_HasTxMessageData()                                       (TRUE != FALSE)
#define FrNm_HasPCConfig()                                            (TRUE != FALSE)
#define FrNm_HasChannelConfigOfPCConfig()                             (TRUE != FALSE)
#define FrNm_HasCurrentFrCycleOfPCConfig()                            (TRUE != FALSE)
#define FrNm_HasLastNetworkRequestedOfPCConfig()                      (TRUE != FALSE)
#define FrNm_HasMainAcrossFrCycleOfPCConfig()                         (TRUE != FALSE)
#define FrNm_HasModuleInitializedOfPCConfig()                         (TRUE != FALSE)
#define FrNm_HasMsgConfirmationFlagOfPCConfig()                       (TRUE != FALSE)
#define FrNm_HasMsgIndicationFlagOfPCConfig()                         (TRUE != FALSE)
#define FrNm_HasNetworkRequestedOfPCConfig()                          (TRUE != FALSE)
#define FrNm_HasNetworkRestartFlagOfPCConfig()                        (TRUE != FALSE)
#define FrNm_HasNetworkTimerOfPCConfig()                              (TRUE != FALSE)
#define FrNm_HasNmStateOfPCConfig()                                   (TRUE != FALSE)
#define FrNm_HasPbChannelConfigOfPCConfig()                           (TRUE != FALSE)
#define FrNm_HasRxMessageDataOfPCConfig()                             (TRUE != FALSE)
#define FrNm_HasRxMessageVoteOfPCConfig()                             (TRUE != FALSE)
#define FrNm_HasSizeOfChannelConfigOfPCConfig()                       (TRUE != FALSE)
#define FrNm_HasSizeOfCurrentFrCycleOfPCConfig()                      (TRUE != FALSE)
#define FrNm_HasSizeOfLastNetworkRequestedOfPCConfig()                (TRUE != FALSE)
#define FrNm_HasSizeOfMsgConfirmationFlagOfPCConfig()                 (TRUE != FALSE)
#define FrNm_HasSizeOfMsgIndicationFlagOfPCConfig()                   (TRUE != FALSE)
#define FrNm_HasSizeOfNetworkRequestedOfPCConfig()                    (TRUE != FALSE)
#define FrNm_HasSizeOfNetworkRestartFlagOfPCConfig()                  (TRUE != FALSE)
#define FrNm_HasSizeOfNetworkTimerOfPCConfig()                        (TRUE != FALSE)
#define FrNm_HasSizeOfNmStateOfPCConfig()                             (TRUE != FALSE)
#define FrNm_HasSizeOfPbChannelConfigOfPCConfig()                     (TRUE != FALSE)
#define FrNm_HasSizeOfRxMessageVoteOfPCConfig()                       (TRUE != FALSE)
#define FrNm_HasSizeOfSysToNmChIndOfPCConfig()                        (TRUE != FALSE)
#define FrNm_HasSizeOfTimeoutTimerOfPCConfig()                        (TRUE != FALSE)
#define FrNm_HasSizeOfTxMessageDataOfPCConfig()                       (TRUE != FALSE)
#define FrNm_HasSysToNmChIndOfPCConfig()                              (TRUE != FALSE)
#define FrNm_HasTimeoutTimerOfPCConfig()                              (TRUE != FALSE)
#define FrNm_HasTxDataPduIdOfPCConfig()                               (TRUE != FALSE)
#define FrNm_HasTxMessageDataOfPCConfig()                             (TRUE != FALSE)
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCIncrementDataMacros  FrNm Increment Data Macros (PRE_COMPILE)
  \brief  These macros can be used to increment VAR data with numerical nature.
  \{
*/ 
#define FrNm_IncCurrentFrCycle(Index)                                 FrNm_GetCurrentFrCycle(Index)++
#define FrNm_IncNetworkRestartFlag(Index)                             FrNm_GetNetworkRestartFlag(Index)++
#define FrNm_IncNetworkTimer(Index)                                   FrNm_GetNetworkTimer(Index)++
#define FrNm_IncNmState(Index)                                        FrNm_GetNmState(Index)++
#define FrNm_IncRxMessageData(Index)                                  FrNm_GetRxMessageData(Index)++
#define FrNm_IncRxMessageVote(Index)                                  FrNm_GetRxMessageVote(Index)++
#define FrNm_IncTimeoutTimer(Index)                                   FrNm_GetTimeoutTimer(Index)++
#define FrNm_IncTxMessageData(Index)                                  FrNm_GetTxMessageData(Index)++
/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCDecrementDataMacros  FrNm Decrement Data Macros (PRE_COMPILE)
  \brief  These macros can be used to decrement VAR data with numerical nature.
  \{
*/ 
#define FrNm_DecCurrentFrCycle(Index)                                 FrNm_GetCurrentFrCycle(Index)--
#define FrNm_DecNetworkRestartFlag(Index)                             FrNm_GetNetworkRestartFlag(Index)--
#define FrNm_DecNetworkTimer(Index)                                   FrNm_GetNetworkTimer(Index)--
#define FrNm_DecNmState(Index)                                        FrNm_GetNmState(Index)--
#define FrNm_DecRxMessageData(Index)                                  FrNm_GetRxMessageData(Index)--
#define FrNm_DecRxMessageVote(Index)                                  FrNm_GetRxMessageVote(Index)--
#define FrNm_DecTimeoutTimer(Index)                                   FrNm_GetTimeoutTimer(Index)--
#define FrNm_DecTxMessageData(Index)                                  FrNm_GetTxMessageData(Index)--
/** 
  \}
*/ 

  /* PRQA L:Macros_3453 */
/** 
  \}
*/ 

/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL ACCESS FUNCTION MACROS
**********************************************************************************************************************/

/** 
  \defgroup  DataAccessMacros  Data Access Macros
  \brief  generated data access macros to abstract the generated data from the code to read and write CONST or VAR data.
  \{
*/ 
  /* PRQA S 3453 Macros_3453 */  /* MD_MSR_FctLikeMacro */
/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: GLOBAL FUNCTION MACROS
**********************************************************************************************************************/
  /* PRQA L:Macros_3453 */
/** 
  \}
*/ 

/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: GLOBAL ACCESS FUNCTION MACROS
**********************************************************************************************************************/

/** 
  \defgroup  DataAccessMacros  Data Access Macros
  \brief  generated data access macros to abstract the generated data from the code to read and write CONST or VAR data.
  \{
*/ 
  /* PRQA S 3453 Macros_3453 */  /* MD_MSR_FctLikeMacro */
/**********************************************************************************************************************
  CONFIGURATION CLASS: POST_BUILD
  SECTION: GLOBAL FUNCTION MACROS
**********************************************************************************************************************/
  /* PRQA L:Macros_3453 */
/** 
  \}
*/ 

/**********************************************************************************************************************
  CONFIGURATION CLASS: POST_BUILD
  SECTION: GLOBAL ACCESS FUNCTION MACROS
**********************************************************************************************************************/


/**********************************************************************************************************************
 *  GLOBAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL SIMPLE DATA TYPES AND STRUCTURES
**********************************************************************************************************************/
/** 
  \defgroup  FrNmPCIterableTypes  FrNm Iterable Types (PRE_COMPILE)
  \brief  These type definitions are used to iterate over an array with least processor cycles for variable access as possible.
  \{
*/ 
/**   \brief  type used to iterate FrNm_ChannelConfig */
typedef uint8_least FrNm_ChannelConfigIterType;

/**   \brief  type used to iterate FrNm_PbChannelConfig */
typedef uint8_least FrNm_PbChannelConfigIterType;

/**   \brief  type used to iterate FrNm_RxMessageData */
typedef uint8_least FrNm_RxMessageDataIterType;

/**   \brief  type used to iterate FrNm_SysToNmChInd */
typedef uint8_least FrNm_SysToNmChIndIterType;

/**   \brief  type used to iterate FrNm_TxDataPduId */
typedef uint8_least FrNm_TxDataPduIdIterType;

/**   \brief  type used to iterate FrNm_TxMessageData */
typedef uint8_least FrNm_TxMessageDataIterType;

/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCIterableTypesWithSizeRelations  FrNm Iterable Types With Size Relations (PRE_COMPILE)
  \brief  These type definitions are used to iterate over a VAR based array with the same iterator as the related CONST array.
  \{
*/ 
/**   \brief  type used to iterate FrNm_CurrentFrCycle */
typedef FrNm_ChannelConfigIterType FrNm_CurrentFrCycleIterType;

/**   \brief  type used to iterate FrNm_LastNetworkRequested */
typedef FrNm_ChannelConfigIterType FrNm_LastNetworkRequestedIterType;

/**   \brief  type used to iterate FrNm_MsgConfirmationFlag */
typedef FrNm_ChannelConfigIterType FrNm_MsgConfirmationFlagIterType;

/**   \brief  type used to iterate FrNm_MsgIndicationFlag */
typedef FrNm_ChannelConfigIterType FrNm_MsgIndicationFlagIterType;

/**   \brief  type used to iterate FrNm_NetworkRequested */
typedef FrNm_ChannelConfigIterType FrNm_NetworkRequestedIterType;

/**   \brief  type used to iterate FrNm_NetworkRestartFlag */
typedef FrNm_ChannelConfigIterType FrNm_NetworkRestartFlagIterType;

/**   \brief  type used to iterate FrNm_NetworkTimer */
typedef FrNm_ChannelConfigIterType FrNm_NetworkTimerIterType;

/**   \brief  type used to iterate FrNm_NmState */
typedef FrNm_ChannelConfigIterType FrNm_NmStateIterType;

/**   \brief  type used to iterate FrNm_RxMessageVote */
typedef FrNm_ChannelConfigIterType FrNm_RxMessageVoteIterType;

/**   \brief  type used to iterate FrNm_TimeoutTimer */
typedef FrNm_ChannelConfigIterType FrNm_TimeoutTimerIterType;

/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCValueTypes  FrNm Value Types (PRE_COMPILE)
  \brief  These type definitions are used for value based data representations.
  \{
*/ 
/**   \brief  value based type definition for FrNm_ChannelHandleOfChannelConfig */
typedef uint8 FrNm_ChannelHandleOfChannelConfigType;

/**   \brief  value based type definition for FrNm_ChannelIdOfChannelConfig */
typedef uint8 FrNm_ChannelIdOfChannelConfigType;

/**   \brief  value based type definition for FrNm_ControlBitVectorActiveOfChannelConfig */
typedef boolean FrNm_ControlBitVectorActiveOfChannelConfigType;

/**   \brief  value based type definition for FrNm_DataCycleMaskOfChannelConfig */
typedef uint8 FrNm_DataCycleMaskOfChannelConfigType;

/**   \brief  value based type definition for FrNm_PduScheduleVariantOfChannelConfig */
typedef uint8 FrNm_PduScheduleVariantOfChannelConfigType;

/**   \brief  value based type definition for FrNm_ReadySleepCntOfChannelConfig */
typedef uint8 FrNm_ReadySleepCntOfChannelConfigType;

/**   \brief  value based type definition for FrNm_RepeatMessageTimeOfChannelConfig */
typedef uint8 FrNm_RepeatMessageTimeOfChannelConfigType;

/**   \brief  value based type definition for FrNm_RepetitionCycleOfChannelConfig */
typedef uint8 FrNm_RepetitionCycleOfChannelConfigType;

/**   \brief  value based type definition for FrNm_VotingCycleMaskOfChannelConfig */
typedef uint8 FrNm_VotingCycleMaskOfChannelConfigType;

/**   \brief  value based type definition for FrNm_CurrentFrCycle */
typedef uint8 FrNm_CurrentFrCycleType;

/**   \brief  value based type definition for FrNm_LastNetworkRequested */
typedef boolean FrNm_LastNetworkRequestedType;

/**   \brief  value based type definition for FrNm_MainAcrossFrCycle */
typedef boolean FrNm_MainAcrossFrCycleType;

/**   \brief  value based type definition for FrNm_ModuleInitialized */
typedef boolean FrNm_ModuleInitializedType;

/**   \brief  value based type definition for FrNm_MsgConfirmationFlag */
typedef boolean FrNm_MsgConfirmationFlagType;

/**   \brief  value based type definition for FrNm_MsgIndicationFlag */
typedef boolean FrNm_MsgIndicationFlagType;

/**   \brief  value based type definition for FrNm_NetworkRequested */
typedef boolean FrNm_NetworkRequestedType;

/**   \brief  value based type definition for FrNm_NetworkRestartFlag */
typedef uint8 FrNm_NetworkRestartFlagType;

/**   \brief  value based type definition for FrNm_NetworkTimer */
typedef uint16 FrNm_NetworkTimerType;

/**   \brief  value based type definition for FrNm_NmState */
typedef uint8 FrNm_NmStateType;

/**   \brief  value based type definition for FrNm_MsgTimeoutTimeOfPbChannelConfig */
typedef uint8 FrNm_MsgTimeoutTimeOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_NodeIdOfPbChannelConfig */
typedef uint8 FrNm_NodeIdOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_NodeIdPositionOfPbChannelConfig */
typedef uint8 FrNm_NodeIdPositionOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_RxMessageDataEndIdxOfPbChannelConfig */
typedef uint8 FrNm_RxMessageDataEndIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_RxMessageDataLengthOfPbChannelConfig */
typedef uint8 FrNm_RxMessageDataLengthOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_RxMessageDataStartIdxOfPbChannelConfig */
typedef uint8 FrNm_RxMessageDataStartIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_RxMessageDataUsedOfPbChannelConfig */
typedef boolean FrNm_RxMessageDataUsedOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_RxMessageData_CBVIdxOfPbChannelConfig */
typedef uint8 FrNm_RxMessageData_CBVIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_RxMessageData_CBVUsedOfPbChannelConfig */
typedef boolean FrNm_RxMessageData_CBVUsedOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_RxMessageData_NIDIdxOfPbChannelConfig */
typedef uint8 FrNm_RxMessageData_NIDIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_RxMessageData_NIDUsedOfPbChannelConfig */
typedef boolean FrNm_RxMessageData_NIDUsedOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_RxMessageData_UserDataEndIdxOfPbChannelConfig */
typedef uint8 FrNm_RxMessageData_UserDataEndIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_RxMessageData_UserDataLengthOfPbChannelConfig */
typedef uint8 FrNm_RxMessageData_UserDataLengthOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_RxMessageData_UserDataStartIdxOfPbChannelConfig */
typedef uint8 FrNm_RxMessageData_UserDataStartIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_RxMessageData_UserDataUsedOfPbChannelConfig */
typedef boolean FrNm_RxMessageData_UserDataUsedOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxDataPduIdEndIdxOfPbChannelConfig */
typedef uint8 FrNm_TxDataPduIdEndIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxDataPduIdStartIdxOfPbChannelConfig */
typedef uint8 FrNm_TxDataPduIdStartIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxDataPduIdUsedOfPbChannelConfig */
typedef boolean FrNm_TxDataPduIdUsedOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxMessageDataEndIdxOfPbChannelConfig */
typedef uint8 FrNm_TxMessageDataEndIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxMessageDataLengthOfPbChannelConfig */
typedef uint8 FrNm_TxMessageDataLengthOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxMessageDataStartIdxOfPbChannelConfig */
typedef uint8 FrNm_TxMessageDataStartIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxMessageDataUsedOfPbChannelConfig */
typedef boolean FrNm_TxMessageDataUsedOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxMessageData_CBVIdxOfPbChannelConfig */
typedef uint8 FrNm_TxMessageData_CBVIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxMessageData_CBVUsedOfPbChannelConfig */
typedef boolean FrNm_TxMessageData_CBVUsedOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxMessageData_NIDIdxOfPbChannelConfig */
typedef uint8 FrNm_TxMessageData_NIDIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxMessageData_NIDUsedOfPbChannelConfig */
typedef boolean FrNm_TxMessageData_NIDUsedOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxMessageData_UserDataEndIdxOfPbChannelConfig */
typedef uint8 FrNm_TxMessageData_UserDataEndIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxMessageData_UserDataLengthOfPbChannelConfig */
typedef uint8 FrNm_TxMessageData_UserDataLengthOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxMessageData_UserDataStartIdxOfPbChannelConfig */
typedef uint8 FrNm_TxMessageData_UserDataStartIdxOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxMessageData_UserDataUsedOfPbChannelConfig */
typedef boolean FrNm_TxMessageData_UserDataUsedOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_TxUserDataPduIdOfPbChannelConfig */
typedef uint8 FrNm_TxUserDataPduIdOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_RxMessageData */
typedef uint8 FrNm_RxMessageDataType;

/**   \brief  value based type definition for FrNm_RxMessageVote */
typedef uint8 FrNm_RxMessageVoteType;

/**   \brief  value based type definition for FrNm_SizeOfChannelConfig */
typedef uint8 FrNm_SizeOfChannelConfigType;

/**   \brief  value based type definition for FrNm_SizeOfCurrentFrCycle */
typedef uint8 FrNm_SizeOfCurrentFrCycleType;

/**   \brief  value based type definition for FrNm_SizeOfLastNetworkRequested */
typedef uint8 FrNm_SizeOfLastNetworkRequestedType;

/**   \brief  value based type definition for FrNm_SizeOfMsgConfirmationFlag */
typedef uint8 FrNm_SizeOfMsgConfirmationFlagType;

/**   \brief  value based type definition for FrNm_SizeOfMsgIndicationFlag */
typedef uint8 FrNm_SizeOfMsgIndicationFlagType;

/**   \brief  value based type definition for FrNm_SizeOfNetworkRequested */
typedef uint8 FrNm_SizeOfNetworkRequestedType;

/**   \brief  value based type definition for FrNm_SizeOfNetworkRestartFlag */
typedef uint8 FrNm_SizeOfNetworkRestartFlagType;

/**   \brief  value based type definition for FrNm_SizeOfNetworkTimer */
typedef uint8 FrNm_SizeOfNetworkTimerType;

/**   \brief  value based type definition for FrNm_SizeOfNmState */
typedef uint8 FrNm_SizeOfNmStateType;

/**   \brief  value based type definition for FrNm_SizeOfPbChannelConfig */
typedef uint8 FrNm_SizeOfPbChannelConfigType;

/**   \brief  value based type definition for FrNm_SizeOfRxMessageVote */
typedef uint8 FrNm_SizeOfRxMessageVoteType;

/**   \brief  value based type definition for FrNm_SizeOfSysToNmChInd */
typedef uint8 FrNm_SizeOfSysToNmChIndType;

/**   \brief  value based type definition for FrNm_SizeOfTimeoutTimer */
typedef uint8 FrNm_SizeOfTimeoutTimerType;

/**   \brief  value based type definition for FrNm_SizeOfTxMessageData */
typedef uint8 FrNm_SizeOfTxMessageDataType;

/**   \brief  value based type definition for FrNm_SysToNmChInd */
typedef NetworkHandleType FrNm_SysToNmChIndType;

/**   \brief  value based type definition for FrNm_TimeoutTimer */
typedef uint16 FrNm_TimeoutTimerType;

/**   \brief  value based type definition for FrNm_TxDataPduId */
typedef uint8 FrNm_TxDataPduIdType;

/**   \brief  value based type definition for FrNm_TxMessageData */
typedef uint8 FrNm_TxMessageDataType;

/** 
  \}
*/ 

/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL COMPLEX DATA TYPES AND STRUCTURES
**********************************************************************************************************************/
/** 
  \defgroup  FrNmPCStructTypes  FrNm Struct Types (PRE_COMPILE)
  \brief  These type definitions are used for structured data representations.
  \{
*/ 
/**   \brief  type used in FrNm_ChannelConfig */
typedef struct sFrNm_ChannelConfigType
{
  uint8 FrNm_ChannelConfigNeverUsed;  /**< dummy entry for the structure in the configuration variant precompile which is not used by the code. */
} FrNm_ChannelConfigType;

/**   \brief  type used in FrNm_PbChannelConfig */
typedef struct sFrNm_PbChannelConfigType
{
  uint8 FrNm_PbChannelConfigNeverUsed;  /**< dummy entry for the structure in the configuration variant precompile which is not used by the code. */
} FrNm_PbChannelConfigType;

/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCSymbolicStructTypes  FrNm Symbolic Struct Types (PRE_COMPILE)
  \brief  These structs are used in unions to have a symbol based data representation style.
  \{
*/ 
/**   \brief  type to be used as symbolic data element access to FrNm_CurrentFrCycle */
typedef struct FrNm_CurrentFrCycleStructSTag
{
  FrNm_CurrentFrCycleType CN_FlexRay_oChannel_A_24bd889a;
} FrNm_CurrentFrCycleStructSType;

/**   \brief  type to be used as symbolic data element access to FrNm_LastNetworkRequested */
typedef struct FrNm_LastNetworkRequestedStructSTag
{
  FrNm_LastNetworkRequestedType CN_FlexRay_oChannel_A_24bd889a;
} FrNm_LastNetworkRequestedStructSType;

/**   \brief  type to be used as symbolic data element access to FrNm_MsgConfirmationFlag */
typedef struct FrNm_MsgConfirmationFlagStructSTag
{
  FrNm_MsgConfirmationFlagType CN_FlexRay_oChannel_A_24bd889a;
} FrNm_MsgConfirmationFlagStructSType;

/**   \brief  type to be used as symbolic data element access to FrNm_MsgIndicationFlag */
typedef struct FrNm_MsgIndicationFlagStructSTag
{
  FrNm_MsgIndicationFlagType CN_FlexRay_oChannel_A_24bd889a;
} FrNm_MsgIndicationFlagStructSType;

/**   \brief  type to be used as symbolic data element access to FrNm_NetworkRequested */
typedef struct FrNm_NetworkRequestedStructSTag
{
  FrNm_NetworkRequestedType CN_FlexRay_oChannel_A_24bd889a;
} FrNm_NetworkRequestedStructSType;

/**   \brief  type to be used as symbolic data element access to FrNm_NetworkRestartFlag */
typedef struct FrNm_NetworkRestartFlagStructSTag
{
  FrNm_NetworkRestartFlagType CN_FlexRay_oChannel_A_24bd889a;
} FrNm_NetworkRestartFlagStructSType;

/**   \brief  type to be used as symbolic data element access to FrNm_NetworkTimer */
typedef struct FrNm_NetworkTimerStructSTag
{
  FrNm_NetworkTimerType CN_FlexRay_oChannel_A_24bd889a;
} FrNm_NetworkTimerStructSType;

/**   \brief  type to be used as symbolic data element access to FrNm_NmState */
typedef struct FrNm_NmStateStructSTag
{
  FrNm_NmStateType CN_FlexRay_oChannel_A_24bd889a;
} FrNm_NmStateStructSType;

/**   \brief  type to be used as symbolic data element access to FrNm_RxMessageVote */
typedef struct FrNm_RxMessageVoteStructSTag
{
  FrNm_RxMessageVoteType CN_FlexRay_oChannel_A_24bd889a;
} FrNm_RxMessageVoteStructSType;

/**   \brief  type to be used as symbolic data element access to FrNm_TimeoutTimer */
typedef struct FrNm_TimeoutTimerStructSTag
{
  FrNm_TimeoutTimerType CN_FlexRay_oChannel_A_24bd889a;
} FrNm_TimeoutTimerStructSType;

/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCUnionIndexAndSymbolTypes  FrNm Union Index And Symbol Types (PRE_COMPILE)
  \brief  These unions are used to access arrays in an index and symbol based style.
  \{
*/ 
/**   \brief  type to access FrNm_CurrentFrCycle in an index and symbol based style. */
typedef union FrNm_CurrentFrCycleUTag
{  /* PRQA S 0750 */  /* MD_CSL_Union */
  FrNm_CurrentFrCycleType raw[1];
  FrNm_CurrentFrCycleStructSType str;
} FrNm_CurrentFrCycleUType;

/**   \brief  type to access FrNm_LastNetworkRequested in an index and symbol based style. */
typedef union FrNm_LastNetworkRequestedUTag
{  /* PRQA S 0750 */  /* MD_CSL_Union */
  FrNm_LastNetworkRequestedType raw[1];
  FrNm_LastNetworkRequestedStructSType str;
} FrNm_LastNetworkRequestedUType;

/**   \brief  type to access FrNm_MsgConfirmationFlag in an index and symbol based style. */
typedef union FrNm_MsgConfirmationFlagUTag
{  /* PRQA S 0750 */  /* MD_CSL_Union */
  FrNm_MsgConfirmationFlagType raw[1];
  FrNm_MsgConfirmationFlagStructSType str;
} FrNm_MsgConfirmationFlagUType;

/**   \brief  type to access FrNm_MsgIndicationFlag in an index and symbol based style. */
typedef union FrNm_MsgIndicationFlagUTag
{  /* PRQA S 0750 */  /* MD_CSL_Union */
  FrNm_MsgIndicationFlagType raw[1];
  FrNm_MsgIndicationFlagStructSType str;
} FrNm_MsgIndicationFlagUType;

/**   \brief  type to access FrNm_NetworkRequested in an index and symbol based style. */
typedef union FrNm_NetworkRequestedUTag
{  /* PRQA S 0750 */  /* MD_CSL_Union */
  FrNm_NetworkRequestedType raw[1];
  FrNm_NetworkRequestedStructSType str;
} FrNm_NetworkRequestedUType;

/**   \brief  type to access FrNm_NetworkRestartFlag in an index and symbol based style. */
typedef union FrNm_NetworkRestartFlagUTag
{  /* PRQA S 0750 */  /* MD_CSL_Union */
  FrNm_NetworkRestartFlagType raw[1];
  FrNm_NetworkRestartFlagStructSType str;
} FrNm_NetworkRestartFlagUType;

/**   \brief  type to access FrNm_NetworkTimer in an index and symbol based style. */
typedef union FrNm_NetworkTimerUTag
{  /* PRQA S 0750 */  /* MD_CSL_Union */
  FrNm_NetworkTimerType raw[1];
  FrNm_NetworkTimerStructSType str;
} FrNm_NetworkTimerUType;

/**   \brief  type to access FrNm_NmState in an index and symbol based style. */
typedef union FrNm_NmStateUTag
{  /* PRQA S 0750 */  /* MD_CSL_Union */
  FrNm_NmStateType raw[1];
  FrNm_NmStateStructSType str;
} FrNm_NmStateUType;

/**   \brief  type to access FrNm_RxMessageVote in an index and symbol based style. */
typedef union FrNm_RxMessageVoteUTag
{  /* PRQA S 0750 */  /* MD_CSL_Union */
  FrNm_RxMessageVoteType raw[1];
  FrNm_RxMessageVoteStructSType str;
} FrNm_RxMessageVoteUType;

/**   \brief  type to access FrNm_TimeoutTimer in an index and symbol based style. */
typedef union FrNm_TimeoutTimerUTag
{  /* PRQA S 0750 */  /* MD_CSL_Union */
  FrNm_TimeoutTimerType raw[1];
  FrNm_TimeoutTimerStructSType str;
} FrNm_TimeoutTimerUType;

/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCRootPointerTypes  FrNm Root Pointer Types (PRE_COMPILE)
  \brief  These type definitions are used to point from the config root to symbol instances.
  \{
*/ 
/**   \brief  type used to point to FrNm_ChannelConfig */
typedef P2CONST(FrNm_ChannelConfigType, TYPEDEF, FRNM_CONST) FrNm_ChannelConfigPtrType;

/**   \brief  type used to point to FrNm_CurrentFrCycle */
typedef P2VAR(FrNm_CurrentFrCycleType, TYPEDEF, FRNM_VAR_NOINIT) FrNm_CurrentFrCyclePtrType;

/**   \brief  type used to point to FrNm_LastNetworkRequested */
typedef P2VAR(FrNm_LastNetworkRequestedType, TYPEDEF, FRNM_VAR_NOINIT) FrNm_LastNetworkRequestedPtrType;

/**   \brief  type used to point to FrNm_ModuleInitialized */
typedef P2VAR(FrNm_ModuleInitializedType, TYPEDEF, FRNM_VAR_ZERO_INIT) FrNm_ModuleInitializedPtrType;

/**   \brief  type used to point to FrNm_MsgConfirmationFlag */
typedef P2VAR(FrNm_MsgConfirmationFlagType, TYPEDEF, FRNM_VAR_NOINIT) FrNm_MsgConfirmationFlagPtrType;

/**   \brief  type used to point to FrNm_MsgIndicationFlag */
typedef P2VAR(FrNm_MsgIndicationFlagType, TYPEDEF, FRNM_VAR_NOINIT) FrNm_MsgIndicationFlagPtrType;

/**   \brief  type used to point to FrNm_NetworkRequested */
typedef P2VAR(FrNm_NetworkRequestedType, TYPEDEF, FRNM_VAR_NOINIT) FrNm_NetworkRequestedPtrType;

/**   \brief  type used to point to FrNm_NetworkRestartFlag */
typedef P2VAR(FrNm_NetworkRestartFlagType, TYPEDEF, FRNM_VAR_NOINIT) FrNm_NetworkRestartFlagPtrType;

/**   \brief  type used to point to FrNm_NetworkTimer */
typedef P2VAR(FrNm_NetworkTimerType, TYPEDEF, FRNM_VAR_NOINIT) FrNm_NetworkTimerPtrType;

/**   \brief  type used to point to FrNm_NmState */
typedef P2VAR(FrNm_NmStateType, TYPEDEF, FRNM_VAR_NOINIT) FrNm_NmStatePtrType;

/**   \brief  type used to point to FrNm_PbChannelConfig */
typedef P2CONST(FrNm_PbChannelConfigType, TYPEDEF, FRNM_CONST) FrNm_PbChannelConfigPtrType;

/**   \brief  type used to point to FrNm_RxMessageData */
typedef P2VAR(FrNm_RxMessageDataType, TYPEDEF, FRNM_VAR_NOINIT) FrNm_RxMessageDataPtrType;

/**   \brief  type used to point to FrNm_RxMessageVote */
typedef P2VAR(FrNm_RxMessageVoteType, TYPEDEF, FRNM_VAR_NOINIT) FrNm_RxMessageVotePtrType;

/**   \brief  type used to point to FrNm_SysToNmChInd */
typedef P2CONST(FrNm_SysToNmChIndType, TYPEDEF, FRNM_CONST) FrNm_SysToNmChIndPtrType;

/**   \brief  type used to point to FrNm_TimeoutTimer */
typedef P2VAR(FrNm_TimeoutTimerType, TYPEDEF, FRNM_VAR_NOINIT) FrNm_TimeoutTimerPtrType;

/**   \brief  type used to point to FrNm_TxDataPduId */
typedef P2CONST(FrNm_TxDataPduIdType, TYPEDEF, FRNM_CONST) FrNm_TxDataPduIdPtrType;

/**   \brief  type used to point to FrNm_TxMessageData */
typedef P2VAR(FrNm_TxMessageDataType, TYPEDEF, FRNM_VAR_NOINIT) FrNm_TxMessageDataPtrType;

/** 
  \}
*/ 

/** 
  \defgroup  FrNmPCRootValueTypes  FrNm Root Value Types (PRE_COMPILE)
  \brief  These type definitions are used for value representations in root arrays.
  \{
*/ 
/**   \brief  type used in FrNm_PCConfig */
typedef struct sFrNm_PCConfigType
{
  uint8 FrNm_PCConfigNeverUsed;  /**< dummy entry for the structure in the configuration variant precompile which is not used by the code. */
} FrNm_PCConfigType;

typedef FrNm_PCConfigType FrNm_ConfigType;  /**< A structure type is present for data in each configuration class. This typedef redefines the probably different name to the specified one. */

/** 
  \}
*/ 


/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: GLOBAL SIMPLE DATA TYPES AND STRUCTURES
**********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: GLOBAL COMPLEX DATA TYPES AND STRUCTURES
**********************************************************************************************************************/

/**********************************************************************************************************************
  CONFIGURATION CLASS: POST_BUILD
  SECTION: GLOBAL SIMPLE DATA TYPES AND STRUCTURES
**********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: POST_BUILD
  SECTION: GLOBAL COMPLEX DATA TYPES AND STRUCTURES
**********************************************************************************************************************/



/**********************************************************************************************************************
 *  GLOBAL DATA PROTOTYPES
 *********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL DATA PROTOTYPES
**********************************************************************************************************************/
/**********************************************************************************************************************
  FrNm_SysToNmChInd
**********************************************************************************************************************/
/** 
  \var    FrNm_SysToNmChInd
  \brief  System to FrNm Channel Indirection
*/ 
#define FRNM_START_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern CONST(FrNm_SysToNmChIndType, FRNM_CONST) FrNm_SysToNmChInd[3];
#define FRNM_STOP_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_CurrentFrCycle
**********************************************************************************************************************/
/** 
  \var    FrNm_CurrentFrCycle
  \brief  Current Emulated / Actual FlexRay Cycle
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern VAR(FrNm_CurrentFrCycleUType, FRNM_VAR_NOINIT) FrNm_CurrentFrCycle;  /* PRQA S 0759 */  /* MD_CSL_Union */
#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_LastNetworkRequested
**********************************************************************************************************************/
/** 
  \var    FrNm_LastNetworkRequested
  \brief  Last Network Requested
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern VAR(FrNm_LastNetworkRequestedUType, FRNM_VAR_NOINIT) FrNm_LastNetworkRequested;  /* PRQA S 0759 */  /* MD_CSL_Union */
#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_ModuleInitialized
**********************************************************************************************************************/
#define FRNM_START_SEC_VAR_ZERO_INIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern VAR(FrNm_ModuleInitializedType, FRNM_VAR_ZERO_INIT) FrNm_ModuleInitialized;
#define FRNM_STOP_SEC_VAR_ZERO_INIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_MsgConfirmationFlag
**********************************************************************************************************************/
/** 
  \var    FrNm_MsgConfirmationFlag
  \brief  Message Confirmation Flag
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern VAR(FrNm_MsgConfirmationFlagUType, FRNM_VAR_NOINIT) FrNm_MsgConfirmationFlag;  /* PRQA S 0759 */  /* MD_CSL_Union */
#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_MsgIndicationFlag
**********************************************************************************************************************/
/** 
  \var    FrNm_MsgIndicationFlag
  \brief  Message Indication Flag
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern VAR(FrNm_MsgIndicationFlagUType, FRNM_VAR_NOINIT) FrNm_MsgIndicationFlag;  /* PRQA S 0759 */  /* MD_CSL_Union */
#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_NetworkRequested
**********************************************************************************************************************/
/** 
  \var    FrNm_NetworkRequested
  \brief  Network Requested
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern VAR(FrNm_NetworkRequestedUType, FRNM_VAR_NOINIT) FrNm_NetworkRequested;  /* PRQA S 0759 */  /* MD_CSL_Union */
#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_NetworkRestartFlag
**********************************************************************************************************************/
/** 
  \var    FrNm_NetworkRestartFlag
  \brief  Network Restart Flag
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern VAR(FrNm_NetworkRestartFlagUType, FRNM_VAR_NOINIT) FrNm_NetworkRestartFlag;  /* PRQA S 0759 */  /* MD_CSL_Union */
#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_NetworkTimer
**********************************************************************************************************************/
/** 
  \var    FrNm_NetworkTimer
  \brief  Network Timer
*/ 
#define FRNM_START_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern VAR(FrNm_NetworkTimerUType, FRNM_VAR_NOINIT) FrNm_NetworkTimer;  /* PRQA S 0759 */  /* MD_CSL_Union */
#define FRNM_STOP_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_NmState
**********************************************************************************************************************/
/** 
  \var    FrNm_NmState
  \brief  Current state of the state machine
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern VAR(FrNm_NmStateUType, FRNM_VAR_NOINIT) FrNm_NmState;  /* PRQA S 0759 */  /* MD_CSL_Union */
#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_RxMessageData
**********************************************************************************************************************/
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern VAR(FrNm_RxMessageDataType, FRNM_VAR_NOINIT) FrNm_RxMessageData[8];
#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_RxMessageVote
**********************************************************************************************************************/
/** 
  \var    FrNm_RxMessageVote
  \brief  Rx NM message vote buffer
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern VAR(FrNm_RxMessageVoteUType, FRNM_VAR_NOINIT) FrNm_RxMessageVote;  /* PRQA S 0759 */  /* MD_CSL_Union */
#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_TimeoutTimer
**********************************************************************************************************************/
/** 
  \var    FrNm_TimeoutTimer
  \brief  Timer for NM Algorithm
*/ 
#define FRNM_START_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern VAR(FrNm_TimeoutTimerUType, FRNM_VAR_NOINIT) FrNm_TimeoutTimer;  /* PRQA S 0759 */  /* MD_CSL_Union */
#define FRNM_STOP_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_TxMessageData
**********************************************************************************************************************/
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
extern VAR(FrNm_TxMessageDataType, FRNM_VAR_NOINIT) FrNm_TxMessageData[8];
#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */


/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: GLOBAL DATA PROTOTYPES
**********************************************************************************************************************/

/**********************************************************************************************************************
  CONFIGURATION CLASS: POST_BUILD
  SECTION: GLOBAL DATA PROTOTYPES
**********************************************************************************************************************/


/**********************************************************************************************************************
 *  GLOBAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/

/* Local main function of FlexRay NM. */
#define FRNM_START_SEC_CODE
#include "MemMap.h"  /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
  FrNm_LocalMainFunction()
**********************************************************************************************************************/
/*! \brief       Main function of the FlexRay Network Management.
 *  \details     This function is called cyclically and processes the NM algorithm
 *  \pre         -
 *  \config      -
 *  \context     TASK
 *  \reentrant   FALSE
 *  \synchronous TRUE
 *  \note        Function is called by FrNm (FrNm_MainFunction_<NmClstIdx>)
 *               Is not static as called from the generated code, but must not be called by any other component
 *********************************************************************************************************************/
#if defined ( FRNM_OPTIMIZE_CHANNEL_ENABLED )
extern FUNC( void, FRNM_CODE ) FrNm_LocalMainFunction( void );
#else
extern FUNC( void, FRNM_CODE ) FrNm_LocalMainFunction( CONST( NetworkHandleType, AUTOMATIC ) channel );
#endif

#define FRNM_STOP_SEC_CODE
#include "MemMap.h"  /* PRQA S 5087 */ /* MD_MSR_MemMap */


#endif /* FRNM_CFG_H */

/**********************************************************************************************************************
 *  END OF FILE: FrNm_Cfg.h
 *********************************************************************************************************************/

