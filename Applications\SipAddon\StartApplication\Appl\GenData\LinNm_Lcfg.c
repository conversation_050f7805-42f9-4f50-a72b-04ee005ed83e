/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: LinNm
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: LinNm_Lcfg.c
 *   Generation Time: 2025-08-05 10:37:17
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/


#include "ComStack_Types.h"
#include "NmStack_Types.h"
#include "LinNm_Cfg.h"

#define LINNM_START_SEC_CONST_UNSPECIFIED
#include "MemMap.h"  /* PRQA S 5087 *//* MD_MSR_MemMap */

/**********************************************************************************************************************
 *  System To LinNm Channel
 *********************************************************************************************************************/
CONST(NetworkHandleType, LINNM_CONST) LinNm_SystemToLinNmChannel[LinNm_NumberOfSystemChannels] = 
{
  0xFFu /*  ComMConf_ComMChannel_CN_CAN_fe6ecc87 -> No LinNm channel  */ , 
  0u /*  ComMConf_ComMChannel_CN_LIN00_19b2d5e7 -> LinNmChannelConfig  */ , 
  0xFFu /*  ComMConf_ComMChannel_CN_FlexRay_oChannel_A_24bd889a -> No LinNm channel  */ 
};


/**********************************************************************************************************************
 *  LinNm To System Channel
 *********************************************************************************************************************/
CONST(NetworkHandleType, LINNM_CONST) LinNm_LinNmToSystemChannel[LinNm_NumberOfLinNmChannels] = 
{
  1u /*  LinNmChannelConfig -> ComMConf_ComMChannel_CN_LIN00_19b2d5e7  */ 
};

#define LINNM_STOP_SEC_CONST_UNSPECIFIED
#include "MemMap.h"  /* PRQA S 5087 *//* MD_MSR_MemMap */ 



#define LINNM_START_SEC_VAR_NOINIT_8BIT
#include "MemMap.h"  /* PRQA S 5087 *//* MD_MSR_MemMap */

/**********************************************************************************************************************
 *  Channel Data Structure
 *********************************************************************************************************************/
VAR(Nm_StateType, LINNM_VAR_NOINIT) LinNm_NmState[LinNm_NumberOfLinNmChannels];
VAR(Nm_ModeType, LINNM_VAR_NOINIT)  LinNm_NmMode [LinNm_NumberOfLinNmChannels];

#define LINNM_STOP_SEC_VAR_NOINIT_8BIT
#include "MemMap.h"  /* PRQA S 5087 *//* MD_MSR_MemMap */



