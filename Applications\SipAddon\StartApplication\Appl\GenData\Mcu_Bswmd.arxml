<?xml version="1.0" encoding="UTF-8"?>

<!--
/*******************************************************************************
**                                                                            **
** Copyright (C) Infineon Technologies (2020)                                 **
**                                                                            **
** All rights reserved.                                                       **
**                                                                            **
** This document contains proprietary information belonging to Infineon       **
** Technologies. Passing on and copying of this document, and communication   **
** of its contents is not permitted without prior written authorization.      **
**                                                                            **
********************************************************************************
**                                                                            **
**  FILENAME  : Mcu_Bswmd.arxml                                               **
**                                                                            **
**  VERSION   : 1.40.0_19.0.0                                                 **
**                                                                            **
**  DATE, TIME: 2025-08-05, 10:50:53          !!!IGNORE-LINE!!!               **
**                                                                            **
**  GENERATOR : Build b191017-0938            !!!IGNORE-LINE!!!               **
**                                                                            **
**  VARIANT   : Variant PB                                                    **
**                                                                            **
**  PLATFORM  : Infineon AURIX2G                                              **
**                                                                            **
**  AUTHOR    : DL-AUTOSAR-Engineering                                        **
**                                                                            **
**  VENDOR    : Infineon Technologies                                         **
**                                                                            **
**  DESCRIPTION  : Basic Software Module Description for MCU                  **
**                                                                            **
**  SPECIFICATION(S) : Specification of Mcu Driver, AUTOSAR Release 4.2.2     **
**                                                                            **
**  MAY BE CHANGED BY USER : no                                               **
**                                                                            **
*******************************************************************************/
-->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-2-2.xsd">
<AR-PACKAGES>
  <AR-PACKAGE>
    <SHORT-NAME>AUTOSAR_Mcu</SHORT-NAME>
    <AR-PACKAGES>
      <AR-PACKAGE>
        <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
        <CATEGORY>STANDARD</CATEGORY>
        <ELEMENTS>
          <BSW-MODULE-DESCRIPTION>
            <SHORT-NAME>Mcu</SHORT-NAME>
            <LONG-NAME>
              <L-4 L="EN">Mcu Driver</L-4>
            </LONG-NAME>
            <CATEGORY>BSW_MODULE</CATEGORY>
            <MODULE-ID>101</MODULE-ID>
            <PROVIDED-ENTRYS>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_Init</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_InitRamSection</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_InitClock</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_DistributePllClock</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_GetPllStatus</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_GetResetReason</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_GetResetRawValue</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_SetMode</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChannelInit</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChannelShadowTransfer</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChannelDeInit</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChannelInit</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChannelShadowTransfer</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChannelDeInit</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TimChannelInit</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TimChannelDeInit</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChannelEnable</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChannelDisable</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_IsTomChannelEnabled</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChannelEnable</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChannelDisable</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_IsAtomChannelEnabled</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TimChannelEnable</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TimChannelDisable</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_IsTimChannelEnabled</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_ConnectPortPinToTim</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_ConnectTimerOutToPortPin</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomTriggerRequest</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChInitCheck</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChUpdateEnDis</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChOutEnCtrlUpdate</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChOutEnStatUpdate</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChEndisCtrlUpdate</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChEndisStatUpdate</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomTriggerRequest</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChInitCheck</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChUpdateEnDis</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChOutEnCtrlUpdate</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChOutEnStatUpdate</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChEndisCtrlUpdate</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChEndisStatUpdate</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChannelIsr</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChannelIsr</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TimChannelIsr</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TimChInitCheck</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerInit</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerDeInit</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerStart</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerStop</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerShadowTransfer</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerIntEnDis</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_ChannelIsr</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_TimerInit</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_TimerDeInit</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_TimerStart</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_TimerStop</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_TimerClockInit</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_ChannelIsr</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Stm_SetupComparator</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Stm_CheckComparator</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Stm_ComparatorIntDisable</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Stm_CompareMatchIsr</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Eru_GatingIsr</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerInitCheck</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_TimerInitCheck</BSW-MODULE-ENTRY-REF>
              </BSW-MODULE-ENTRY-REF-CONDITIONAL>
            </PROVIDED-ENTRYS>
            <BSW-MODULE-DEPENDENCYS>
              <BSW-MODULE-DEPENDENCY>
                <SHORT-NAME>McalLibDependency</SHORT-NAME>
                <TARGET-MODULE-ID>255</TARGET-MODULE-ID>
                <REQUIRED-ENTRYS>
                  <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WritePeripEndInitProtReg</BSW-MODULE-ENTRY-REF>
                  </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WriteSafetyEndInitProtRegMask</BSW-MODULE-ENTRY-REF>
                  </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetCpuIndex</BSW-MODULE-ENTRY-REF>
                  </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetBitAtomic</BSW-MODULE-ENTRY-REF>
                  </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_SetBitAtomic</BSW-MODULE-ENTRY-REF>
                  </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WriteCpuEndInitProtReg</BSW-MODULE-ENTRY-REF>
                  </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_DelayGetTick</BSW-MODULE-ENTRY-REF>
                  </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_DelayResetTickCalibration</BSW-MODULE-ENTRY-REF>
                  </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetSpinlock</BSW-MODULE-ENTRY-REF>
                  </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_ReleaseSpinlock</BSW-MODULE-ENTRY-REF>
                  </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                </REQUIRED-ENTRYS>
              </BSW-MODULE-DEPENDENCY>
              <BSW-MODULE-DEPENDENCY>
                <SHORT-NAME>DetDependency</SHORT-NAME>
                <TARGET-MODULE-ID>15</TARGET-MODULE-ID>
                <REQUIRED-ENTRYS>
                  <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Det/BswModuleEntrys/Det_ReportError</BSW-MODULE-ENTRY-REF>
                  </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                </REQUIRED-ENTRYS>
              </BSW-MODULE-DEPENDENCY>
              <BSW-MODULE-DEPENDENCY>
                <SHORT-NAME>DemDependency</SHORT-NAME>
                <TARGET-MODULE-ID>54</TARGET-MODULE-ID>
                <REQUIRED-ENTRYS>
                  <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Dem/BswModuleEntrys/Dem_ReportErrorStatus</BSW-MODULE-ENTRY-REF>
                  </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                </REQUIRED-ENTRYS>
              </BSW-MODULE-DEPENDENCY>
            </BSW-MODULE-DEPENDENCYS>
            <INTERNAL-BEHAVIORS>
              <BSW-INTERNAL-BEHAVIOR>
                <SHORT-NAME>McuBehavior</SHORT-NAME>
                <EXCLUSIVE-AREAS>
                  <EXCLUSIVE-AREA>
                    <SHORT-NAME>TomTgcReg</SHORT-NAME>
                    <INTRODUCTION>
                      <NOTE SI="User of MCU TOM shall ensure that the critical section protection provided by MCU for TOM is implemented to disable or enable interrupts" NOTE-TYPE="HINT"/>
                    </INTRODUCTION>
                  </EXCLUSIVE-AREA>
                  <EXCLUSIVE-AREA>
                    <SHORT-NAME>AtomAgcReg</SHORT-NAME>
                    <INTRODUCTION>
                      <NOTE SI="User of MCU ATOM shall ensure that the critical section protection provided by MCU for ATOM is implemented to disable or enable interrupts" NOTE-TYPE="HINT"/>
                    </INTRODUCTION>
                  </EXCLUSIVE-AREA>
                </EXCLUSIVE-AREAS>
                <ENTITYS>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_Init</SHORT-NAME>
                    <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_Init</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_InitClock</SHORT-NAME>
                    <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_InitClock</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_InitRamSection</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_InitRamSection</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_SetMode</SHORT-NAME>
                    <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_SetMode</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_DistributePllClock</SHORT-NAME>
                    <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_DistributePllClock</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_GetPllStatus</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_GetPllStatus</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_GetResetRawValue</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_GetResetRawValue</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_GetResetReason</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_GetResetReason</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TomChannelInit</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChannelInit</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TomChannelShadowTransfer</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/TomTgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChannelShadowTransfer</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TomChannelDeInit</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/TomTgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChannelDeInit</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_AtomChannelInit</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChannelInit</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_AtomChannelShadowTransfer</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/AtomAgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChannelShadowTransfer</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_AtomChannelDeInit</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/AtomAgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChannelDeInit</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TimChannelInit</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TimChannelInit</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TimChannelDeInit</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TimChannelDeInit</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TomChannelEnable</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/TomTgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChannelEnable</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TomChannelDisable</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/TomTgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChannelDisable</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_IsTomChannelEnabled</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_IsTomChannelEnabled</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_AtomChannelEnable</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/AtomAgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChannelEnable</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_AtomChannelDisable</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/AtomAgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChannelDisable</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_IsAtomChannelEnabled</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_IsAtomChannelEnabled</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TimChannelEnable</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TimChannelEnable</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TimChannelDisable</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TimChannelDisable</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_IsTimChannelEnabled</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_IsTimChannelEnabled</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-INTERRUPT-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TimChannelIsr</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TimChannelIsr</IMPLEMENTED-ENTRY-REF>
                  </BSW-INTERRUPT-ENTITY>
                  <BSW-INTERRUPT-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TomChannelIsr</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChannelIsr</IMPLEMENTED-ENTRY-REF>
                  </BSW-INTERRUPT-ENTITY>
                  <BSW-INTERRUPT-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_AtomChannelIsr</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChannelIsr</IMPLEMENTED-ENTRY-REF>
                  </BSW-INTERRUPT-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_ConnectPortPinToTim</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_ConnectPortPinToTim</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_ConnectTimerOutToPortPin</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_ConnectTimerOutToPortPin</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TomTriggerRequest</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/TomTgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomTriggerRequest</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TomChInitCheck</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChInitCheck</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TomChUpdateEnDis</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/TomTgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChUpdateEnDis</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TomChOutEnCtrlUpdate</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/TomTgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChOutEnCtrlUpdate</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TomChOutEnStatUpdate</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/TomTgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChOutEnStatUpdate</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TomChEndisCtrlUpdate</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/TomTgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChEndisCtrlUpdate</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TomChEndisStatUpdate</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/TomTgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TomChEndisStatUpdate</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_AtomTriggerRequest</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/AtomAgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomTriggerRequest</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_AtomChInitCheck</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChInitCheck</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_AtomChUpdateEnDis</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/AtomAgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChUpdateEnDis</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_AtomChOutEnCtrlUpdate</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/AtomAgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChOutEnCtrlUpdate</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_AtomChOutEnStatUpdate</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/AtomAgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChOutEnStatUpdate</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_AtomChEndisCtrlUpdate</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/AtomAgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChEndisCtrlUpdate</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_AtomChEndisStatUpdate</SHORT-NAME>
                    <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior/AtomAgcReg</CAN-ENTER-EXCLUSIVE-AREA-REF>
                    </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_AtomChEndisStatUpdate</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gtm_TimChInitCheck</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gtm_TimChInitCheck</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Ccu6_TimerInit</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerInit</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Ccu6_TimerStart</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerStart</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Ccu6_TimerStop</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerStop</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Ccu6_TimerShadowTransfer</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerShadowTransfer</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Ccu6_TimerIntEnDis</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerIntEnDis</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Ccu6_TimerDeInit</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerDeInit</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-INTERRUPT-ENTITY>
                    <SHORT-NAME>Mcu_17_Ccu6_ChannelIsr</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_ChannelIsr</IMPLEMENTED-ENTRY-REF>
                  </BSW-INTERRUPT-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gpt12_TimerInit</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_TimerInit</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gpt12_TimerDeInit</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_TimerDeInit</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gpt12_TimerStart</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_TimerStart</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gpt12_TimerStop</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_TimerStop</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gpt12_TimerClockInit</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_TimerClockInit</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-INTERRUPT-ENTITY>
                    <SHORT-NAME>Mcu_17_Gpt12_ChannelIsr</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_ChannelIsr</IMPLEMENTED-ENTRY-REF>
                  </BSW-INTERRUPT-ENTITY>
                  <BSW-INTERRUPT-ENTITY>
                    <SHORT-NAME>Mcu_17_Stm_CompareMatchIsr</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Stm_CompareMatchIsr</IMPLEMENTED-ENTRY-REF>
                  </BSW-INTERRUPT-ENTITY>
                  <BSW-INTERRUPT-ENTITY>
                    <SHORT-NAME>Mcu_17_Eru_GatingIsr</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Eru_GatingIsr</IMPLEMENTED-ENTRY-REF>
                  </BSW-INTERRUPT-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Stm_SetupComparator</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Stm_SetupComparator</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Stm_CheckComparator</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Stm_CheckComparator</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Stm_ComparatorIntDisable</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Stm_ComparatorIntDisable</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Ccu6_TimerInitCheck</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Ccu6_TimerInitCheck</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                  <BSW-CALLED-ENTITY>
                    <SHORT-NAME>Mcu_17_Gpt12_TimerInitCheck</SHORT-NAME>
                    <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                    <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Mcu/BswModuleEntrys/Mcu_17_Gpt12_TimerInitCheck</IMPLEMENTED-ENTRY-REF>
                  </BSW-CALLED-ENTITY>
                </ENTITYS>
                <SERVICE-DEPENDENCYS>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_GTM_CLC_ENABLE_ERR</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_GTM_CLC_DISABLE_ERR</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_CCU6_CLC_DISABLE_ERR</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_OSC_FAILURE</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_SYSTEM_PLL_TIMEOUT_ERR</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_SYSTEM_PLL_LOCK_LOSS</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_PERIPHERAL_PLL_LOCK_LOSS</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_PERIPHERAL_PLL_TIMEOUT_ERR</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_CONVCTRL_CLC_ENABLE_ERR</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_CCUCON0_UPDATE_ERR</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_CCU6_CLC_ENABLE_ERR</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_CONVCTRL_CLC_DISABLE_ERR</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_GPT12_CLC_ENABLE_ERR</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_GPT12_CLC_DISABLE_ERR</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                  <BSW-SERVICE-DEPENDENCY>
                    <SERVICE-NEEDS>
                      <DIAGNOSTIC-EVENT-NEEDS>
                        <SHORT-NAME>MCU_E_PMSWCR_UPDATE_ERR</SHORT-NAME>
                      </DIAGNOSTIC-EVENT-NEEDS>
                    </SERVICE-NEEDS>
                  </BSW-SERVICE-DEPENDENCY>
                </SERVICE-DEPENDENCYS>
              </BSW-INTERNAL-BEHAVIOR>
            </INTERNAL-BEHAVIORS>
          </BSW-MODULE-DESCRIPTION>
        </ELEMENTS>
      </AR-PACKAGE>
      <AR-PACKAGE>
        <SHORT-NAME>BswModuleEntrys</SHORT-NAME>
        <CATEGORY>STANDARD</CATEGORY>
        <ELEMENTS>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_Init</SHORT-NAME>
            <SERVICE-ID>0</SERVICE-ID>
            <IS-REENTRANT>false</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_ConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_InitClock</SHORT-NAME>
            <SERVICE-ID>2</SERVICE-ID>
            <IS-REENTRANT>false</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Std/ImplementationDataTypes/Std_ReturnType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ClockSetting</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_ClockType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_InitRamSection</SHORT-NAME>
            <SERVICE-ID>1</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Std/ImplementationDataTypes/Std_ReturnType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>RamSection</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_RamSectionType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_SetMode</SHORT-NAME>
            <SERVICE-ID>8</SERVICE-ID>
            <IS-REENTRANT>false</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>McuMode</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_ModeType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_DistributePllClock</SHORT-NAME>
            <SERVICE-ID>3</SERVICE-ID>
            <IS-REENTRANT>false</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Std/ImplementationDataTypes/Std_ReturnType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_GetPllStatus</SHORT-NAME>
            <SERVICE-ID>4</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_PllStatusType</IMPLEMENTATION-DATA-TYPE-REF>
                    <ADDITIONAL-NATIVE-TYPE-QUALIFIER>enum</ADDITIONAL-NATIVE-TYPE-QUALIFIER>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_GetResetRawValue</SHORT-NAME>
            <SERVICE-ID>6</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_RawResetType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_GetResetReason</SHORT-NAME>
            <SERVICE-ID>5</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_ResetType</IMPLEMENTATION-DATA-TYPE-REF>
                    <ADDITIONAL-NATIVE-TYPE-QUALIFIER>enum</ADDITIONAL-NATIVE-TYPE-QUALIFIER>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TomChannelInit</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TomChannelInit is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>96</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TomAtomChConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TomChannelShadowTransfer</SHORT-NAME>
            <SERVICE-ID>97</SERVICE-ID>
            <IS-REENTRANT>false</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TomChannelDeInit</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TomChannelDeInit is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>99</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TomChannelDisable</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TomChannelDisable is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>105</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TomChannelEnable</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TomChannelEnable is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>104</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerOutputEn</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerOutputEnableType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_IsTomChannelEnabled</SHORT-NAME>
            <SERVICE-ID>110</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerStatusType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_IsAtomChannelEnabled</SHORT-NAME>
            <SERVICE-ID>111</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerStatusType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_IsTimChannelEnabled</SHORT-NAME>
            <SERVICE-ID>112</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerStatusType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_AtomChannelInit</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_AtomChannelInit is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>100</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TomAtomChConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_AtomChannelShadowTransfer</SHORT-NAME>
            <SERVICE-ID>101</SERVICE-ID>
            <IS-REENTRANT>false</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_AtomChannelDeInit</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_AtomChannelDeInit is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>102</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_AtomChannelEnable</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_AtomChannelEnable is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>106</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerOutputEn</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerOutputEnableType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_AtomChannelDisable</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_AtomChannelDisable is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>107</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TimChannelInit</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TimChannelInit is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>98</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimChConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TimChannelDeInit</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TimChannelDeInit is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>103</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TimChannelEnable</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TimChannelEnable is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>108</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TimChannelDisable</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TimChannelDisable is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>109</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TimChannelIsr</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TimChannelIsr is reentrant but for different channels." NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>INTERRUPT</CALL-TYPE>
            <EXECUTION-CONTEXT>INTERRUPT-CAT-1</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TomChannelIsr</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TomChannelIsr is reentrant but for different channels." NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>INTERRUPT</CALL-TYPE>
            <EXECUTION-CONTEXT>INTERRUPT-CAT-1</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_AtomChannelIsr</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_AtomChannelIsr is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>INTERRUPT</CALL-TYPE>
            <EXECUTION-CONTEXT>INTERRUPT-CAT-1</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_ConnectPortPinToTim</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TimChannelIsr is reentrant for TIM modules" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>114</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerChselValue</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_ConnectTimerOutToPortPin</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_ConnectTimerOutToPortPin is reentrant for other port pins" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>113</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Tout_IndexNumber</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerOutColumnSelect</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_MappedPortTimerOutType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TomChInitCheck</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TomChInitCheck is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>116</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Std/ImplementationDataTypes/Std_ReturnType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TomAtomChConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_AtomChInitCheck</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_AtomChInitCheck is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>123</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Std/ImplementationDataTypes/Std_ReturnType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TomAtomChConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TimChInitCheck</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TimChInitCheck is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>129</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Std/ImplementationDataTypes/Std_ReturnType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TomAtomChConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_AtomChEndisCtrlUpdate</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_AtomChEndisCtrlUpdate is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>127</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerEnDis</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerEnTriggerType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_AtomChEndisStatUpdate</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_AtomChEndisStatUpdate is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>128</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerEnDis</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerEnableType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TomChEndisStatUpdate</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TomChEndisStatUpdate is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>121</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerEnDis</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerEnTriggerType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TomChEndisCtrlUpdate</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TomChEndisCtrlUpdate is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>120</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerEnDis</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerEnTriggerType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_AtomChOutEnStatUpdate</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_AtomChOutEnStatUpdate is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>126</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerOutputEnDis</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerOutputEnableType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TomChOutEnStatUpdate</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TomChOutEnStatUpdate is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>119</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerOutputEnDis</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerOutputEnableType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TomChOutEnCtrlUpdate</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TomChOutEnCtrlUpdate is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>118</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerOutputEnDis</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerOutputEnTriggerType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TomChUpdateEnDis</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TomChUpdateEnDis is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>117</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>UpEnVal</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerUpdateEnableType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_AtomChUpdateEnDis</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_AtomChUpdateEnDis is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>124</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>UpEnVal</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerUpdateEnableType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_AtomChOutEnCtrlUpdate</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_AtomChOutEnCtrlUpdate is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>125</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Channel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerOutputEnDis</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerOutputEnTriggerType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_AtomTriggerRequest</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_AtomTriggerRequest is reentrant for other AGC" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>122</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TriggerChannels</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gtm_TomTriggerRequest</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gtm_TomTriggerRequest is reentrant for other TGC" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>115</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Module</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TomTgcIndex</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TriggerChannels</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Ccu6_TimerInit</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Ccu6_TimerInit is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>130</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Ccu6_TimerConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Ccu6_TimerDeInit</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Ccu6_TimerDeInit is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>131</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Ccu6_TimerChIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Ccu6_TimerStart</SHORT-NAME>
              <INTRODUCTION>
                <NOTE SI="Mcu_17_Ccu6_TimerStart is reentrant for other channels" NOTE-TYPE="HINT" />
              </INTRODUCTION>
            <SERVICE-ID>132</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Ccu6_TimerChIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Ccu6_TimerStop</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Ccu6_TimerStop is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>133</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Ccu6_TimerChIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Ccu6_TimerShadowTransfer</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Ccu6_TimerShadowTransfer is reentrant for other CCU6 timers" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>134</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Ccu6_TimerChIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Ccu6_TimerIntEnDis</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Ccu6_TimerIntEnDis is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>135</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Ccu6IntConfig</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Ccu6_TimerChIntType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Ccu6_ChannelIsr</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Ccu6_ChannelIsr is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>INTERRUPT</CALL-TYPE>
            <EXECUTION-CONTEXT>INTERRUPT-CAT-1</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Kernel</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Ccu6_KernelIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Comparator</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Ccu6_ComparatorType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gpt12_TimerInit</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gpt12_TimerInit is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>138</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gpt12_TimerConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gpt12_TimerDeInit</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gpt12_TimerDeInit is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>146</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gpt12_TimerChIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gpt12_TimerStart</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gpt12_TimerStart is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>147</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gpt12_TimerChIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gpt12_TimerStop</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gpt12_TimerStop is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>148</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>TimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gpt12_TimerChIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gpt12_TimerClockInit</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gpt12_TimerClockInit is reentrant for other timer blocks" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>149</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>BlockId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gpt12_TimerBlockType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ClockDiv</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gpt12_ClkPrescalarType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gpt12_ChannelIsr</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gpt12_ChannelIsr is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>INTERRUPT</CALL-TYPE>
            <EXECUTION-CONTEXT>INTERRUPT-CAT-1</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Timer</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gpt12_TimerChIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Ccu6_TimerInitCheck</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Ccu6_TimerInitCheck is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>137</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Std/ImplementationDataTypes/Std_ReturnType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Ccu6_TimerConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Gpt12_TimerInitCheck</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Gpt12_TimerInitCheck is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>139</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Std/ImplementationDataTypes/Std_ReturnType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gpt12_TimerConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Stm_ComparatorIntDisable</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Stm_ComparatorIntDisable is reentrant for STM timers" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>136</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>StmTimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>StmComparatorId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Stm_SetupComparator</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Stm_SetupComparator is reentrant but for other STM Comparator." NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>144</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Stm_TimerConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Stm_CheckComparator</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Stm_CheckComparator is reentrant for other STM Timers." NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <SERVICE-ID>145</SERVICE-ID>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>REGULAR</CALL-TYPE>
            <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <RETURN-TYPE>
              <SHORT-NAME>Return</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Std/ImplementationDataTypes/Std_ReturnType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </RETURN-TYPE>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>ConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Stm_TimerConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Eru_GatingIsr</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Eru_GatingIsr is reentrant for other channels" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>INTERRUPT</CALL-TYPE>
            <EXECUTION-CONTEXT>INTERRUPT-CAT-1</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>Timer</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Eru_SrcIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
          <BSW-MODULE-ENTRY>
            <SHORT-NAME>Mcu_17_Stm_CompareMatchIsr</SHORT-NAME>
            <INTRODUCTION>
              <NOTE SI="Mcu_17_Stm_CompareMatchIsr is reentrant for other STM timers" NOTE-TYPE="HINT" />
            </INTRODUCTION>
            <IS-REENTRANT>true</IS-REENTRANT>
            <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
            <CALL-TYPE>INTERRUPT</CALL-TYPE>
            <EXECUTION-CONTEXT>INTERRUPT-CAT-1</EXECUTION-CONTEXT>
            <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            <ARGUMENTS>
              <SW-SERVICE-ARG>
                <SHORT-NAME>StmTimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Stm_StmIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
              <SW-SERVICE-ARG>
                <SHORT-NAME>StmCmpId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Stm_StmCmpIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </SW-SERVICE-ARG>
            </ARGUMENTS>
          </BSW-MODULE-ENTRY>
        </ELEMENTS>
      </AR-PACKAGE>
      <AR-PACKAGE>
        <SHORT-NAME>Implementations</SHORT-NAME>
        <ELEMENTS>
          <BSW-IMPLEMENTATION>
            <SHORT-NAME>Mcu</SHORT-NAME>
            <CODE-DESCRIPTORS>
              <CODE>
                <SHORT-NAME>Files</SHORT-NAME>
                <ARTIFACT-DESCRIPTORS>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>ssc::src::Mcu.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>ssc::inc::Mcu.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                </ARTIFACT-DESCRIPTORS>
              </CODE>
            </CODE-DESCRIPTORS>
            <GENERATED-ARTIFACTS>
              <DEPENDENCY-ON-ARTIFACT>
                <SHORT-NAME>Mcu_PBcfg_c</SHORT-NAME>
                <ARTIFACT-DESCRIPTOR>
                  <SHORT-LABEL>generate::template::src::Mcu_PBcfg.c</SHORT-LABEL>
                  <CATEGORY>SWTEMPLATE</CATEGORY>
                </ARTIFACT-DESCRIPTOR>
                <USAGES>
                  <USAGE>EXECUTE</USAGE>
                </USAGES>
              </DEPENDENCY-ON-ARTIFACT>
              <DEPENDENCY-ON-ARTIFACT>
                <SHORT-NAME>Mcu_Cfg_h</SHORT-NAME>
                <ARTIFACT-DESCRIPTOR>
                  <SHORT-LABEL>generate::template::inc::Mcu_Cfg.h</SHORT-LABEL>
                  <CATEGORY>SWTEMPLATE</CATEGORY>
                </ARTIFACT-DESCRIPTOR>
                <USAGES>
                  <USAGE>COMPILE</USAGE>
                </USAGES>
              </DEPENDENCY-ON-ARTIFACT>
              <DEPENDENCY-ON-ARTIFACT>
                <SHORT-NAME>Mcu_PBcfg_h</SHORT-NAME>
                <ARTIFACT-DESCRIPTOR>
                  <SHORT-LABEL>generate::template::inc::Mcu_PBcfg.h</SHORT-LABEL>
                  <CATEGORY>SWTEMPLATE</CATEGORY>
                </ARTIFACT-DESCRIPTOR>
                <USAGES>
                  <USAGE>EXECUTE</USAGE>
                </USAGES>
              </DEPENDENCY-ON-ARTIFACT>
              <DEPENDENCY-ON-ARTIFACT>
                <SHORT-NAME>Mcu_xdm</SHORT-NAME>
                <ARTIFACT-DESCRIPTOR>
                  <SHORT-LABEL>config::Mcu.xdm</SHORT-LABEL>
                  <CATEGORY>SWTOOL</CATEGORY>
                </ARTIFACT-DESCRIPTOR>
                <USAGES>
                  <USAGE>CODEGENERATION</USAGE>
                </USAGES>
              </DEPENDENCY-ON-ARTIFACT>
              <DEPENDENCY-ON-ARTIFACT>
                <SHORT-NAME>McuPreConfiguration_xdm</SHORT-NAME>
                <ARTIFACT-DESCRIPTOR>
                  <SHORT-LABEL>config::McuPreConfiguration.xdm</SHORT-LABEL>
                  <CATEGORY>SWTOOL</CATEGORY>
                </ARTIFACT-DESCRIPTOR>
                <USAGES>
                  <USAGE>CODEGENERATION</USAGE>
                </USAGES>
              </DEPENDENCY-ON-ARTIFACT>
                
              <DEPENDENCY-ON-ARTIFACT>
                <SHORT-NAME>Mcu_bmd</SHORT-NAME>
                <ARTIFACT-DESCRIPTOR>
                  <SHORT-LABEL>autosar::TC37x::TC377x_ED_EX::Mcu.bmd</SHORT-LABEL>
                  <CATEGORY>SWTOOL</CATEGORY>
                </ARTIFACT-DESCRIPTOR>
                <USAGES>
                  <USAGE>CODEGENERATION</USAGE>
                </USAGES>
              </DEPENDENCY-ON-ARTIFACT>
            </GENERATED-ARTIFACTS>
            <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
            <RESOURCE-CONSUMPTION>
              <SHORT-NAME>ResourceConsumption</SHORT-NAME>
              <MEMORY-SECTIONS>
                <MEMORY-SECTION>
                  <SHORT-NAME>CONFIG_DATA_ASIL_B_GLOBAL_UNSPECIFIED</SHORT-NAME>
                  <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                  <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONFIG_DATA</SW-ADDRMETHOD-REF>
                </MEMORY-SECTION>
                <MEMORY-SECTION>
                  <SHORT-NAME>VAR_CLEARED_ASIL_B_GLOBAL_32</SHORT-NAME>
                  <ALIGNMENT>32</ALIGNMENT>
                  <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/VAR_CLEARED</SW-ADDRMETHOD-REF>
                </MEMORY-SECTION>
                <MEMORY-SECTION>
                  <SHORT-NAME>CODE_ASIL_B_GLOBAL</SHORT-NAME>
                  <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                  <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CODE</SW-ADDRMETHOD-REF>
                </MEMORY-SECTION>
              </MEMORY-SECTIONS>
              <SECTION-NAME-PREFIXS>
                <SECTION-NAME-PREFIX>
                  <SHORT-NAME>MCU</SHORT-NAME>
                  <SYMBOL>MCU</SYMBOL>
                </SECTION-NAME-PREFIX>
              </SECTION-NAME-PREFIXS>
            </RESOURCE-CONSUMPTION>
            <SW-VERSION>10.40.2</SW-VERSION>
            <VENDOR-ID>17</VENDOR-ID>
            <AR-RELEASE-VERSION>4.2.2</AR-RELEASE-VERSION>
            <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior</BEHAVIOR-REF>
            <VENDOR-SPECIFIC-MODULE-DEF-REFS>
              <VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/AURIX2G/EcucDefs/Mcu</VENDOR-SPECIFIC-MODULE-DEF-REF>
            </VENDOR-SPECIFIC-MODULE-DEF-REFS>
          </BSW-IMPLEMENTATION>
          <BSW-IMPLEMENTATION>
            <SHORT-NAME>Mcu_17_TimerIp</SHORT-NAME>
            <CODE-DESCRIPTORS>
              <CODE>
                <SHORT-NAME>Files</SHORT-NAME>
                <ARTIFACT-DESCRIPTORS>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>ssc::src::Mcu_17_TimerIp.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>ssc::inc::Mcu_17_TimerIp.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                </ARTIFACT-DESCRIPTORS>
              </CODE>
            </CODE-DESCRIPTORS>
            <GENERATED-ARTIFACTS>
              <DEPENDENCY-ON-ARTIFACT>
                <SHORT-NAME>Mcu_17_TimerIp_Cfg_c</SHORT-NAME>
                <ARTIFACT-DESCRIPTOR>
                  <SHORT-LABEL>generate::template::src::Mcu_17_TimerIp_Cfg.c</SHORT-LABEL>
                  <CATEGORY>SWTEMPLATE</CATEGORY>
                </ARTIFACT-DESCRIPTOR>
                <USAGES>
                  <USAGE>COMPILE</USAGE>
                </USAGES>
              </DEPENDENCY-ON-ARTIFACT>
              <DEPENDENCY-ON-ARTIFACT>
                <SHORT-NAME>Mcu_17_TimerIp_Cfg_h</SHORT-NAME>
                <ARTIFACT-DESCRIPTOR>
                  <SHORT-LABEL>generate::template::inc::Mcu_17_TimerIp_Cfg.h</SHORT-LABEL>
                  <CATEGORY>SWTEMPLATE</CATEGORY>
                </ARTIFACT-DESCRIPTOR>
                <USAGES>
                  <USAGE>COMPILE</USAGE>
                </USAGES>
              </DEPENDENCY-ON-ARTIFACT>
            </GENERATED-ARTIFACTS>
            <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
            <RESOURCE-CONSUMPTION>
              <SHORT-NAME>ResourceConsumption</SHORT-NAME>
              <MEMORY-SECTIONS>
                <MEMORY-SECTION>
                  <SHORT-NAME>VAR_CLEARED_ASIL_B_GLOBAL_32</SHORT-NAME>
                  <ALIGNMENT>32</ALIGNMENT>
                  <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/VAR_CLEARED</SW-ADDRMETHOD-REF>
                </MEMORY-SECTION>
                <MEMORY-SECTION>
                  <SHORT-NAME>CONFIG_DATA_ASIL_B_GLOBAL_8</SHORT-NAME>
                  <ALIGNMENT>8</ALIGNMENT>
                  <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONFIG_DATA</SW-ADDRMETHOD-REF>
                </MEMORY-SECTION>
                <MEMORY-SECTION>
                  <SHORT-NAME>CONST_ASIL_B_GLOBAL_UNSPECIFIED</SHORT-NAME>
                  <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                  <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONST</SW-ADDRMETHOD-REF>
                </MEMORY-SECTION>
                <MEMORY-SECTION>
                  <SHORT-NAME>CONFIG_DATA_ASIL_B_GLOBAL_16</SHORT-NAME>
                  <ALIGNMENT>16</ALIGNMENT>
                  <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONFIG_DATA</SW-ADDRMETHOD-REF>
                </MEMORY-SECTION>
                <MEMORY-SECTION>
                  <SHORT-NAME>VAR_INIT_ASIL_B_GLOBAL_32</SHORT-NAME>
                  <ALIGNMENT>32</ALIGNMENT>
                  <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/VAR_INIT</SW-ADDRMETHOD-REF>
                </MEMORY-SECTION>
                <MEMORY-SECTION>
                  <SHORT-NAME>VAR_INIT_ASIL_B_GLOBAL_UNSPECIFIED</SHORT-NAME>
                  <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                  <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/VAR_INIT</SW-ADDRMETHOD-REF>
                </MEMORY-SECTION>
                <MEMORY-SECTION>
                  <SHORT-NAME>CODE_ASIL_B_GLOBAL</SHORT-NAME>
                  <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                  <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CODE</SW-ADDRMETHOD-REF>
                </MEMORY-SECTION>
                <MEMORY-SECTION>
                  <SHORT-NAME>CODE_FAST_ASIL_B_GLOBAL</SHORT-NAME>
                  <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                  <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CODE_FAST</SW-ADDRMETHOD-REF>
                </MEMORY-SECTION>
                <MEMORY-SECTION>
                  <SHORT-NAME>CONFIG_DATA_ASIL_B_GLOBAL_32</SHORT-NAME>
                  <ALIGNMENT>32</ALIGNMENT>
                  <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONFIG_DATA</SW-ADDRMETHOD-REF>
                </MEMORY-SECTION>
              </MEMORY-SECTIONS>
              <SECTION-NAME-PREFIXS>
                <SECTION-NAME-PREFIX>
                  <SHORT-NAME>MCU_17_TIMERIP</SHORT-NAME>
                  <SYMBOL>MCU_17_TIMERIP</SYMBOL>
                </SECTION-NAME-PREFIX>
              </SECTION-NAME-PREFIXS>
            </RESOURCE-CONSUMPTION>
            <SW-VERSION>4.0.0</SW-VERSION>
            <VENDOR-ID>17</VENDOR-ID>
            <AR-RELEASE-VERSION>4.2.2</AR-RELEASE-VERSION>
            <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/AUTOSAR_Mcu/BswModuleDescriptions/Mcu/McuBehavior</BEHAVIOR-REF>
            <VENDOR-SPECIFIC-MODULE-DEF-REFS>
              <VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/AURIX2G/EcucDefs/Mcu</VENDOR-SPECIFIC-MODULE-DEF-REF>
            </VENDOR-SPECIFIC-MODULE-DEF-REFS>
          </BSW-IMPLEMENTATION>
        </ELEMENTS>
      </AR-PACKAGE>
      <AR-PACKAGE>
        <SHORT-NAME>ImplementationDataTypes</SHORT-NAME>
        <CATEGORY>STANDARD</CATEGORY>
        <ELEMENTS>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Ccu6_TimerChIntType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Ccu6_TimerChIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>IEnBitPos</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>IEnLen</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>RegVal</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Eru_SrcIdentifierType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gpt12_ClkPrescalarType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gpt12_TimerBlockType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gtm_MappedPortTimerOutType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <COMPU-METHOD-REF DEST="COMPU-METHOD">/AUTOSAR_Mcu/CompuMethods/Mcu_17_Gtm_MappedPortTimerOutType</COMPU-METHOD-REF>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                  <ADDITIONAL-NATIVE-TYPE-QUALIFIER>enum</ADDITIONAL-NATIVE-TYPE-QUALIFIER>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gtm_TimerEnableType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gtm_TimerEnTriggerType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <COMPU-METHOD-REF DEST="COMPU-METHOD">/AUTOSAR_Mcu/CompuMethods/Mcu_17_Gtm_TimerEnTriggerType</COMPU-METHOD-REF>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                  <ADDITIONAL-NATIVE-TYPE-QUALIFIER>enum</ADDITIONAL-NATIVE-TYPE-QUALIFIER>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gtm_TimerOutputEnableType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <COMPU-METHOD-REF DEST="COMPU-METHOD">/AUTOSAR_Mcu/CompuMethods/Mcu_17_Gtm_TimerOutputEnableType</COMPU-METHOD-REF>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                  <ADDITIONAL-NATIVE-TYPE-QUALIFIER>enum</ADDITIONAL-NATIVE-TYPE-QUALIFIER>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gtm_TimerOutputEnTriggerType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <COMPU-METHOD-REF DEST="COMPU-METHOD">/AUTOSAR_Mcu/CompuMethods/Mcu_17_Gtm_TimerOutputEnTriggerType</COMPU-METHOD-REF>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                  <ADDITIONAL-NATIVE-TYPE-QUALIFIER>enum</ADDITIONAL-NATIVE-TYPE-QUALIFIER>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gtm_TimerUpdateEnableType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Stm_StmCmpIdentifierType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Stm_StmIdentifierType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Stm_TimerConfigType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>StmTimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-BIT-REPRESENTATION>
                        <NUMBER-OF-BITS>8</NUMBER-OF-BITS>
                      </SW-BIT-REPRESENTATION>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>CMPRegId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-BIT-REPRESENTATION>
                        <NUMBER-OF-BITS>8</NUMBER-OF-BITS>
                      </SW-BIT-REPRESENTATION>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>CompareRegVal</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-BIT-REPRESENTATION>
                        <NUMBER-OF-BITS>8</NUMBER-OF-BITS>
                      </SW-BIT-REPRESENTATION>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>reserved</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-BIT-REPRESENTATION>
                        <NUMBER-OF-BITS>8</NUMBER-OF-BITS>
                      </SW-BIT-REPRESENTATION>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>CmconRegVal</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_PllStatusType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <COMPU-METHOD-REF DEST="COMPU-METHOD">/AUTOSAR_Mcu/CompuMethods/Mcu_PllStatusType</COMPU-METHOD-REF>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                  <ADDITIONAL-NATIVE-TYPE-QUALIFIER>enum</ADDITIONAL-NATIVE-TYPE-QUALIFIER>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_ClockType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_ResetType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <COMPU-METHOD-REF DEST="COMPU-METHOD">/AUTOSAR_Mcu/CompuMethods/Mcu_ResetType</COMPU-METHOD-REF>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                  <ADDITIONAL-NATIVE-TYPE-QUALIFIER>enum</ADDITIONAL-NATIVE-TYPE-QUALIFIER>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_RawResetType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_RamSectionType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_ModeType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gtm_TimChConfigType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerChIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimChCtrlReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimChExtendedCtrlReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimChFltRisingEdge</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimChFltFallingEdge</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimChIntEnMode</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gtm_TimerChIdentifierType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gtm_TimerOutType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <COMPU-METHOD-REF DEST="COMPU-METHOD">/AUTOSAR_Mcu/CompuMethods/Mcu_17_Gtm_TimerOutType</COMPU-METHOD-REF>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                  <ADDITIONAL-NATIVE-TYPE-QUALIFIER>enum</ADDITIONAL-NATIVE-TYPE-QUALIFIER>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gtm_TomAtomChConfigType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerType</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerOutType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gtm_TimerChIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerChCtrlReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerChCN0Reg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerChCM0Reg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerChCM1Reg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerChSR0Reg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerChSR1Reg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerChPortOutConfig</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerChIntEnMode</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gtm_TimerStatusType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <COMPU-METHOD-REF DEST="COMPU-METHOD">/AUTOSAR_Mcu/CompuMethods/Mcu_17_Gtm_TimerStatusType</COMPU-METHOD-REF>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                  <ADDITIONAL-NATIVE-TYPE-QUALIFIER>enum</ADDITIONAL-NATIVE-TYPE-QUALIFIER>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Ccu6_ComparatorType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Ccu6_KernelIdentifierType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Ccu6_TimerChIdentifierType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Ccu6_TimerConfigType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Ccu6_TimerChIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerCtrlReg0</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>ModCtrlReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>PasStateLvlReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerCntReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerPeriodReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>Ccu6ShadowReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerModeSelectReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>PortInSelReg0</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>IntEnReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>IntNodePointerReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gpt12_TimerChIdentifierType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_17_Gpt12_TimerConfigType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerId</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_17_Gpt12_TimerChIdentifierType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerCtrlReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TimerCntReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>PortInSelReg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_ClockConfigType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>SystemPllCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_SystemPllConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>PeripheralPllCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_PeripheralPllConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>SysPllK2DivStepUpChangeDelay</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>SysPllK2DivStepDownChangeDelay</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>PeripheralPllK2StepUpChangeDelay</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>PeripheralPllK2StepDownChangeDelay</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>PeripheralPllK3StepUpChangeDelay</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>PeripheralPllK3StepDownChangeDelay</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>PllDistributionCfgPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_PllDistributionConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>ExternalClockCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_ExternalClockConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>BackupFreqKDiv</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>ConvCtrlBlockConf</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_ConfigType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>McuClockSettingPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_ClockConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>McuRamCfgPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_RamConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>McuGtmConfigPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_GtmConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>McuLowPowerModeCfgPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_LowPowerModeType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>McuResetCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>McuArstDisCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>McuTrapSettingConf0</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>McuTrapSettingConf1</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>McuEruEiFiltCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>McuNoOfClockCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_ClockType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_ExternalClockConfigType</SHORT-NAME>
            <CATEGORY>TYPE_REFERENCE</CATEGORY>
            <SW-DATA-DEF-PROPS>
              <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                  <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
              </SW-DATA-DEF-PROPS-VARIANTS>
            </SW-DATA-DEF-PROPS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_GtmAdcTrigType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmAdcTrigOut0</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmAdcTrigOut1</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_GtmAtomConfigType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>AtomAgcIntTrigRstCn0</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>AtomAgcActTb</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_GtmClockSettingType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmCmuClockEnable</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmCmuGlobalNumerator</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmCmuGlobalDenominator</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmCmuConfClkCtrl</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <ARRAY-SIZE>8</ARRAY-SIZE>
                <ARRAY-SIZE-SEMANTICS>FIXED-SIZE</ARRAY-SIZE-SEMANTICS>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmCmuFixedClkCtrl</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmCmuClsInDiv</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmEclkCtrl</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <ARRAY-SIZE>3</ARRAY-SIZE>
                <ARRAY-SIZE-SEMANTICS>FIXED-SIZE</ARRAY-SIZE-SEMANTICS>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_GtmExtClkType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_GtmClusterConfigType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmCcmCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmCcmConfClockCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmCcmFixedClockCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_GtmConfigType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmAtomModuleUsage</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmClockCfgPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_GtmClockSettingType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmTbuCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmTomModuleUsage</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmClusterCfgPtr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <SW-POINTER-TARGET-PROPS>
                        <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                        <SW-DATA-DEF-PROPS>
                          <SW-DATA-DEF-PROPS-VARIANTS>
                            <SW-DATA-DEF-PROPS-CONDITIONAL>
                              <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_GtmClusterConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                            </SW-DATA-DEF-PROPS-CONDITIONAL>
                          </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                      </SW-POINTER-TARGET-PROPS>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmTomCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <ARRAY-SIZE>6</ARRAY-SIZE>
                <ARRAY-SIZE-SEMANTICS>FIXED-SIZE</ARRAY-SIZE-SEMANTICS>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_GtmTomConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmAtomCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <ARRAY-SIZE>6</ARRAY-SIZE>
                <ARRAY-SIZE-SEMANTICS>FIXED-SIZE</ARRAY-SIZE-SEMANTICS>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_GtmAtomConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmAdcTrigCfg</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <ARRAY-SIZE>5</ARRAY-SIZE>
                <ARRAY-SIZE-SEMANTICS>FIXED-SIZE</ARRAY-SIZE-SEMANTICS>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_GtmAdcTrigType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>IsGtmSleepModeEnabled</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_GtmExtClkType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmCmuExtClockNum</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>GtmCmuExtClockDen</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
          <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_GtmTomConfigType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TomTgcIntTrigRstCn0</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>TomTgcActTb</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            </SUB-ELEMENTS>
            <TYPE-EMITTER>BSW</TYPE-EMITTER>
          </IMPLEMENTATION-DATA-TYPE>
        <IMPLEMENTATION-DATA-TYPE>
            <SHORT-NAME>Mcu_LowPowerModeType</SHORT-NAME>
            <CATEGORY>STRUCTURE</CATEGORY>
            <SUB-ELEMENTS>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>MaxModeEvrcCtrl</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_ModeEvrcCtrlType</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>Pmswcr0</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>Pmswcr3</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>Pmswcr4</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>Pmswcr5</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>StdbyRamAdr</SHORT-NAME>
                <CATEGORY>DATA_REFERENCE</CATEGORY>
              <ARRAY-SIZE>0</ARRAY-SIZE>
              <ARRAY-SIZE-SEMANTICS>FIXED-SIZE</ARRAY-SIZE-SEMANTICS>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-POINTER-TARGET-PROPS>
                      <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </SW-POINTER-TARGET-PROPS>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
          </SUB-ELEMENTS>
          <TYPE-EMITTER>BSW</TYPE-EMITTER>
        </IMPLEMENTATION-DATA-TYPE>
        <IMPLEMENTATION-DATA-TYPE>
          <SHORT-NAME>Mcu_ModeEvrcCtrlType</SHORT-NAME>
          <CATEGORY>STRUCTURE</CATEGORY>
          <SUB-ELEMENTS>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>McuMode</SHORT-NAME>
              <CATEGORY>DATA_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-POINTER-TARGET-PROPS>
                      <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <SW-BIT-REPRESENTATION>
                              <NUMBER-OF-BITS>3</NUMBER-OF-BITS>
                            </SW-BIT-REPRESENTATION>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </SW-POINTER-TARGET-PROPS>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>EvrcLowPowerMode</SHORT-NAME>
              <CATEGORY>DATA_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-POINTER-TARGET-PROPS>
                      <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <SW-BIT-REPRESENTATION>
                              <NUMBER-OF-BITS>1</NUMBER-OF-BITS>
                            </SW-BIT-REPRESENTATION>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </SW-POINTER-TARGET-PROPS>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>Reserved</SHORT-NAME>
              <CATEGORY>DATA_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-POINTER-TARGET-PROPS>
                      <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <SW-BIT-REPRESENTATION>
                              <NUMBER-OF-BITS>28</NUMBER-OF-BITS>
                            </SW-BIT-REPRESENTATION>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </SW-POINTER-TARGET-PROPS>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
          </SUB-ELEMENTS>
          <TYPE-EMITTER>BSW</TYPE-EMITTER>
        </IMPLEMENTATION-DATA-TYPE>
        <IMPLEMENTATION-DATA-TYPE>
          <SHORT-NAME>Mcu_PeripheralPllConfigType</SHORT-NAME>
          <CATEGORY>STRUCTURE</CATEGORY>
          <SUB-ELEMENTS>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>PerPllNDiv</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-BIT-REPRESENTATION>
                      <NUMBER-OF-BITS>7</NUMBER-OF-BITS>
                    </SW-BIT-REPRESENTATION>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>PerPllPDiv</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-BIT-REPRESENTATION>
                      <NUMBER-OF-BITS>3</NUMBER-OF-BITS>
                    </SW-BIT-REPRESENTATION>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>PerPllK2Div</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-BIT-REPRESENTATION>
                      <NUMBER-OF-BITS>3</NUMBER-OF-BITS>
                    </SW-BIT-REPRESENTATION>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>PerPllK3Div</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-BIT-REPRESENTATION>
                      <NUMBER-OF-BITS>3</NUMBER-OF-BITS>
                    </SW-BIT-REPRESENTATION>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>K3DivByPass</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-BIT-REPRESENTATION>
                      <NUMBER-OF-BITS>1</NUMBER-OF-BITS>
                    </SW-BIT-REPRESENTATION>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>Reserved</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-BIT-REPRESENTATION>
                      <NUMBER-OF-BITS>15</NUMBER-OF-BITS>
                    </SW-BIT-REPRESENTATION>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
          </SUB-ELEMENTS>
          <TYPE-EMITTER>BSW</TYPE-EMITTER>
        </IMPLEMENTATION-DATA-TYPE>
        <IMPLEMENTATION-DATA-TYPE>
          <SHORT-NAME>Mcu_PllDistributionConfigType</SHORT-NAME>
          <CATEGORY>STRUCTURE</CATEGORY>
          <SUB-ELEMENTS>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>Ccucon0</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>Ccucon1</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>Ccucon2</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>Ccucon3</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>Ccucon5</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>Ccucon4</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>CcuconCpu</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <ARRAY-SIZE>3</ARRAY-SIZE>
              <ARRAY-SIZE-SEMANTICS>FIXED-SIZE</ARRAY-SIZE-SEMANTICS>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
          </SUB-ELEMENTS>
          <TYPE-EMITTER>BSW</TYPE-EMITTER>
        </IMPLEMENTATION-DATA-TYPE>
        <IMPLEMENTATION-DATA-TYPE>
          <SHORT-NAME>Mcu_RamBaseAdrType</SHORT-NAME>
          <CATEGORY>DATA_REFERENCE</CATEGORY>
          <SW-DATA-DEF-PROPS>
            <SW-DATA-DEF-PROPS-VARIANTS>
              <SW-DATA-DEF-PROPS-CONDITIONAL>
                <SW-POINTER-TARGET-PROPS>
                  <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/BaseTypes/void</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-POINTER-TARGET-PROPS>
              </SW-DATA-DEF-PROPS-CONDITIONAL>
            </SW-DATA-DEF-PROPS-VARIANTS>
          </SW-DATA-DEF-PROPS>
          <TYPE-EMITTER>BSW</TYPE-EMITTER>
        </IMPLEMENTATION-DATA-TYPE>
        <IMPLEMENTATION-DATA-TYPE>
          <SHORT-NAME>Mcu_RamConfigType</SHORT-NAME>
          <CATEGORY>STRUCTURE</CATEGORY>
          <SUB-ELEMENTS>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>RamBaseAdrPtr</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_RamBaseAdrType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>RamSize</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_RamSizeType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>RamPrstData</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Mcu/ImplementationDataTypes/Mcu_RamPrstDatType</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
          </SUB-ELEMENTS>
          <TYPE-EMITTER>BSW</TYPE-EMITTER>
        </IMPLEMENTATION-DATA-TYPE>
        <IMPLEMENTATION-DATA-TYPE>
          <SHORT-NAME>Mcu_RamPrstDatType</SHORT-NAME>
          <CATEGORY>TYPE_REFERENCE</CATEGORY>
          <SW-DATA-DEF-PROPS>
            <SW-DATA-DEF-PROPS-VARIANTS>
              <SW-DATA-DEF-PROPS-CONDITIONAL>
                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
              </SW-DATA-DEF-PROPS-CONDITIONAL>
            </SW-DATA-DEF-PROPS-VARIANTS>
          </SW-DATA-DEF-PROPS>
          <TYPE-EMITTER>BSW</TYPE-EMITTER>
        </IMPLEMENTATION-DATA-TYPE>
        <IMPLEMENTATION-DATA-TYPE>
          <SHORT-NAME>Mcu_RamSizeType</SHORT-NAME>
          <CATEGORY>TYPE_REFERENCE</CATEGORY>
          <SW-DATA-DEF-PROPS>
            <SW-DATA-DEF-PROPS-VARIANTS>
              <SW-DATA-DEF-PROPS-CONDITIONAL>
                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
              </SW-DATA-DEF-PROPS-CONDITIONAL>
            </SW-DATA-DEF-PROPS-VARIANTS>
          </SW-DATA-DEF-PROPS>
          <TYPE-EMITTER>BSW</TYPE-EMITTER>
        </IMPLEMENTATION-DATA-TYPE>
        <IMPLEMENTATION-DATA-TYPE>
          <SHORT-NAME>Mcu_SystemPllConfigType</SHORT-NAME>
          <CATEGORY>STRUCTURE</CATEGORY>
          <SUB-ELEMENTS>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>Insel</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-BIT-REPRESENTATION>
                      <NUMBER-OF-BITS>2</NUMBER-OF-BITS>
                    </SW-BIT-REPRESENTATION>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>SysPllPDiv</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-BIT-REPRESENTATION>
                      <NUMBER-OF-BITS>3</NUMBER-OF-BITS>
                    </SW-BIT-REPRESENTATION>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>SysPllNDiv</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-BIT-REPRESENTATION>
                      <NUMBER-OF-BITS>7</NUMBER-OF-BITS>
                    </SW-BIT-REPRESENTATION>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>SysPllK2Div</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-BIT-REPRESENTATION>
                      <NUMBER-OF-BITS>3</NUMBER-OF-BITS>
                    </SW-BIT-REPRESENTATION>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>FmPllEn</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-BIT-REPRESENTATION>
                      <NUMBER-OF-BITS>1</NUMBER-OF-BITS>
                    </SW-BIT-REPRESENTATION>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
              <SHORT-NAME>ModulationAmplitude</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <SW-BIT-REPRESENTATION>
                      <NUMBER-OF-BITS>16</NUMBER-OF-BITS>
                    </SW-BIT-REPRESENTATION>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/unsigned_int</IMPLEMENTATION-DATA-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
          </SUB-ELEMENTS>
          <TYPE-EMITTER>BSW</TYPE-EMITTER>
        </IMPLEMENTATION-DATA-TYPE>
      </ELEMENTS>
    </AR-PACKAGE>
    <AR-PACKAGE>
      <SHORT-NAME>CompuMethods</SHORT-NAME>
      <CATEGORY>STANDARD</CATEGORY>
      <ELEMENTS>
        <COMPU-METHOD>
          <SHORT-NAME>Mcu_17_Gtm_MappedPortTimerOutType</SHORT-NAME>
          <CATEGORY>TEXTTABLE</CATEGORY>
          <COMPU-INTERNAL-TO-PHYS>
            <COMPU-SCALES>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>0-MCU_OUT_TIMER_MAPPED_COL_A</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>1-MCU_OUT_TIMER_MAPPED_COL_B</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>2-MCU_OUT_TIMER_MAPPED_COL_C</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>3-MCU_OUT_TIMER_MAPPED_COL_D</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>4-MCU_OUT_TIMER_MAPPED_COL_E</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>5-MCU_OUT_TIMER_MAPPED_COL_F</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>6-MCU_OUT_TIMER_MAPPED_COL_G</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>7-MCU_OUT_TIMER_MAPPED_COL_H</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>8-MCU_OUT_TIMER_MAPPED_COL_I</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>9-MCU_OUT_TIMER_MAPPED_COL_J</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>10-MCU_OUT_TIMER_MAPPED_COL_K</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>11-MCU_OUT_TIMER_MAPPED_COL_L</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
            </COMPU-SCALES>
          </COMPU-INTERNAL-TO-PHYS>
        </COMPU-METHOD>
        <COMPU-METHOD>
          <SHORT-NAME>Mcu_17_Gtm_TimerEnTriggerType</SHORT-NAME>
          <CATEGORY>TEXTTABLE</CATEGORY>
          <COMPU-INTERNAL-TO-PHYS>
            <COMPU-SCALES>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>0-MCU_NOCHANGE_ON_TRIGGER</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>1-MCU_DISABLE_ON_TRIGGER</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>2-MCU_ENABLE_ON_TRIGGER</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
            </COMPU-SCALES>
          </COMPU-INTERNAL-TO-PHYS>
        </COMPU-METHOD>
        <COMPU-METHOD>
          <SHORT-NAME>Mcu_17_Gtm_TimerOutputEnableType</SHORT-NAME>
          <CATEGORY>TEXTTABLE</CATEGORY>
          <COMPU-INTERNAL-TO-PHYS>
            <COMPU-SCALES>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>0-MCU_GTM_TIMER_OUT_DISABLE</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>1-MCU_GTM_TIMER_OUT_ENABLE</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
            </COMPU-SCALES>
          </COMPU-INTERNAL-TO-PHYS>
        </COMPU-METHOD>
        <COMPU-METHOD>
          <SHORT-NAME>Mcu_17_Gtm_TimerOutputEnTriggerType</SHORT-NAME>
          <CATEGORY>TEXTTABLE</CATEGORY>
          <COMPU-INTERNAL-TO-PHYS>
            <COMPU-SCALES>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>0-MCU_NOCHANGE_OUT_ON_TRIGGER</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>1-MCU_DISABLE_OUT_ON_TRIGGER</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>2-MCU_ENABLE_OUT_ON_TRIGGER</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
            </COMPU-SCALES>
          </COMPU-INTERNAL-TO-PHYS>
        </COMPU-METHOD>
        <COMPU-METHOD>
          <SHORT-NAME>Mcu_PllStatusType</SHORT-NAME>
          <CATEGORY>TEXTTABLE</CATEGORY>
          <COMPU-INTERNAL-TO-PHYS>
            <COMPU-SCALES>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>0 - MCU_PLL_LOCKED</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>1 - MCU_PLL_UNLOCKED</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>2 - MCU_PLL_STATUS_UNDEFINED</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
            </COMPU-SCALES>
          </COMPU-INTERNAL-TO-PHYS>
        </COMPU-METHOD>
        <COMPU-METHOD>
          <SHORT-NAME>Mcu_ResetType</SHORT-NAME>
          <CATEGORY>TEXTTABLE</CATEGORY>
          <COMPU-INTERNAL-TO-PHYS>
            <COMPU-SCALES>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>0 - MCU_ESR0_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>1 - MCU_ESR1_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>2 - MCU_SMU_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>3 - MCU_SW_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>4 - MCU_STM0_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>5 - MCU_STM1_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>6 - MCU_STM2_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>7 - MCU_STM3_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>8 - MCU_STM4_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>9 - MCU_STM5_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>10 - MCU_POWER_ON_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>11 - MCU_CB0_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>12 - MCU_CB1_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>13 - MCU_CB3_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>14 - MCU_EVRC_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>15 - MCU_EVR33_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>16 - MCU_SUPPLY_WDOG_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>17 - MCU_STBYR_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>18 - MCU_LBIST_RESET</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>254 - MCU_RESET_MULTIPLE</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>255 - MCU_RESET_UNDEFINED</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
            </COMPU-SCALES>
          </COMPU-INTERNAL-TO-PHYS>
        </COMPU-METHOD>
        <COMPU-METHOD>
          <SHORT-NAME>Mcu_17_Gtm_TimerOutType</SHORT-NAME>
          <CATEGORY>TEXTTABLE</CATEGORY>
          <COMPU-INTERNAL-TO-PHYS>
            <COMPU-SCALES>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>0 - MCU_GTM_TIMER_TOM</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>1 - MCU_GTM_TIMER_ATOM</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
            </COMPU-SCALES>
          </COMPU-INTERNAL-TO-PHYS>
        </COMPU-METHOD>
        <COMPU-METHOD>
          <SHORT-NAME>Mcu_17_Gtm_TimerStatusType</SHORT-NAME>
          <CATEGORY>TEXTTABLE</CATEGORY>
          <COMPU-INTERNAL-TO-PHYS>
            <COMPU-SCALES>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>0 - MCU_GTM_TIMER_STOPPED</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
              <COMPU-SCALE>
                <LOWER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <UPPER-LIMIT INTERVAL-TYPE="CLOSED"/>
                <COMPU-CONST>
                  <VT>1 - MCU_GTM_TIMER_RUNNING</VT>
                </COMPU-CONST>
              </COMPU-SCALE>
            </COMPU-SCALES>
          </COMPU-INTERNAL-TO-PHYS>
        </COMPU-METHOD>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AR-PACKAGE>
</AR-PACKAGES>
</AUTOSAR>
