=======================================================================
CYGWIN 2.8.0 LICENSE (cygwin1.dll)
=======================================================================
THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESSED OR
IMPLIED WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.

Unless stated otherwise, the sources under the cygwin subdirectory,
as well as the sources under the cygserver subdirectory linked into
the Cygwin DLL, are licensed under the Lesser Gnu Public License,
version 3 or (at your option) any later version (LGPLv3+).  See the
COPYING.LIB file for the exact wording of that license.

Unless stated otherwise, the sources under the cygserver subdir not
linked into the Cygwin DLL, as well as the sources under the lsaauth
and the utils subdirectories are licensed under the Gnu Public License,
version 3 or (at your option) any later version (GPLv3+).  See the
COPYING file for the exact wording of that license. 

Parts of the sources in any subdirectory are licensed using a BSD-like
license.  The affected source files contain explicit copyright notices
to that effect.

Linking Exception:

  As a special exception, the copyright holders of the Cygwin library
  grant you additional permission to link libcygwin.a, crt0.o, and
  gcrt0.o with independent modules to produce an executable, and to
  convey the resulting executable under terms of your choice, without
  any need to comply with the conditions of LGPLv3 section 4. An
  independent module is a module which is not itself based on the
  Cygwin library.


=======================================================================
CYGWIN 1.xx LICENSE (Other DLLs and Exes)
=======================================================================

--------------------------------------------------------------------------
This program is free software; you can redistribute it and/or modify it
under the terms of the GNU General Public License (GPL) as published by
the Free Software Foundation; either version 2 of the License, or (at
your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
--------------------------------------------------------------------------

			*** NOTE ***

In accordance with section 10 of the GPL, Red Hat permits programs whose
sources are distributed under a license that complies with the Open
Source definition to be linked with libcygwin.a without libcygwin.a
itself causing the resulting program to be covered by the GNU GPL.

This means that you can port an Open Source(tm) application to cygwin,
and distribute that executable as if it didn't include a copy of
libcygwin.a linked into it.  Note that this does not apply to the cygwin
DLL itself.  If you distribute a (possibly modified) version of the DLL
you must adhere to the terms of the GPL, i.e., you must provide sources
for the cygwin DLL.

See http://www.opensource.org/osd.html for the precise Open Source
Definition referenced above.

Red Hat sells a special Cygwin License for customers who are unable to
provide their application in open source code form.  For more
information, please see: http://www.redhat.com/software/tools/cygwin/,
or call 866-2REDHAT ext.  3007

