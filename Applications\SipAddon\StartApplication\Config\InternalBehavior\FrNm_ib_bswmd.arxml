<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <ELEMENTS>
        <BSW-IMPLEMENTATION UUID="eda7af4f-25d7-482e-bda1-863612107f63">
          <SHORT-NAME>FrNm_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>8.00.00</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
        </BSW-IMPLEMENTATION>
      </ELEMENTS>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="0dd0c90b-8d3f-4c1c-b6f9-3ea86d00034f">
          <SHORT-NAME>FrNm_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="64d06f06-b453-4968-aa9b-2009c4a03272">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="4119ceb9-2e0e-43f8-8cda-e8ca59f563e6">
                  <SHORT-NAME>FrNm</SHORT-NAME>
                  <PROVIDED-ENTRYS>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/FrNm_ib_bswmd/BswModuleDescriptions/FrNm_MainFunction_0</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  </PROVIDED-ENTRYS>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="c6eb2153-4e43-4651-b640-b181fa0199f6">
                      <SHORT-NAME>FrNmBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="f6625014-3381-4da3-8c91-094709c81496">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_0</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Protects the global Pn Cluster requests from being modified from FrNm_RxIndication or FrNm_StartupError while copying to temporary buffer.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="4f9d80ea-d2ca-4437-a8d5-c459b522d833">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_1</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Protects the global TX message data from being modified while copying to the SDU.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="88dc0753-3727-42c0-9a34-8f87553a412c">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_2</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Protects the global TX message buffer from being read while user data is written to it.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="5ee4c9ae-728c-4c9c-9ab7-df58d39b0831">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_3</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Protects the global TX message buffer from being read while user data is written to it.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="d3ecf2b3-da16-4223-a782-94e18e950d39">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_4</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Ensures that the global RX message buffer is not read while data is written to it.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="2b04f3d9-41b3-41b1-819a-142b0a7d0f85">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_5</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Ensures that the global RX message buffer is not read while data is written to it.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="87328298-3cfe-4c99-ab98-e13393ac26d9">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_6</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Ensures that the global RX message buffer is not written while user data is read from it.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="8d9571c8-a720-4287-b8ed-adc0ba6d352a">
                          <SHORT-NAME>FrNm_MainFunction_0</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/FrNm_ib_bswmd/BswModuleDescriptions/FrNm_MainFunction_0</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="915d272f-f9aa-4fb2-ac98-27915d105bb9">
                          <SHORT-NAME>FrNm_MainFunction_0TimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/FrNm_ib_bswmd/BswModuleDescriptions/FrNm/FrNmBehavior/FrNm_MainFunction_0</STARTS-ON-EVENT-REF>
                          <PERIOD>0.005</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="6de0868e-84b6-4a57-9dea-25f1f36abe31">
                  <SHORT-NAME>FrNm_MainFunction_0</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="fa0ca78e-e887-4b52-8f3b-e1dc746e2b7b">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
