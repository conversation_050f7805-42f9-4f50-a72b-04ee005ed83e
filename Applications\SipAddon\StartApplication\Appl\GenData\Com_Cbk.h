/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Com
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: Com_Cbk.h
 *   Generation Time: 2025-08-05 10:37:17
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

/**********************************************************************************************************************
 * WARNING: This code has been generated with reduced-severity errors. 
 * The created output files contain errors that have been ignored. Usage of the created files can lead to unpredictable behavior of the embedded code.
 * Usage of the created files happens at own risk!
 * 
 * [Warning] COM02205 - Inconsistent signal layout. 
 * - [Reduced Severity due to User-Defined Parameter] /ActiveEcuC/EcuC/EcucPduCollection/PDU_nm_MyECU_Fr_ae963333_Tx[0:PduLength](value=6) of /ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx cannot hold contained ComSignals / ComGroupSignals.
 * 
 * Exceeding signals:
 * /ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx
 * Erroneous configuration elements:
 * /ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx[0:ComBitPosition](value=16) (DefRef: /MICROSAR/Com/ComConfig/ComSignal/ComBitPosition)
 * /ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx[0:ComSignalLength](value=6) (DefRef: /MICROSAR/Com/ComConfig/ComSignal/ComSignalLength)
 * /ActiveEcuC/EcuC/EcucPduCollection/PDU_nm_MyECU_Fr_ae963333_Tx[0:PduLength](value=6) (DefRef: /MICROSAR/EcuC/EcucPduCollection/Pdu/PduLength)
 * /ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx[0:ComIPduSignalRef](value=/ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx) (DefRef: /MICROSAR/Com/ComConfig/ComIPdu/ComIPduSignalRef)
 * /ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx[0:ComSignalEndianness](value=OPAQUE) (DefRef: /MICROSAR/Com/ComConfig/ComSignal/ComSignalEndianness)
 * /ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx[0:ComSignalType](value=UINT8_N) (DefRef: /MICROSAR/Com/ComConfig/ComSignal/ComSignalType)
 *********************************************************************************************************************/

#if !defined (COM_CBK_H)
# define COM_CBK_H

/**********************************************************************************************************************
  MISRA / PClint JUSTIFICATIONS
**********************************************************************************************************************/

/**********************************************************************************************************************
  INCLUDES
**********************************************************************************************************************/
#include "Com_Cot.h"

/**********************************************************************************************************************
  GLOBAL CONSTANT MACROS
**********************************************************************************************************************/



/**
 * \defgroup ComHandleIdsComRxPdu Handle IDs of handle space ComRxPdu.
 * \brief Rx Pdus
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define ComConf_ComIPdu_Frame_LinTr_RearECU_oLIN00_22758e6f_Rx        0u
#define ComConf_ComIPdu_Frame_LinTr_Slave3_oLIN00_bac14905_Rx         1u
#define ComConf_ComIPdu_PDU_Dummy_RearECU_1e8d611e_Rx                 2u
#define ComConf_ComIPdu_PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx       3u
#define ComConf_ComIPdu_msg_RxCycle100_0_oCAN_1e247d16_Rx             4u
#define ComConf_ComIPdu_msg_RxCycle500_20_oCAN_a691adb3_Rx            5u
#define ComConf_ComIPdu_msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx 6u
#define ComConf_ComIPdu_msg_RxEvent_20_oCAN_a1df81ad_Rx               7u
#define ComConf_ComIPdu_msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx       8u
#define ComConf_ComIPdu_pdu_RxDyn_64_5b4495a3_Rx                      9u
#define ComConf_ComIPdu_pdu_RxStat_10_6aaa637c_Rx                     10u
#define ComConf_ComIPdu_pdu_RxStat_30_589c01fe_Rx                     11u
/**\} */

/**
 * \defgroup ComHandleIdsComTxPdu Handle IDs of handle space ComTxPdu.
 * \brief Tx Pdus
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define ComConf_ComIPdu_Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx         0u
#define ComConf_ComIPdu_Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx          1u
#define ComConf_ComIPdu_PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx         2u
#define ComConf_ComIPdu_PDU_Transmit_MyECU_05398c7a_Tx                3u
#define ComConf_ComIPdu_PDU_nm_MyECU_Fr_ae963333_Tx                   4u
#define ComConf_ComIPdu_msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx       5u
#define ComConf_ComIPdu_msg_TxCycle10_0_oCAN_85bf3e37_Tx              6u
#define ComConf_ComIPdu_msg_TxCycle1000_10_oCAN_d74aed68_Tx           7u
#define ComConf_ComIPdu_msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx 8u
#define ComConf_ComIPdu_msg_TxEvent_10_oCAN_b2cd4fc2_Tx               9u
#define ComConf_ComIPdu_msg_nm_MyECU_oCAN_c97b60cc_Tx                 10u
#define ComConf_ComIPdu_pdu_TxDyn_16_3c646bcf_Tx                      11u
#define ComConf_ComIPdu_pdu_TxDyn_64_9d2b9c24_Tx                      12u
#define ComConf_ComIPdu_pdu_TxStat_40_64c7eeb3_Tx                     13u
#define ComConf_ComIPdu_pdu_TxStat_64_519c4828_Tx                     14u
/**\} */

/**********************************************************************************************************************
  GLOBAL FUNCTION MACROS
**********************************************************************************************************************/

/**********************************************************************************************************************
  GLOBAL DATA TYPES AND STRUCTURES
**********************************************************************************************************************/

/**********************************************************************************************************************
  GLOBAL DATA PROTOTYPES
**********************************************************************************************************************/

/**********************************************************************************************************************
  GLOBAL FUNCTION PROTOTYPES
**********************************************************************************************************************/
#define COM_START_SEC_CODE
#include "MemMap.h"    /* PRQA S 5087 1 */ /*MD_MSR_MemMap */
/**********************************************************************************************************************
  Com_RxIndication
**********************************************************************************************************************/
/** \brief        This function is called by the lower layer after an I-PDU has been received.
    \param[in]    RxPduId      ID of AUTOSAR COM I-PDU that has been received. Identifies the data that has been received.
                               Range: 0..(maximum number of I-PDU IDs received by AUTOSAR COM) - 1
    \param[in]    PduInfoPtr   Payload information of the received I-PDU (pointer to data and data length).
    \return       none
    \context      The function can be called on interrupt and task level. It is not allowed to use CAT1 interrupts with Rte (BSW00326]). Due to this, the interrupt context shall be configured to a CAT2 interrupt if an Rte is used.
    \synchronous  TRUE
    \reentrant    TRUE, for different Handles
    \trace        SPEC-2737026
    \note         The function is called by the lower layer.
**********************************************************************************************************************/
FUNC(void, COM_CODE) Com_RxIndication(PduIdType RxPduId, P2CONST(PduInfoType, AUTOMATIC, COM_APPL_DATA) PduInfoPtr);


/**********************************************************************************************************************
  Com_TriggerTransmit
**********************************************************************************************************************/
/** \brief          This function is called by the lower layer when an AUTOSAR COM I-PDU shall be transmitted.
                    Within this function, AUTOSAR COM shall copy the contents of its I-PDU transmit buffer
                    to the L-PDU buffer given by SduPtr.
                    Use case:
                    This function is used e.g. by the LIN Master for sending out a LIN frame. In this case, the trigger transmit
                    can be initiated by the Master schedule table itself or a received LIN header.
                    This function is also used by the FlexRay Interface for requesting PDUs to be sent in static part
                    (synchronous to the FlexRay global time). Once the I-PDU has been successfully sent by the lower layer
                    (PDU-Router), the lower layer must call Com_TxConfirmation().
    \param[in]      TxPduId       ID of AUTOSAR COM I-PDU that is requested to be transmitted by AUTOSAR COM.
    \param[in,out]  PduInfoPtr    Contains a pointer to a buffer (SduDataPtr) where the SDU
                                  data shall be copied to, and the available buffer size in SduLengh.
                                  On return, the service will indicate the length of the copied SDU
                                  data in SduLength.
    \return         E_OK          SDU has been copied and SduLength indicates the number of copied bytes.
    \return         E_NOT_OK      No data has been copied, because
                                  Com is not initialized
                                  or TxPduId is not valid
                                  or PduInfoPtr is NULL_PTR
                                  or SduDataPtr is NULL_PTR
                                  or SduLength is too small.
    \context        TASK|ISR2
    \synchronous    TRUE
    \reentrant      TRUE, for different Handles
    \trace          SPEC-2737022, SPEC-2737023, SPEC-2737024
    \note           The function is called by the lower layer.
**********************************************************************************************************************/
FUNC(Std_ReturnType, COM_CODE) Com_TriggerTransmit(PduIdType TxPduId, P2VAR(PduInfoType, AUTOMATIC, COM_APPL_VAR) PduInfoPtr);


#define COM_STOP_SEC_CODE
#include "MemMap.h"    /* PRQA S 5087 1 */ /* MD_MSR_MemMap */

#endif  /* COM_CBK_H */
/**********************************************************************************************************************
  END OF FILE: Com_Cbk.h
**********************************************************************************************************************/

