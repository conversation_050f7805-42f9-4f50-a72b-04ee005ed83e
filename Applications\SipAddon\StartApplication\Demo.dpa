<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ProjectAssistant Version="5.21.40 SP2">
    <General>
        <Name>Demo</Name>
        <Version>1.0</Version>
        <Author>visvga</Author>
    </General>
    <Environment>
        <Platform>Canoeemu</Platform>
        <Derivative UUID="D3FC3AD7-D44D-4CD4-99E0-D5F117D1FF44">TC377T</Derivative>
        <Compiler UUID="8DB6C5C5-6807-4601-B7B7-68A4A5B277F6">Tasking</Compiler>
        <SipIds>
            <SipId>CBD2000456_D02</SipId>
        </SipIds>
        <TargetType>Real Target</TargetType>
        <UseCases/>
        <PostBuildLoadableSupport>false</PostBuildLoadableSupport>
        <PostBuildSelectableSupport>false</PostBuildSelectableSupport>
        <ModuleSpecificDerivatives/>
    </Environment>
    <Folders>
        <ECUC>.\Config\ECUC</ECUC>
        <GenData>.\Appl\GenData</GenData>
        <GenDataVtt>.\Appl\GenDataVtt</GenDataVtt>
        <Source>.\Appl\Source</Source>
        <ServiceComponents>.\Config\ServiceComponents</ServiceComponents>
        <Logs>.\Log</Logs>
        <SIP>.\..\..\..</SIP>
        <StartMenu></StartMenu>
        <ApplicationComponentFolders>
            <ApplicationComponentFolder>.\Config\ApplicationComponents</ApplicationComponentFolder>
        </ApplicationComponentFolders>
        <BswInternalBehaviour>.\Config\InternalBehavior</BswInternalBehaviour>
        <McData>.\Config\McData</McData>
        <DefinitionRestriction>.\DefRestrict</DefinitionRestriction>
        <AUTOSAR>.\Config\AUTOSAR</AUTOSAR>
    </Folders>
    <Tools>
        <DEV>C:\Program Files (x86)\Vector DaVinci Developer 4.4 (SP2)\Bin\DaVinciDEV.exe</DEV>
        <LegacyConverter Version="********"/>
        <DDM Version="5.21.40 SP2"/>
    </Tools>
    <Input>
        <ECUEX>Config\System\SystemExtract.arxml</ECUEX>
        <Options>
            <IgnoreUuidsSystemDescriptionFiles>false</IgnoreUuidsSystemDescriptionFiles>
            <IgnoreUuidsStandardConfigurationFiles>false</IgnoreUuidsStandardConfigurationFiles>
            <GenerateUpdateReport>true</GenerateUpdateReport>
            <GenerateXmlUpdateReport>false</GenerateXmlUpdateReport>
        </Options>
    </Input>
    <References>
        <DVWorkspace>.\Config\Developer\Demo.dcf</DVWorkspace>
        <FlatMap>Config\System\FlatMap.arxml</FlatMap>
        <FlatECUEX>Config\System\FlatExtract.arxml</FlatECUEX>
        <OEMCommunicationExtract>Config\System\Communication.arxml</OEMCommunicationExtract>
        <EcucFileReferences/>
        <DiagECUC>Config\System\DiagnosticsDescriptionData.arxml</DiagECUC>
    </References>
    <EcucSplitter>
        <Configuration>Config\ECUC\Demo.ecuc.arxml</Configuration>
        <Splitter File=".\Config\ECUC\Demo_Com_ecuc.arxml">
            <Module Name="Com"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Os_ecuc.arxml">
            <Module Name="Os"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_LinIf_ecuc.arxml">
            <Module Name="LinIf"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Dem_ecuc.arxml">
            <Module Name="Dem"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_CanSM_ecuc.arxml">
            <Module Name="CanSM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_LinTp_ecuc.arxml">
            <Module Name="LinTp"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_CanTp_ecuc.arxml">
            <Module Name="CanTp"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_ComXf_ecuc.arxml">
            <Module Name="ComXf"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_LinSM_ecuc.arxml">
            <Module Name="LinSM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_EcuC_ecuc.arxml">
            <Module Name="EcuC"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_BswM_ecuc.arxml">
            <Module Name="BswM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_FrIf_ecuc.arxml">
            <Module Name="FrIf"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_vSet_ecuc.arxml">
            <Module Name="vSet"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Fr_ecuc.arxml">
            <Module Name="Fr"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Rte_ecuc.arxml">
            <Module Name="Rte"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_EcuM_ecuc.arxml">
            <Module Name="EcuM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_E2EXf_ecuc.arxml">
            <Module Name="E2EXf"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_LinNm_ecuc.arxml">
            <Module Name="LinNm"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_CanNm_ecuc.arxml">
            <Module Name="CanNm"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_PduR_ecuc.arxml">
            <Module Name="PduR"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Dcm_ecuc.arxml">
            <Module Name="Dcm"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Lin_ecuc.arxml">
            <Module Name="Lin"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Nm_ecuc.arxml">
            <Module Name="Nm"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Can_ecuc.arxml">
            <Module Name="Can"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_CanIf_ecuc.arxml">
            <Module Name="CanIf"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Det_ecuc.arxml">
            <Module Name="Det"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_ComM_ecuc.arxml">
            <Module Name="ComM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_FrNm_ecuc.arxml">
            <Module Name="FrNm"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_FrSM_ecuc.arxml">
            <Module Name="FrSM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_vLinkGen_ecuc.arxml">
            <Module Name="vLinkGen"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_MemIf_ecuc.arxml">
            <Module Name="MemIf"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_vBRS_ecuc.arxml">
            <Module Name="vBRS"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_ResourceM_ecuc.arxml">
            <Module Name="ResourceM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_vBaseEnv_ecuc.arxml">
            <Module Name="vBaseEnv"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_NvM_ecuc.arxml">
            <Module Name="NvM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_vItaSip_ecuc.arxml">
            <Module Name="vItaSip"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Cdd_ecuc.arxml">
            <Module Name="Cdd"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Fee_ecuc.arxml">
            <Module Name="Fee"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Fls_ecuc.arxml">
            <Module Name="Fls"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Port_ecuc.arxml">
            <Module Name="Port"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_McalLib_ecuc.arxml">
            <Module Name="McalLib"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Crc_ecuc.arxml">
            <Module Name="Crc"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Mcu_ecuc.arxml">
            <Module Name="Mcu"/>
        </Splitter>
        <OwnFolderForEachSplitter>false</OwnFolderForEachSplitter>
        <OwnFileForEachInstance>false</OwnFileForEachInstance>
    </EcucSplitter>
    <Display LastVersion="5.21.40 SP2">
        <FileSet Id="" CS="2080fbd">
            <File Order="1" EcuInstance="MyECU" Hash="72a635c7ca4e9be2ce7ff9cb19906ab8" FileCategory="legacy_communication_data">$(DpaProjectFolder)\input_files\FlexRay.xml</File>
            <File Order="2" EcuInstance="MyECU" Hash="685749b5cf70c6d5ca6ab2299bf26737" FileCategory="legacy_communication_data">$(DpaProjectFolder)\input_files\LIN_MyECU.ldf</File>
            <File Order="0" EcuInstance="MyECU" Hash="9bb58a176021d039506a9f5ad22f701b" FileCategory="legacy_communication_data">$(DpaProjectFolder)\input_files\CAN_FD_Mode2.dbc</File>
            <Diagnostic>
                <Description Order="3" Hash="20400a9165aca2a5d065e23f011dee8d">$(DpaProjectFolder)\input_files\MyEcu.cdd</Description>
                <Ecu>MyEcu</Ecu>
                <Variant>CommonDiagnostics</Variant>
                <UseLegacySignalImport>true</UseLegacySignalImport>
            </Diagnostic>
        </FileSet>
        <Merge/>
        <SelectiveUpdate Active="false" CS="ecd6502d"/>
    </Display>
    <ECUC>
        <Active RootPackageName="/ActiveEcuC/ActiveEcuC">Config\ECUC\Demo.ecuc.arxml</Active>
        <Derived RootPackageName="/InitialEcuC/InitialEcuC">Config\ECUC\Demo.ecuc.Initial.arxml</Derived>
    </ECUC>
    <PostBuildLoadable RTEDataFreezeChecksum="" CurrentConfigurationPhase="PRE_COMPILE"/>
    <DEVSettings>
        <SelectiveImport>All</SelectiveImport>
        <ObjectLocking>true</ObjectLocking>
        <ImportModePreset>true</ImportModePreset>
    </DEVSettings>
    <ToolSettings>
        <Generators>
            <Settings Name="com.vector.cfg.gen.core.genusersettings">
                <Settings Name="com.vector.cfg.gen.Tresos_proxy">
                    <Settings Name="General">
                        <Setting Value="System" Name="forcearchitecture"/>
                        <Setting Value="1648,1055,1072" Name="ignoretresosmsgids"/>
                        <Setting Value="true" Name="performanceoptimization"/>
                        <Setting Value="false" Name="tresosverification"/>
                    </Settings>
                </Settings>
            </Settings>
            <Settings Name="com.vector.cfg.gui.core.generators.ExtGenStepOrder">
                <Setting Value="INTERNAL_GENERATION" Name="Order"/>
            </Settings>
            <Settings Name="com.vector.cfg.gui.core.generators.GeneratedModules"/>
            <Settings Name="com.vector.cfg.gui.core.generators.GenerationTarget">
                <Setting Value="Real Target" Name="TargetType"/>
            </Settings>
        </Generators>
        <Misc>
            <Settings Name="com.vector.cfg.gen.core.bswmdmigration.internal.service.BswImplVersionPersistor">
                <Settings Name="|MICROSAR|AvTp">
                    <Setting Value="6.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|BswM">
                    <Setting Value="13.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanIf">
                    <Setting Value="6.23.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanNm">
                    <Setting Value="10.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanSM">
                    <Setting Value="3.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanTSyn">
                    <Setting Value="5.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanTp">
                    <Setting Value="3.5.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanTrcv_GenericCan|CanTrcv">
                    <Setting Value="4.2.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanTrcv_Tja1040|CanTrcv">
                    <Setting Value="4.3.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanTrcv_Tja1043|CanTrcv">
                    <Setting Value="4.4.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanTrcv_Tle9255|CanTrcv">
                    <Setting Value="1.1.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Can_Mpc5700Mcan|Can">
                    <Setting Value="5.4.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Com">
                    <Setting Value="18.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|ComM">
                    <Setting Value="13.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|ComXf">
                    <Setting Value="1.14.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Crc">
                    <Setting Value="5.5.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CryIf">
                    <Setting Value="5.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Crypto_30_LibCv|Crypto">
                    <Setting Value="8.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Crypto_30_vHsm|Crypto">
                    <Setting Value="2.3.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Csm">
                    <Setting Value="5.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Dcm">
                    <Setting Value="13.5.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Dem">
                    <Setting Value="19.2.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Det">
                    <Setting Value="11.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|DiagXf">
                    <Setting Value="1.10.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|DoIP">
                    <Setting Value="9.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|E2EXf">
                    <Setting Value="1.9.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EcuC">
                    <Setting Value="1.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EcuM">
                    <Setting Value="10.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EthIf">
                    <Setting Value="13.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EthSM">
                    <Setting Value="4.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EthSwt_88Q5050|EthSwt">
                    <Setting Value="6.4.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EthTSyn">
                    <Setting Value="12.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EthTrcv_88Q1010|EthTrcv">
                    <Setting Value="3.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EthTrcv_88Q2112|EthTrcv">
                    <Setting Value="3.1.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EthTrcv_Ethmii|EthTrcv">
                    <Setting Value="3.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Eth_Tc3xx|Eth">
                    <Setting Value="4.1.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Etm">
                    <Setting Value="8.0.3" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|FiM">
                    <Setting Value="7.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|FrArTp">
                    <Setting Value="1.3.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|FrIf">
                    <Setting Value="5.2.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|FrNm">
                    <Setting Value="8.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|FrSM">
                    <Setting Value="3.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|FrTSyn">
                    <Setting Value="5.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|FrTp">
                    <Setting Value="3.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|FrTrcv_30_Tja1082|FrTrcv">
                    <Setting Value="1.0.2" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Fr_TricoreEray|Fr">
                    <Setting Value="5.0.3" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|FvM">
                    <Setting Value="7.2.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|IoHwAb">
                    <Setting Value="3.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|IpduM">
                    <Setting Value="9.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|J1939Dcm">
                    <Setting Value="12.4.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|J1939Nm">
                    <Setting Value="6.0.2" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|J1939Rm">
                    <Setting Value="3.0.2" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|J1939Tp">
                    <Setting Value="3.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|KeyM">
                    <Setting Value="2.2.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|LdCom">
                    <Setting Value="3.1.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|LinIf">
                    <Setting Value="8.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|LinNm">
                    <Setting Value="2.3.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|LinSM">
                    <Setting Value="7.0.2" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|LinTp">
                    <Setting Value="8.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|LinTrcv_30_Tle7259|LinTrcv">
                    <Setting Value="6.4.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Lin_Tricore|Lin">
                    <Setting Value="15.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|MemIf">
                    <Setting Value="3.4.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Mirror">
                    <Setting Value="4.0.3" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Nm">
                    <Setting Value="13.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|NvM">
                    <Setting Value="6.2.3" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Os">
                    <Setting Value="2.41.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|PduR">
                    <Setting Value="15.4.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Rte">
                    <Setting Value="4.22.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Sd">
                    <Setting Value="9.2.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|SecOC">
                    <Setting Value="10.1.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|SoAd">
                    <Setting Value="16.3.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|SomeIpTp">
                    <Setting Value="2.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|SomeIpXf">
                    <Setting Value="1.14.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Srp">
                    <Setting Value="6.1.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|StbM">
                    <Setting Value="7.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|TcpIp">
                    <Setting Value="14.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|UdpNm">
                    <Setting Value="5.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|WdgIf">
                    <Setting Value="5.4.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|WdgM">
                    <Setting Value="5.5.2" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|vBRS">
                    <Setting Value="1.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|vBaseEnv">
                    <Setting Value="1.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|vDem42">
                    <Setting Value="1.2.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|vItaSip">
                    <Setting Value="6.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|vLinkGen">
                    <Setting Value="2.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|vSecPrim">
                    <Setting Value="4.0.1" Name="BswImplSwVersion"/>
                </Settings>
            </Settings>
            <Settings Name="com.vector.cfg.gen.core.gencore.internal.aov.AutomaticOnDemandValidationConfigurationService">
                <Setting Value="rO0ABXNyAGpjb20udmVjdG9yLmNmZy5nZW4uY29yZS5nZW5jb3JlLmludGVybmFsLmFvdi5BdXRv&#xA;bWF0aWNPbkRlbWFuZFZhbGlkYXRpb25Db25maWd1cmF0aW9uU2VydmljZSRQZXJzaXN0ZWREYXRh&#xA;pcjrmQ6cuakCAAJaAAlhY3RpdmF0ZWRMABJkZWFjdGl2YXRlZE1vZHVsZXN0ABZMamF2YS91dGls&#xA;L0NvbGxlY3Rpb247eHABc3IANmNvbS5nb29nbGUuY29tbW9uLmNvbGxlY3QuSW1tdXRhYmxlTGlz&#xA;dCRTZXJpYWxpemVkRm9ybQAAAAAAAAAAAgABWwAIZWxlbWVudHN0ABNbTGphdmEvbGFuZy9PYmpl&#xA;Y3Q7eHB1cgATW0xqYXZhLmxhbmcuT2JqZWN0O5DOWJ8QcylsAgAAeHAAAAAA" Name="Data"/>
            </Settings>
            <Settings Name="com.vector.cfg.gui.pse.pages.generators">
                <Settings Name="BuildVipProject"/>
                <Settings Name="CustomWorkflowExecution"/>
            </Settings>
            <Settings Name="com.vector.cfg.persistency.internal.folder">
                <Settings Name="TargetFolderManager"/>
            </Settings>
            <Settings Name="generationReportSettings"/>
        </Misc>
    </ToolSettings>
    <TopDownServiceConfiguration>
        <NvM>true</NvM>
    </TopDownServiceConfiguration>
    <Miscellaneous>
        <AmdGenerateDebugData>false</AmdGenerateDebugData>
        <AutomaticSyncSystemDescription>false</AutomaticSyncSystemDescription>
    </Miscellaneous>
    <SwctGeneration>
        <Component Name="StartApplication" GenerationEnabled="true"/>
    </SwctGeneration>
</ProjectAssistant>
