/*
 * Cdd_Cbk.c
 *
 *  Created on: 25.08.2020
 *      Author: visvga
 */
#include "CddLinTpStub_Cbk.h"
/*Start of LINTP Stubs*/

/**********************************************************************************************************************
  CDDLINTPSTUB_StartOfReception
**********************************************************************************************************************/
/** \brief       The function call indicates the reception start of a segmented PDU.
    \param[in]   id             id of the TP CddPduRUpperLayerRxPdu.
    \param[in]   info           Pointer to a PduInfoType structure containing the payload data
    \param[in]   TpSduLength    length of the entire the TP SDU that will be received.
    \param[out]  bufferSizePtr  length of the available receive buffer in Cdd.\n
                                This parameter is used e.g. in CanTp to calculate the Block Size (BS).
    \return      a BufReq_ReturnType constant of ComStackTypes.h.
    \pre         The Cdd is initialized and active.
    \context     This function can be called on interrupt and task level and has not to be interrupted by other\n
                 CDDLINTPSTUB_StartOfReception calls for the same id.
    \note        The function is called by the PduR.
**********************************************************************************************************************/
FUNC(BufReq_ReturnType, CDDLINTPSTUB_CODE) CddLinTpStub_StartOfReception(PduIdType id, P2VAR(PduInfoType, AUTOMATIC, CDDLINTPSTUB_APPL_DATA) info, PduLengthType TpSduLength, P2VAR(PduLengthType, AUTOMATIC, CDDLINTPSTUB_APPL_DATA) bufferSizePtr)
{
	BufReq_ReturnType result = BUFREQ_OK;

	return result;
}

/**********************************************************************************************************************
  CDDLINTPSTUB_CopyRxData
**********************************************************************************************************************/
/** \brief       This function is called to trigger the copy process of a segmented PDU.\n
                 The function can be called several times and\n
                 each call to this function copies parts of the received data.\n
    \param[in]   id             id of the TP CddPduRUpperLayerRxPdu.
    \param[in]   info           a PduInfoType pointing to the data to be copied in the Cdd data buffer.
    \param[out]  bufferSizePtr  available receive buffer after data has been copied.
    \return      a BufReq_ReturnType constant of ComStackTypes.h.
    \pre         The Cdd is initialized and active.
    \context     This function can be called on interrupt and task level and has not to be interrupted by other\n
                 CDDLINTPSTUB_CopyRxData calls for the same id.
    \note        The function is called by the PduR.
**********************************************************************************************************************/
FUNC(BufReq_ReturnType, CDDLINTPSTUB_CODE) CddLinTpStub_CopyRxData(PduIdType id, P2VAR(PduInfoType, AUTOMATIC, CDDLINTPSTUB_APPL_DATA) info, P2VAR(PduLengthType, AUTOMATIC, CDDLINTPSTUB_APPL_DATA) bufferSizePtr)
{
	BufReq_ReturnType result = BUFREQ_OK;

	return result;
}

/**********************************************************************************************************************
  CDDLINTPSTUB_TpRxIndication
**********************************************************************************************************************/
/** \brief       The function is called to indicate the complete receiption of a Cdd TP SDU
                 or to report an error that occurred during reception.
    \param[in]   id             id of the TP CddPduRUpperLayerRxPdu.
    \param[in]   result         a Std_ReturnType to indicate the result of the reception.
    \return      none
    \pre         The Cdd is initialized and active.
    \context     This function can be called on interrupt and task level and has not to be interrupted by other\n
                 CDDLINTPSTUB_TpRxIndication calls for the same id.
    \note        The function is called by the PduR.
**********************************************************************************************************************/
FUNC(void, CDDLINTPSTUB_CODE) CddLinTpStub_TpRxIndication(PduIdType id, Std_ReturnType result)
{

}
/**********************************************************************************************************************
  CDDLINTPSTUB_CopyTxData
**********************************************************************************************************************/
/** \brief       This function is called to request transmit data of a TP CddPduRUpperLayerTxPdu\n
                 The function can be called several times and\n
                 each call to this function copies the next part of the data to be transmitted.\n
    \param[in]   id             id of the TP CddPduRUpperLayerTxPdu.
    \param[in]   info           a PduInfoType pointing to the destination buffer.
    \param[in]   retry          NULL_PTR to indicate a successful copy process\n
                                or a RetryInfoType containing a TpDataStateType constant of ComStackTypes.h.
    \param       availableDataPtr   Indicates the remaining number of bytes that are available in the TX buffer.\n
                                availableDataPtr can be used by TP modules that support dynamic payload lengths\n
                                (e.g. Iso FrTp) to determine the size of the following CFs.
    \return      a BufReq_ReturnType constant of ComStackTypes.h.
    \pre         The Cdd is initialized and active.
    \context     This function can be called on interrupt and task level and has not to be interrupted by other\n
                 CDDLINTPSTUB_CopyTxData calls for the same id.
    \note        The function is called by the PduR.
**********************************************************************************************************************/
FUNC(BufReq_ReturnType, CDDLINTPSTUB_CODE) CddLinTpStub_CopyTxData(PduIdType id, P2VAR(PduInfoType, AUTOMATIC, CDDLINTPSTUB_APPL_DATA) info, P2VAR(RetryInfoType, AUTOMATIC, CDDLINTPSTUB_APPL_DATA) retry, P2VAR(PduLengthType, AUTOMATIC, CDDLINTPSTUB_APPL_DATA) availableDataPtr)
{
	BufReq_ReturnType result = BUFREQ_OK;

	return result;
}

/**********************************************************************************************************************
  CDDLINTPSTUB_TpTxConfirmation
**********************************************************************************************************************/
/** \brief       The function is called to confirm a successful transmission of a TP CddPduRUpperLayerTxPdu\n
                 or to report an error that occurred during transmission.
    \param[in]   id             id of the TP CddPduRUpperLayerTxPdu.
    \param[in]   result         a Std_ReturnType to indicate the result of the transmission.
    \return      none
    \pre         The Cdd is initialized and active.
    \context     This function can be called on interrupt and task level and has not to be interrupted by other\n
                 CDDLINTPSTUB_TpTxConfirmation calls for the same id.
    \note        The function is called by the PduR.
**********************************************************************************************************************/
FUNC(void, CDDLINTPSTUB_CODE) CddLinTpStub_TpTxConfirmation(PduIdType id, Std_ReturnType result)
{

}



