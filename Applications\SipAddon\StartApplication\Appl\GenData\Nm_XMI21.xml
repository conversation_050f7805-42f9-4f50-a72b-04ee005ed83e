<?xml version="1.0" encoding="UTF-8" standalone="no"?><xmi:XMI xmlns:xmi="http://schema.omg.org/spec/XMI/2.1" xmlns:uml="http://schema.omg.org/spec/UML/2.1" xmi:version="2.1"><xmi:Documentation exporter="Enterprise Architect" exporterVersion="6.5"/><xmi:Extension extender="Enterprise Architect" extenderID="6.5"><elements><element name="BusNmChIdxToBusNmIdxInds:ConstStruct" scope="public" xmi:idref="EAID_cc39598da802584fca05ef1d257515a2" xmi:type="uml:Object"><constraints><constraint description="There is no BusNmAssociationTable available if NM_OPTIMIZE_ONE_BUSNM_ON_A_CHANNEL == STD_ON" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/><constraint description="no Standard Bus Nm Specific Pdu Rx Indication / Generic Bus Nm Specific Pdu Rx Indication is configured in a NmBusType container" name="CUserPreCompileDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_643af246b11210f74f8fcb900bcbc93a"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ChannelConfig:ConstStruct" scope="public" xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081" xmi:type="uml:Object"><properties documentation="Structure for channel specific configuration parameters" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Macro Layer Enabled is ON" name="CUserPreCompileDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_5ae3f56741fe9d79ade64789be1e26e2"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="NmActiveCoordinatorOfChannelConfig" scope="Private" xmi:idref="EAID_c09d0974b7ff4c19b302aa8a7ee0ead7"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Coordinator Support is not enabled" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_NmActiveCoordinatorOfChannelConfigType"/></attribute><attribute name="NmChannelIdOfChannelConfig" scope="Private" xmi:idref="EAID_0f2adc3c303983713c487acfe5daab05"><Constraints/><properties changeability="frozen" collection="true" type="Nm_NmChannelIdOfChannelConfigType"/></attribute><attribute name="NmChannelSleepMasterOfChannelConfig" scope="Private" xmi:idref="EAID_c4a6ab42626e7a87d4c1b9b25621977a"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Coordinator Support Enabled is turned OFF or Channel Sleep Master is turned ON for all BusNms on every coordinated channel" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_NmChannelSleepMasterOfChannelConfigType"/></attribute><attribute name="NmContainsNmOsekOfChannelConfig" scope="Private" xmi:idref="EAID_ba3b1d97226b28502ed0e0497efbb006"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Wait bus sleep extension is not enabled or Coordinator Support is not enabled" type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the Nm_NmContainsNmOsekOfChannelConfig if all values are true" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_NmContainsNmOsekOfChannelConfigType"/></attribute><attribute name="NmPassiveModeEnabledOfChannelConfig" scope="Private" xmi:idref="EAID_b592ac4dbd2bd5d52e07067bf46262db"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Passive Mode Enabled is OFF on all channels" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_NmPassiveModeEnabledOfChannelConfigType"/></attribute><attribute name="NmShutdownDelayTimerOfChannelConfig" scope="Private" xmi:idref="EAID_739209e6c00b86a16b40c5294af34f34"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Coordinator Support is not enabled" type="Pre-condition"/><Constraint name="CNumericalArrayGenCondition" notes="deactivate the Nm_NmShutdownDelayTimerOfChannelConfig if all values are 1" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_NmShutdownDelayTimerOfChannelConfigType"/></attribute><attribute name="NmShutdownLimpHomeTimerOfChannelConfig" scope="Private" xmi:idref="EAID_e71c6586c0b6da2edd0d74148f014524"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Wait bus sleep extension is not enabled or Coordinator Support is not enabled" type="Pre-condition"/><Constraint name="CNumericalArrayGenCondition" notes="deactivate the Nm_NmShutdownLimpHomeTimerOfChannelConfig if all values are 0" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_NmShutdownLimpHomeTimerOfChannelConfigType"/></attribute><attribute name="NmShutdownNormalDelayTimerOfChannelConfig" scope="Private" xmi:idref="EAID_2d823a2032f9c483d41a3fa03025c416"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Wait bus sleep extension is not enabled or Coordinator Support is not enabled" type="Pre-condition"/><Constraint name="CNumericalArrayGenCondition" notes="deactivate the Nm_NmShutdownNormalDelayTimerOfChannelConfig if all values are 1" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_NmShutdownNormalDelayTimerOfChannelConfigType"/></attribute><attribute name="NmStateReportEnabledOfChannelConfig" scope="Private" xmi:idref="EAID_d6c1dfea6a4cc34bd83dbeddbba16fcb"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="StateReporting is not enabled" type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the Nm_NmStateReportEnabledOfChannelConfig if all values are true" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_NmStateReportEnabledOfChannelConfigType"/></attribute><attribute name="NmStateReportSignalRefOfChannelConfig" scope="Private" xmi:idref="EAID_f1975647dcf563082f1ed1dda83bee1b"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="StateReporting is not enabled" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Com_SignalIdType"/></attribute><attribute name="NmSynchronizingNetworkOfChannelConfig" scope="Private" xmi:idref="EAID_fb0c40fbcbdee1545a8374f226d65ce1"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Coordinator Support Enabled is turned OFF or Synchronizing Network is turned ON on every coordinated channel" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_NmSynchronizingNetworkOfChannelConfigType"/></attribute><attribute name="PduRxIndicationOfChannelConfig" scope="Private" xmi:idref="EAID_bc0915e1346833029fae25b2a97b06b4"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the Nm_PduRxIndicationOfChannelConfig if all values are NULL_PTR" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallbackFunctionWithPduInfoType"/></attribute></attributes></element><element name="CoordCluster:ConstStruct" scope="public" xmi:idref="EAID_bdd180dbda7c0e31c75275401471ca5f" xmi:type="uml:Object"><constraints><constraint description="Nm Gw Ext Enabled is turned OFF" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_a10da8c049a819f85d080a135b21ab7c"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="NmFunctionTable:ConstStruct" scope="public" xmi:idref="EAID_8f02352d39aa3ef33e67903f51750234" xmi:type="uml:Object"><constraints><constraint description="Macro Layer Enabled is ON" name="CUserPreCompileDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_5177caccf46d4fce8d11a9874ba7aa62"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="CheckRemoteSleepIndicationOfNmFunctionTable" scope="Private" xmi:idref="EAID_bdd1e0d72456b40cce0fef5e4e566ba8"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeGetData"/></attribute><attribute name="DisableCommunicationOfNmFunctionTable" scope="Private" xmi:idref="EAID_d37621e9ba8b074d557cfbddc16a3e0d"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeStandard"/></attribute><attribute name="EnableCommunicationOfNmFunctionTable" scope="Private" xmi:idref="EAID_bb9eabbec37749712409c7099bb8ae43"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeStandard"/></attribute><attribute name="GetLocalNodeIdentifierOfNmFunctionTable" scope="Private" xmi:idref="EAID_9a76036d0c55a742245285549be16bcf"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeGetData"/></attribute><attribute name="GetNodeIdentifierOfNmFunctionTable" scope="Private" xmi:idref="EAID_bacfab1b403f82abec791ac90bf224b6"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeGetData"/></attribute><attribute name="GetPduDataOfNmFunctionTable" scope="Private" xmi:idref="EAID_0911395d1c8b26872fbad96c662ce68a"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeGetData"/></attribute><attribute name="GetStateOfNmFunctionTable" scope="Private" xmi:idref="EAID_0c916fc2a0a414a5747573ab9d92f3c1"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeGetState"/></attribute><attribute name="GetUserDataOfNmFunctionTable" scope="Private" xmi:idref="EAID_b4a47481774e56c4a2c399768b0af350"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeGetData"/></attribute><attribute name="NetworkReleaseOfNmFunctionTable" scope="Private" xmi:idref="EAID_b1862129b4d8fe9f9974d67c2d84fb30"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeStandard"/></attribute><attribute name="NetworkRequestOfNmFunctionTable" scope="Private" xmi:idref="EAID_2a93504ccefe197a3b259beec685f7ba"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeStandard"/></attribute><attribute name="PassiveStartUpOfNmFunctionTable" scope="Private" xmi:idref="EAID_35c5b55adfece8d7c464d06548f44d5c"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeStandard"/></attribute><attribute name="RepeatMessageRequestOfNmFunctionTable" scope="Private" xmi:idref="EAID_fbeb708d4637e35398a81147ba9f4e2e"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeStandard"/></attribute><attribute name="RequestBusSynchronizationOfNmFunctionTable" scope="Private" xmi:idref="EAID_92a95d5526103411e55bebb14f335f88"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeStandard"/></attribute><attribute name="SetSleepReadyBitOfNmFunctionTable" scope="Private" xmi:idref="EAID_3f82f1e301149337b3e3ae7866df6f4a"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeSetSleepReady"/></attribute><attribute name="SetUserDataOfNmFunctionTable" scope="Private" xmi:idref="EAID_01332292cd005cfdd9ab2385db1dc8bb"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Function is not available due to pre-compile settings" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="Nm_CallBusFuncTypeSetUserData"/></attribute></attributes></element><element name="NmPartEcuMapping:ConstStruct" scope="public" xmi:idref="EAID_e6828a3ad1cf3440998acb767249e64a" xmi:type="uml:Object"><constraints><constraint description="Nm Gw Ext Enabled is turned OFF" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_e14587392dd2b5217d459bc9b1cfd3e4"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="MapPartEcuToActChOfNmPartEcuMapping" scope="Private" xmi:idref="EAID_0820cc8ef276df636515baa314d38bc4"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Nm Gw Ext Enabled is turned OFF" type="Pre-condition"/></Constraints><documentation value="Map PartEcu To Node Id number"/><properties changeability="frozen" collection="true" type="Nm_MapPartEcuToActChOfNmPartEcuMappingType"/></attribute></attributes></element><element name="SysToNmChInd:ConstArray" scope="public" xmi:idref="EAID_d984aba699c56246cc20c3f36e4024d4" xmi:type="uml:Object"><properties documentation="Channel indirection: System Channel handle to NM channel handle" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="SysToNmChInd" scope="Private" xmi:idref="EAID_15897a661a968e0f69afd1509c2375c9"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Channel Indirection is not needed because Macro Layer Enabled is ON or the number of system channels is equal to the number of NM channels in the VARIANT-PRE-COMPILE" type="Pre-condition"/></Constraints><documentation value="Channel indirection: System Channel handle to NM channel handle"/><properties changeability="frozen" collection="true" type="Nm_SysToNmChIndType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_347f3a06f2690ee2de9471e4edf83f81"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="CarWakeUpCallbackFctPtr:ConstVar" scope="public" xmi:idref="EAID_476d804a7cb2915f27a8f625ad601acb" xmi:type="uml:Object"><properties documentation="Car Wake Up Callback" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="CarWakeUpCallbackFctPtr" scope="Private" xmi:idref="EAID_5c79e79dc7245c417c46d798c5e4a959"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Macro Layer Enabled is ON or (Module variant is neither VARIANT-LINK-TIME nor VARIANT-POST-BUILD-LOADABLE and NmCarWakeUpRxEnabled is OFF)" type="Pre-condition"/><Constraint name="CNullPtrGenCondition" notes="deactivate the Nm_CarWakeUpCallbackFctPtr if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Car Wake Up Callback"/><properties changeability="frozen" type="Nm_CallbackFunction"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_c8aea864d41b9b092c3a80bcc1ceef3e"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="NmCoordinatorWithNmFiatC:ConstVar" scope="public" xmi:idref="EAID_f5ac4de6a340d6631abe49e90f03ae98" xmi:type="uml:Object"><properties documentation="Coordinator With NmFiatC" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="NmCoordinatorWithNmFiatC" scope="Private" xmi:idref="EAID_069fb0f40662d11cb6003160cb501289"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Coordinator Support Enabled is OFF or there is no coordinated channel with NmFiatC" type="Pre-condition"/><Constraint name="IndirectableFeatureDeactivationCondition" notes="No channel with NmFiatC exists that belongs to a coordinator cluster" type="Pre-condition"/><Constraint name="CNumericalArrayGenCondition" notes="deactivate the Nm_NmCoordinatorWithNmFiatC if all values are 0" type="Pre-condition"/></Constraints><documentation value="Coordinator With NmFiatC"/><properties changeability="frozen" type="Nm_NmCoordinatorWithNmFiatCType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_31ca97b5125ed64c6c95780f038d0151"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ChPrevBusNmState:VarArray" scope="public" xmi:idref="EAID_8bb4797e0bb26d355b66ebb8f2156f95" xmi:type="uml:Object"><properties documentation="Previous BusNm state for Channel" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ChPrevBusNmState" scope="Private" xmi:idref="EAID_f02fa8a3b303add4fd807c088adb3363"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Only one BusNm can be found on each channel or State Change Ind Enabled is OFF" type="Pre-condition"/></Constraints><documentation value="Previous BusNm state for Channel"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_80a1c275b5ef7bf28989fc5f9a29de1e"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ChRemSleepState:VarArray" scope="public" xmi:idref="EAID_143d2ecac320b3beb12a68bbd7bd19b7" xmi:type="uml:Object"><properties documentation="Remote Sleep State for Channel" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ChRemSleepState" scope="Private" xmi:idref="EAID_405eb0ea80a892c50aa9a7eaab03f82b"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Only one BusNm can be found on each channel or (Remote Sleep Ind Enabled is OFF and Coordinator Sync Support is OFF)" type="Pre-condition"/></Constraints><documentation value="Remote Sleep State for Channel"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_37a82256e15f19c2e82fbc7af09f5f26"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ChannelActiveTimer:VarArray" scope="public" xmi:idref="EAID_5818ba99e199a5c78280b3faa9e2b94c" xmi:type="uml:Object"><properties documentation="Shutdown Delay Timer for BusNm on channel / Synchronization Point timeout timer" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ChannelActiveTimer" scope="Private" xmi:idref="EAID_f21f3f3e9ab63438fbe40a392600e88e"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Coordinator Support Enabled is OFF" type="Pre-condition"/></Constraints><documentation value="Shutdown Delay Timer for BusNm on channel / Synchronization Point timeout timer"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_a829e4efb61a947e0098512f436c1fd4"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ChannelBusNmState:VarArray" scope="public" xmi:idref="EAID_67de5b98a6d67ca5dfcc3bbf2df66393" xmi:type="uml:Object"><properties documentation="BusNm state for Channel" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ChannelBusNmState" scope="Private" xmi:idref="EAID_da9daf975d51f0c8bf30e3e955ac955c"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Only one BusNm can be found on each channel or State Change Ind Enabled is OFF" type="Pre-condition"/></Constraints><documentation value="BusNm state for Channel"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_39d39678373df4943e017e02f37f94f8"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ChannelMode:VarArray" scope="public" xmi:idref="EAID_40483e7305d7443c12903a85d28d04ae" xmi:type="uml:Object"><properties documentation="Mode for Channel" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ChannelMode" scope="Private" xmi:idref="EAID_da413c59556eb124605bdfb3521d6274"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Only one BusNm can be found on each channel" type="Pre-condition"/></Constraints><documentation value="Mode for Channel"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_865b64f3a2f292f071fef0f2713345b8"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ChannelRequested:VarArray" scope="public" xmi:idref="EAID_5edebc35301d0d07e2f44a653d3ed8c0" xmi:type="uml:Object"><properties documentation="Channel Requested State" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ChannelRequested" scope="Private" xmi:idref="EAID_f13c9144f3d1ae85b11c5bfbce6e086c"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Coordinator Support Enabled is OFF" type="Pre-condition"/></Constraints><documentation value="Channel Requested State"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_d9f91131b67ced3aeedc8349f25bb9c1"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ChannelState:VarArray" scope="public" xmi:idref="EAID_dd86c03a928963ae42cc1de9bfcd5807" xmi:type="uml:Object"><properties documentation="The current channel state" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ChannelState" scope="Private" xmi:idref="EAID_66645088309cbc6f810eab448dfb255a"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Coordinator Support Enabled is OFF" type="Pre-condition"/></Constraints><documentation value="The current channel state"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_a143f61a96e0907cee33877c4f673ff7"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="CoordinatorState:VarArray" scope="public" xmi:idref="EAID_4f01a709c3bb258ecbb5b293037b1bec" xmi:type="uml:Object"><properties documentation="Coordinator State" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="CoordinatorState" scope="Private" xmi:idref="EAID_8bfc1664c68470024f21265abad10288"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Coordinator Support Enabled is OFF" type="Pre-condition"/></Constraints><documentation value="Coordinator State"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_ac1873a70f21e8602a3ccc586763e46a"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ExternalWakeupProcessed:VarArray" scope="public" xmi:idref="EAID_0e2e44c6f2039ba03209aa6112938f6a" xmi:type="uml:Object"><properties documentation="External Wakeup Processed" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ExternalWakeupProcessed" scope="Private" xmi:idref="EAID_a226f169765e58bbdf5ed32251e2905b"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Nm Gw Ext Enabled is turned OFF" type="Pre-condition"/></Constraints><documentation value="External Wakeup Processed"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_9b1ea0e7448224986686356b661f8ab6"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="OsekNormal:VarArray" scope="public" xmi:idref="EAID_b22009d71d2f10f6e5e6bcb412e3d58a" xmi:type="uml:Object"><properties documentation="NmOsek is in normal state or not in the channel" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="OsekNormal" scope="Private" xmi:idref="EAID_0b5e7ccbdf7c194bf977575a2e29d285"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Wait bus sleep extension is not enabled or Coordinator Support is not enabled" type="Pre-condition"/></Constraints><documentation value="NmOsek is in normal state or not in the channel"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_962bfaf5413b85dd83d1acbb15b42bfc"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="PartEcuActiveChannels:VarArray" scope="public" xmi:idref="EAID_820b622b9b5fad682764a9c4dacb7e42" xmi:type="uml:Object"><properties documentation="Wakeup Detected" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="PartEcuActiveChannels" scope="Private" xmi:idref="EAID_5a3bf82af6efff3e2775c83c617a984d"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Nm Gw Ext Enabled is turned OFF" type="Pre-condition"/></Constraints><documentation value="Wakeup Detected"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_6f18242b00ce660423168415f32cfb9e"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="PartEcuReqChannels:VarArray" scope="public" xmi:idref="EAID_8d35c7cd0a29256abada43e22841d951" xmi:type="uml:Object"><properties documentation="Part Ecu Req Channels" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="PartEcuReqChannels" scope="Private" xmi:idref="EAID_7d8e47499746b0e6c0175ac0aad41a9d"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Gw Ext Enabled is turned OFF or Nm Gw Ext Proxy Bits Enabled is turned OFF" type="Pre-condition"/></Constraints><documentation value="Part Ecu Req Channels"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_aaf8f11b7ad6f217330fc767fdddc85f"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="PartEcuToActToNodeId:VarArray" scope="public" xmi:idref="EAID_8fb7e4d9b06fa5d5e566e5482d8a3268" xmi:type="uml:Object"><properties documentation="Variable of the Actual channels to be kept awake, corresponding to the ECUs refered by Node ID" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="PartEcuToActToNodeId" scope="Private" xmi:idref="EAID_7ddd4f8e68b493d886fa4e268c45a8e3"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Nm Gw Ext Enabled is turned OFF" type="Pre-condition"/></Constraints><documentation value="Variable of the Actual channels to be kept awake, corresponding to the ECUs refered by Node ID"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_aecd15a28def0fbf0574fc2a4884047a"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="RemoteSleepFilter:VarArray" scope="public" xmi:idref="EAID_1b129b2cf7910ca4465e966e2ecbb530" xmi:type="uml:Object"><properties documentation="Remote Sleep Filter" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="RemoteSleepFilter" scope="Private" xmi:idref="EAID_39026df7b03fc7cf36b296b5aacd0f38"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Nm Gw Ext Enabled is turned OFF" type="Pre-condition"/></Constraints><documentation value="Remote Sleep Filter"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_1513efe841ef0397d9866e4a12a0b12e"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="RemoteWakeupFilter:VarArray" scope="public" xmi:idref="EAID_d9e2ae8c800d1c7b32926b762877bfc0" xmi:type="uml:Object"><properties documentation="Remote Wakeup Filter" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="RemoteWakeupFilter" scope="Private" xmi:idref="EAID_3746a42aac6ac095b5ef718cbc2cf628"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Nm Gw Ext Enabled is turned OFF" type="Pre-condition"/></Constraints><documentation value="Remote Wakeup Filter"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_aa3e29d3707cd0a3be0ea4e6cfb2d2a7"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="WakeupDetected:VarArray" scope="public" xmi:idref="EAID_c56464b2686246bf9c587e3863085ad0" xmi:type="uml:Object"><properties documentation="Wakeup Detected" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="WakeupDetected" scope="Private" xmi:idref="EAID_5b2802257771405b1a5149163c766cdf"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Nm Gw Ext Enabled is turned OFF" type="Pre-condition"/></Constraints><documentation value="Wakeup Detected"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_3748bb13622a9578a8a3a8ae4b2b23b5"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element></elements><diagrams><diagram xmi:id="EAID_6c11e0903295067e46fa8a2885785d30"><elements><element subject="EAID_cc39598da802584fca05ef1d257515a2"/><element subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element subject="EAID_1e235e617d7f7128562ac69a1602c2fd"/><element subject="EAID_ef9791da85a8d6423d2d4994adc4073c"/><element subject="EAID_bdd180dbda7c0e31c75275401471ca5f"/><element subject="EAID_e451f3056f0a4610349012ec13dcc3c8"/><element subject="EAID_1a6610a5b9ef1e06a4cddcd91f800302"/><element subject="EAID_8f02352d39aa3ef33e67903f51750234"/><element subject="EAID_e6828a3ad1cf3440998acb767249e64a"/><element subject="EAID_d984aba699c56246cc20c3f36e4024d4"/><element subject="EAID_8bb4797e0bb26d355b66ebb8f2156f95"/><element subject="EAID_8ef25595bfd3e70dde0a1006750a1007"/><element subject="EAID_143d2ecac320b3beb12a68bbd7bd19b7"/><element subject="EAID_1635be7a1e9fcdf253f1f1dfc0d37a75"/><element subject="EAID_5818ba99e199a5c78280b3faa9e2b94c"/><element subject="EAID_e7c97d199925dd79ce86de19bc71dd9e"/><element subject="EAID_67de5b98a6d67ca5dfcc3bbf2df66393"/><element subject="EAID_54666ec9e269fe17e42a8820f597f83f"/><element subject="EAID_40483e7305d7443c12903a85d28d04ae"/><element subject="EAID_bc7e90322a6c0c099cffa1f9e3cbea37"/><element subject="EAID_5edebc35301d0d07e2f44a653d3ed8c0"/><element subject="EAID_e44bfbe9a93e15366271a3ca584e106b"/><element subject="EAID_dd86c03a928963ae42cc1de9bfcd5807"/><element subject="EAID_9be9aa8d7d114368d8be350a7d5ec338"/><element subject="EAID_4f01a709c3bb258ecbb5b293037b1bec"/><element subject="EAID_0e2e44c6f2039ba03209aa6112938f6a"/><element subject="EAID_11985bc4adb6b9e875a2981cdbc60765"/><element subject="EAID_b22009d71d2f10f6e5e6bcb412e3d58a"/><element subject="EAID_41f13c243936534219e7f07683037e54"/><element subject="EAID_820b622b9b5fad682764a9c4dacb7e42"/><element subject="EAID_663ddbd37f43d5a4820e456d77e3a53a"/><element subject="EAID_8d35c7cd0a29256abada43e22841d951"/><element subject="EAID_8860136434da496f99d9f74edbb04f02"/><element subject="EAID_8fb7e4d9b06fa5d5e566e5482d8a3268"/><element subject="EAID_ae34e4e06097c2fe323602696dc68f92"/><element subject="EAID_1b129b2cf7910ca4465e966e2ecbb530"/><element subject="EAID_5863e99e0875bc1d4098fc57cba1aa5c"/><element subject="EAID_d9e2ae8c800d1c7b32926b762877bfc0"/><element subject="EAID_8117d791d7cf45534fdd5d2e9d3284b4"/><element subject="EAID_c56464b2686246bf9c587e3863085ad0"/><element subject="EAID_06dfae38c7297a1bb0755fe12a7a1e09"/></elements><model owner="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093" package="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093"/><properties name="All Data and Relations" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_bce6788aedc7feab328cc18429a8dd17"><elements><element subject="EAID_cc39598da802584fca05ef1d257515a2"/><element subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element subject="EAID_1e235e617d7f7128562ac69a1602c2fd"/><element subject="EAID_ef9791da85a8d6423d2d4994adc4073c"/><element subject="EAID_bdd180dbda7c0e31c75275401471ca5f"/><element subject="EAID_e451f3056f0a4610349012ec13dcc3c8"/><element subject="EAID_1a6610a5b9ef1e06a4cddcd91f800302"/><element subject="EAID_8f02352d39aa3ef33e67903f51750234"/><element subject="EAID_e6828a3ad1cf3440998acb767249e64a"/><element subject="EAID_d984aba699c56246cc20c3f36e4024d4"/></elements><model owner="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093" package="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093"/><properties name="CONST with Struct Elements" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_dc2cbb3514122c0294260043b87d405a"><elements><element subject="EAID_cc39598da802584fca05ef1d257515a2"/><element subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element subject="EAID_1e235e617d7f7128562ac69a1602c2fd"/><element subject="EAID_ef9791da85a8d6423d2d4994adc4073c"/><element subject="EAID_bdd180dbda7c0e31c75275401471ca5f"/><element subject="EAID_e451f3056f0a4610349012ec13dcc3c8"/><element subject="EAID_1a6610a5b9ef1e06a4cddcd91f800302"/><element subject="EAID_8f02352d39aa3ef33e67903f51750234"/><element subject="EAID_e6828a3ad1cf3440998acb767249e64a"/><element subject="EAID_d984aba699c56246cc20c3f36e4024d4"/></elements><model owner="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093" package="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093"/><properties name="CONST without Struct Elements" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_8e035c32efde703dc9c6a36a886f81bb"><elements><element subject="EAID_8bb4797e0bb26d355b66ebb8f2156f95"/><element subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element subject="EAID_143d2ecac320b3beb12a68bbd7bd19b7"/><element subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element subject="EAID_5818ba99e199a5c78280b3faa9e2b94c"/><element subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element subject="EAID_67de5b98a6d67ca5dfcc3bbf2df66393"/><element subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element subject="EAID_40483e7305d7443c12903a85d28d04ae"/><element subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element subject="EAID_5edebc35301d0d07e2f44a653d3ed8c0"/><element subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element subject="EAID_dd86c03a928963ae42cc1de9bfcd5807"/><element subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element subject="EAID_4f01a709c3bb258ecbb5b293037b1bec"/><element subject="EAID_0e2e44c6f2039ba03209aa6112938f6a"/><element subject="EAID_bdd180dbda7c0e31c75275401471ca5f"/><element subject="EAID_b22009d71d2f10f6e5e6bcb412e3d58a"/><element subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element subject="EAID_820b622b9b5fad682764a9c4dacb7e42"/><element subject="EAID_bdd180dbda7c0e31c75275401471ca5f"/><element subject="EAID_8d35c7cd0a29256abada43e22841d951"/><element subject="EAID_bdd180dbda7c0e31c75275401471ca5f"/><element subject="EAID_8fb7e4d9b06fa5d5e566e5482d8a3268"/><element subject="EAID_e6828a3ad1cf3440998acb767249e64a"/><element subject="EAID_1b129b2cf7910ca4465e966e2ecbb530"/><element subject="EAID_bdd180dbda7c0e31c75275401471ca5f"/><element subject="EAID_d9e2ae8c800d1c7b32926b762877bfc0"/><element subject="EAID_bdd180dbda7c0e31c75275401471ca5f"/><element subject="EAID_c56464b2686246bf9c587e3863085ad0"/><element subject="EAID_bdd180dbda7c0e31c75275401471ca5f"/></elements><model owner="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093" package="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093"/><properties name="VAR and Relations" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_0adf5f5ff3cb3f2b852777f9cd5f5fcc"><elements><element subject="EAID_b22009d71d2f10f6e5e6bcb412e3d58a"/></elements><model owner="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093" package="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093"/><properties name="Data Accessed by Adress Operator" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_60b03b10d317af9775c1232c154e289b"><elements/><model owner="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093" package="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093"/><properties name="Data Accessed by Interface Handles" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_064b4c829ac142c92fd86e0db43f0206"><elements/><model owner="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093" package="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093"/><properties name="Max Precompile Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_8891bf4ce1800a9310c1ec31fe81e6de"><elements><element subject="EAID_cc39598da802584fca05ef1d257515a2"/><element subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element subject="EAID_bdd180dbda7c0e31c75275401471ca5f"/><element subject="EAID_8f02352d39aa3ef33e67903f51750234"/><element subject="EAID_e6828a3ad1cf3440998acb767249e64a"/><element subject="EAID_d984aba699c56246cc20c3f36e4024d4"/><element subject="EAID_476d804a7cb2915f27a8f625ad601acb"/><element subject="EAID_f5ac4de6a340d6631abe49e90f03ae98"/><element subject="EAID_8bb4797e0bb26d355b66ebb8f2156f95"/><element subject="EAID_143d2ecac320b3beb12a68bbd7bd19b7"/><element subject="EAID_5818ba99e199a5c78280b3faa9e2b94c"/><element subject="EAID_67de5b98a6d67ca5dfcc3bbf2df66393"/><element subject="EAID_40483e7305d7443c12903a85d28d04ae"/><element subject="EAID_5edebc35301d0d07e2f44a653d3ed8c0"/><element subject="EAID_dd86c03a928963ae42cc1de9bfcd5807"/><element subject="EAID_4f01a709c3bb258ecbb5b293037b1bec"/><element subject="EAID_0e2e44c6f2039ba03209aa6112938f6a"/><element subject="EAID_b22009d71d2f10f6e5e6bcb412e3d58a"/><element subject="EAID_820b622b9b5fad682764a9c4dacb7e42"/><element subject="EAID_8d35c7cd0a29256abada43e22841d951"/><element subject="EAID_8fb7e4d9b06fa5d5e566e5482d8a3268"/><element subject="EAID_1b129b2cf7910ca4465e966e2ecbb530"/><element subject="EAID_d9e2ae8c800d1c7b32926b762877bfc0"/><element subject="EAID_c56464b2686246bf9c587e3863085ad0"/></elements><model owner="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093" package="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093"/><properties name="Max Linktime Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_2fe13a270c291ccc60b048e98df386ba"><elements/><model owner="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093" package="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093"/><properties name="Max Postbuild Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_e91b9dbaf111d84a7eeb10ef53a286f3"><elements/><model owner="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093" package="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093"/><properties name="Calibration Lite Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_ebd2a1e77dce2fdc674c7525c028aa85"><elements/><model owner="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093" package="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093"/><properties name="Sandbox with Details" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_823fd4cee1579edcdb3e2b4e1e035ade"><elements/><model owner="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093" package="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093"/><properties name="Sandbox without Details" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram></diagrams></xmi:Extension><uml:Model name="Nm Abstract Data Model" visibility="public" xmi:id="EAPK425eeb8cff1c7e35e6b2b65d8d1b3093" xmi:type="uml:Package"><packagedElement name="BusNmChIdxToBusNmIdxInds:ConstStruct" visibility="public" xmi:id="EAID_cc39598da802584fca05ef1d257515a2" xmi:type="uml:InstanceSpecification"/><packagedElement name="ChannelConfig:ConstStruct" visibility="public" xmi:id="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_1e235e617d7f7128562ac69a1602c2fd" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst1e235e617d7f7128562ac69a1602c2fd" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_f83e4563fb8ee245707af7e732c8e8cc" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_7f6c4ef1060764898f682a1d57c44d00" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_8f02352d39aa3ef33e67903f51750234"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_ef9791da85a8d6423d2d4994adc4073c" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstef9791da85a8d6423d2d4994adc4073c" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_32badfe5a0ae8ec3a56ca55dadc59986" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_0128b08804194c5a7fee151beaec2410" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_4f01a709c3bb258ecbb5b293037b1bec"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_1e235e617d7f7128562ac69a1602c2fd" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst1e235e617d7f7128562ac69a1602c2fd"/><memberEnd xmi:idref="EAID_src1e235e617d7f7128562ac69a1602c2fd"/><ownedEnd aggregation="none" association="EAID_1e235e617d7f7128562ac69a1602c2fd" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src1e235e617d7f7128562ac69a1602c2fd" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_ef9791da85a8d6423d2d4994adc4073c" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstef9791da85a8d6423d2d4994adc4073c"/><memberEnd xmi:idref="EAID_srcef9791da85a8d6423d2d4994adc4073c"/><ownedEnd aggregation="none" association="EAID_ef9791da85a8d6423d2d4994adc4073c" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcef9791da85a8d6423d2d4994adc4073c" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd></packagedElement><packagedElement name="CoordCluster:ConstStruct" visibility="public" xmi:id="EAID_bdd180dbda7c0e31c75275401471ca5f" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_e451f3056f0a4610349012ec13dcc3c8" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dste451f3056f0a4610349012ec13dcc3c8" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_acf144bb73c1455643cebf3c30ecb718" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_380806f0c1904423830c423c43a38b94" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_e6828a3ad1cf3440998acb767249e64a"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_1a6610a5b9ef1e06a4cddcd91f800302" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst1a6610a5b9ef1e06a4cddcd91f800302" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_95d3fe750b8f846b4aabd733bac469a4" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_dd4dad4390cefd86dcd49a9ec1f9e4b6" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_5818ba99e199a5c78280b3faa9e2b94c"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_e451f3056f0a4610349012ec13dcc3c8" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dste451f3056f0a4610349012ec13dcc3c8"/><memberEnd xmi:idref="EAID_srce451f3056f0a4610349012ec13dcc3c8"/><ownedEnd aggregation="none" association="EAID_e451f3056f0a4610349012ec13dcc3c8" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srce451f3056f0a4610349012ec13dcc3c8" xmi:type="uml:Property"><type xmi:idref="EAID_bdd180dbda7c0e31c75275401471ca5f"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_1a6610a5b9ef1e06a4cddcd91f800302" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst1a6610a5b9ef1e06a4cddcd91f800302"/><memberEnd xmi:idref="EAID_src1a6610a5b9ef1e06a4cddcd91f800302"/><ownedEnd aggregation="none" association="EAID_1a6610a5b9ef1e06a4cddcd91f800302" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src1a6610a5b9ef1e06a4cddcd91f800302" xmi:type="uml:Property"><type xmi:idref="EAID_bdd180dbda7c0e31c75275401471ca5f"/></ownedEnd></packagedElement><packagedElement name="NmFunctionTable:ConstStruct" visibility="public" xmi:id="EAID_8f02352d39aa3ef33e67903f51750234" xmi:type="uml:InstanceSpecification"/><packagedElement name="NmPartEcuMapping:ConstStruct" visibility="public" xmi:id="EAID_e6828a3ad1cf3440998acb767249e64a" xmi:type="uml:InstanceSpecification"/><packagedElement name="SysToNmChInd:ConstArray" visibility="public" xmi:id="EAID_d984aba699c56246cc20c3f36e4024d4" xmi:type="uml:InstanceSpecification"/><packagedElement name="CarWakeUpCallbackFctPtr:ConstVar" visibility="public" xmi:id="EAID_476d804a7cb2915f27a8f625ad601acb" xmi:type="uml:InstanceSpecification"/><packagedElement name="NmCoordinatorWithNmFiatC:ConstVar" visibility="public" xmi:id="EAID_f5ac4de6a340d6631abe49e90f03ae98" xmi:type="uml:InstanceSpecification"/><packagedElement name="ChPrevBusNmState:VarArray" visibility="public" xmi:id="EAID_8bb4797e0bb26d355b66ebb8f2156f95" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_8ef25595bfd3e70dde0a1006750a1007" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst8ef25595bfd3e70dde0a1006750a1007"/><ownedEnd aggregation="none" association="EAID_8ef25595bfd3e70dde0a1006750a1007" visibility="public" xmi:id="EAID_dst8ef25595bfd3e70dde0a1006750a1007" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_src8ef25595bfd3e70dde0a1006750a1007"/><ownedEnd aggregation="none" association="EAID_8ef25595bfd3e70dde0a1006750a1007" visibility="public" xmi:id="EAID_src8ef25595bfd3e70dde0a1006750a1007" xmi:type="uml:Property"><type xmi:idref="EAID_8bb4797e0bb26d355b66ebb8f2156f95"/></ownedEnd></packagedElement><packagedElement name="ChRemSleepState:VarArray" visibility="public" xmi:id="EAID_143d2ecac320b3beb12a68bbd7bd19b7" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_1635be7a1e9fcdf253f1f1dfc0d37a75" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst1635be7a1e9fcdf253f1f1dfc0d37a75"/><ownedEnd aggregation="none" association="EAID_1635be7a1e9fcdf253f1f1dfc0d37a75" visibility="public" xmi:id="EAID_dst1635be7a1e9fcdf253f1f1dfc0d37a75" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_src1635be7a1e9fcdf253f1f1dfc0d37a75"/><ownedEnd aggregation="none" association="EAID_1635be7a1e9fcdf253f1f1dfc0d37a75" visibility="public" xmi:id="EAID_src1635be7a1e9fcdf253f1f1dfc0d37a75" xmi:type="uml:Property"><type xmi:idref="EAID_143d2ecac320b3beb12a68bbd7bd19b7"/></ownedEnd></packagedElement><packagedElement name="ChannelActiveTimer:VarArray" visibility="public" xmi:id="EAID_5818ba99e199a5c78280b3faa9e2b94c" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_e7c97d199925dd79ce86de19bc71dd9e" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dste7c97d199925dd79ce86de19bc71dd9e"/><ownedEnd aggregation="none" association="EAID_e7c97d199925dd79ce86de19bc71dd9e" visibility="public" xmi:id="EAID_dste7c97d199925dd79ce86de19bc71dd9e" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_srce7c97d199925dd79ce86de19bc71dd9e"/><ownedEnd aggregation="none" association="EAID_e7c97d199925dd79ce86de19bc71dd9e" visibility="public" xmi:id="EAID_srce7c97d199925dd79ce86de19bc71dd9e" xmi:type="uml:Property"><type xmi:idref="EAID_5818ba99e199a5c78280b3faa9e2b94c"/></ownedEnd></packagedElement><packagedElement name="ChannelBusNmState:VarArray" visibility="public" xmi:id="EAID_67de5b98a6d67ca5dfcc3bbf2df66393" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_54666ec9e269fe17e42a8820f597f83f" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst54666ec9e269fe17e42a8820f597f83f"/><ownedEnd aggregation="none" association="EAID_54666ec9e269fe17e42a8820f597f83f" visibility="public" xmi:id="EAID_dst54666ec9e269fe17e42a8820f597f83f" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_src54666ec9e269fe17e42a8820f597f83f"/><ownedEnd aggregation="none" association="EAID_54666ec9e269fe17e42a8820f597f83f" visibility="public" xmi:id="EAID_src54666ec9e269fe17e42a8820f597f83f" xmi:type="uml:Property"><type xmi:idref="EAID_67de5b98a6d67ca5dfcc3bbf2df66393"/></ownedEnd></packagedElement><packagedElement name="ChannelMode:VarArray" visibility="public" xmi:id="EAID_40483e7305d7443c12903a85d28d04ae" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_bc7e90322a6c0c099cffa1f9e3cbea37" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstbc7e90322a6c0c099cffa1f9e3cbea37"/><ownedEnd aggregation="none" association="EAID_bc7e90322a6c0c099cffa1f9e3cbea37" visibility="public" xmi:id="EAID_dstbc7e90322a6c0c099cffa1f9e3cbea37" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_srcbc7e90322a6c0c099cffa1f9e3cbea37"/><ownedEnd aggregation="none" association="EAID_bc7e90322a6c0c099cffa1f9e3cbea37" visibility="public" xmi:id="EAID_srcbc7e90322a6c0c099cffa1f9e3cbea37" xmi:type="uml:Property"><type xmi:idref="EAID_40483e7305d7443c12903a85d28d04ae"/></ownedEnd></packagedElement><packagedElement name="ChannelRequested:VarArray" visibility="public" xmi:id="EAID_5edebc35301d0d07e2f44a653d3ed8c0" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_e44bfbe9a93e15366271a3ca584e106b" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dste44bfbe9a93e15366271a3ca584e106b"/><ownedEnd aggregation="none" association="EAID_e44bfbe9a93e15366271a3ca584e106b" visibility="public" xmi:id="EAID_dste44bfbe9a93e15366271a3ca584e106b" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_srce44bfbe9a93e15366271a3ca584e106b"/><ownedEnd aggregation="none" association="EAID_e44bfbe9a93e15366271a3ca584e106b" visibility="public" xmi:id="EAID_srce44bfbe9a93e15366271a3ca584e106b" xmi:type="uml:Property"><type xmi:idref="EAID_5edebc35301d0d07e2f44a653d3ed8c0"/></ownedEnd></packagedElement><packagedElement name="ChannelState:VarArray" visibility="public" xmi:id="EAID_dd86c03a928963ae42cc1de9bfcd5807" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_9be9aa8d7d114368d8be350a7d5ec338" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst9be9aa8d7d114368d8be350a7d5ec338"/><ownedEnd aggregation="none" association="EAID_9be9aa8d7d114368d8be350a7d5ec338" visibility="public" xmi:id="EAID_dst9be9aa8d7d114368d8be350a7d5ec338" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_src9be9aa8d7d114368d8be350a7d5ec338"/><ownedEnd aggregation="none" association="EAID_9be9aa8d7d114368d8be350a7d5ec338" visibility="public" xmi:id="EAID_src9be9aa8d7d114368d8be350a7d5ec338" xmi:type="uml:Property"><type xmi:idref="EAID_dd86c03a928963ae42cc1de9bfcd5807"/></ownedEnd></packagedElement><packagedElement name="CoordinatorState:VarArray" visibility="public" xmi:id="EAID_4f01a709c3bb258ecbb5b293037b1bec" xmi:type="uml:InstanceSpecification"/><packagedElement name="ExternalWakeupProcessed:VarArray" visibility="public" xmi:id="EAID_0e2e44c6f2039ba03209aa6112938f6a" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_11985bc4adb6b9e875a2981cdbc60765" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst11985bc4adb6b9e875a2981cdbc60765"/><ownedEnd aggregation="none" association="EAID_11985bc4adb6b9e875a2981cdbc60765" visibility="public" xmi:id="EAID_dst11985bc4adb6b9e875a2981cdbc60765" xmi:type="uml:Property"><type xmi:idref="EAID_bdd180dbda7c0e31c75275401471ca5f"/></ownedEnd><memberEnd xmi:idref="EAID_src11985bc4adb6b9e875a2981cdbc60765"/><ownedEnd aggregation="none" association="EAID_11985bc4adb6b9e875a2981cdbc60765" visibility="public" xmi:id="EAID_src11985bc4adb6b9e875a2981cdbc60765" xmi:type="uml:Property"><type xmi:idref="EAID_0e2e44c6f2039ba03209aa6112938f6a"/></ownedEnd></packagedElement><packagedElement name="OsekNormal:VarArray" visibility="public" xmi:id="EAID_b22009d71d2f10f6e5e6bcb412e3d58a" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_41f13c243936534219e7f07683037e54" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst41f13c243936534219e7f07683037e54"/><ownedEnd aggregation="none" association="EAID_41f13c243936534219e7f07683037e54" visibility="public" xmi:id="EAID_dst41f13c243936534219e7f07683037e54" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_src41f13c243936534219e7f07683037e54"/><ownedEnd aggregation="none" association="EAID_41f13c243936534219e7f07683037e54" visibility="public" xmi:id="EAID_src41f13c243936534219e7f07683037e54" xmi:type="uml:Property"><type xmi:idref="EAID_b22009d71d2f10f6e5e6bcb412e3d58a"/></ownedEnd></packagedElement><packagedElement name="PartEcuActiveChannels:VarArray" visibility="public" xmi:id="EAID_820b622b9b5fad682764a9c4dacb7e42" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_663ddbd37f43d5a4820e456d77e3a53a" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst663ddbd37f43d5a4820e456d77e3a53a"/><ownedEnd aggregation="none" association="EAID_663ddbd37f43d5a4820e456d77e3a53a" visibility="public" xmi:id="EAID_dst663ddbd37f43d5a4820e456d77e3a53a" xmi:type="uml:Property"><type xmi:idref="EAID_bdd180dbda7c0e31c75275401471ca5f"/></ownedEnd><memberEnd xmi:idref="EAID_src663ddbd37f43d5a4820e456d77e3a53a"/><ownedEnd aggregation="none" association="EAID_663ddbd37f43d5a4820e456d77e3a53a" visibility="public" xmi:id="EAID_src663ddbd37f43d5a4820e456d77e3a53a" xmi:type="uml:Property"><type xmi:idref="EAID_820b622b9b5fad682764a9c4dacb7e42"/></ownedEnd></packagedElement><packagedElement name="PartEcuReqChannels:VarArray" visibility="public" xmi:id="EAID_8d35c7cd0a29256abada43e22841d951" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_8860136434da496f99d9f74edbb04f02" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst8860136434da496f99d9f74edbb04f02"/><ownedEnd aggregation="none" association="EAID_8860136434da496f99d9f74edbb04f02" visibility="public" xmi:id="EAID_dst8860136434da496f99d9f74edbb04f02" xmi:type="uml:Property"><type xmi:idref="EAID_bdd180dbda7c0e31c75275401471ca5f"/></ownedEnd><memberEnd xmi:idref="EAID_src8860136434da496f99d9f74edbb04f02"/><ownedEnd aggregation="none" association="EAID_8860136434da496f99d9f74edbb04f02" visibility="public" xmi:id="EAID_src8860136434da496f99d9f74edbb04f02" xmi:type="uml:Property"><type xmi:idref="EAID_8d35c7cd0a29256abada43e22841d951"/></ownedEnd></packagedElement><packagedElement name="PartEcuToActToNodeId:VarArray" visibility="public" xmi:id="EAID_8fb7e4d9b06fa5d5e566e5482d8a3268" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_ae34e4e06097c2fe323602696dc68f92" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstae34e4e06097c2fe323602696dc68f92"/><ownedEnd aggregation="none" association="EAID_ae34e4e06097c2fe323602696dc68f92" visibility="public" xmi:id="EAID_dstae34e4e06097c2fe323602696dc68f92" xmi:type="uml:Property"><type xmi:idref="EAID_e6828a3ad1cf3440998acb767249e64a"/></ownedEnd><memberEnd xmi:idref="EAID_srcae34e4e06097c2fe323602696dc68f92"/><ownedEnd aggregation="none" association="EAID_ae34e4e06097c2fe323602696dc68f92" visibility="public" xmi:id="EAID_srcae34e4e06097c2fe323602696dc68f92" xmi:type="uml:Property"><type xmi:idref="EAID_8fb7e4d9b06fa5d5e566e5482d8a3268"/></ownedEnd></packagedElement><packagedElement name="RemoteSleepFilter:VarArray" visibility="public" xmi:id="EAID_1b129b2cf7910ca4465e966e2ecbb530" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_5863e99e0875bc1d4098fc57cba1aa5c" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst5863e99e0875bc1d4098fc57cba1aa5c"/><ownedEnd aggregation="none" association="EAID_5863e99e0875bc1d4098fc57cba1aa5c" visibility="public" xmi:id="EAID_dst5863e99e0875bc1d4098fc57cba1aa5c" xmi:type="uml:Property"><type xmi:idref="EAID_bdd180dbda7c0e31c75275401471ca5f"/></ownedEnd><memberEnd xmi:idref="EAID_src5863e99e0875bc1d4098fc57cba1aa5c"/><ownedEnd aggregation="none" association="EAID_5863e99e0875bc1d4098fc57cba1aa5c" visibility="public" xmi:id="EAID_src5863e99e0875bc1d4098fc57cba1aa5c" xmi:type="uml:Property"><type xmi:idref="EAID_1b129b2cf7910ca4465e966e2ecbb530"/></ownedEnd></packagedElement><packagedElement name="RemoteWakeupFilter:VarArray" visibility="public" xmi:id="EAID_d9e2ae8c800d1c7b32926b762877bfc0" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_8117d791d7cf45534fdd5d2e9d3284b4" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst8117d791d7cf45534fdd5d2e9d3284b4"/><ownedEnd aggregation="none" association="EAID_8117d791d7cf45534fdd5d2e9d3284b4" visibility="public" xmi:id="EAID_dst8117d791d7cf45534fdd5d2e9d3284b4" xmi:type="uml:Property"><type xmi:idref="EAID_bdd180dbda7c0e31c75275401471ca5f"/></ownedEnd><memberEnd xmi:idref="EAID_src8117d791d7cf45534fdd5d2e9d3284b4"/><ownedEnd aggregation="none" association="EAID_8117d791d7cf45534fdd5d2e9d3284b4" visibility="public" xmi:id="EAID_src8117d791d7cf45534fdd5d2e9d3284b4" xmi:type="uml:Property"><type xmi:idref="EAID_d9e2ae8c800d1c7b32926b762877bfc0"/></ownedEnd></packagedElement><packagedElement name="WakeupDetected:VarArray" visibility="public" xmi:id="EAID_c56464b2686246bf9c587e3863085ad0" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_06dfae38c7297a1bb0755fe12a7a1e09" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst06dfae38c7297a1bb0755fe12a7a1e09"/><ownedEnd aggregation="none" association="EAID_06dfae38c7297a1bb0755fe12a7a1e09" visibility="public" xmi:id="EAID_dst06dfae38c7297a1bb0755fe12a7a1e09" xmi:type="uml:Property"><type xmi:idref="EAID_bdd180dbda7c0e31c75275401471ca5f"/></ownedEnd><memberEnd xmi:idref="EAID_src06dfae38c7297a1bb0755fe12a7a1e09"/><ownedEnd aggregation="none" association="EAID_06dfae38c7297a1bb0755fe12a7a1e09" visibility="public" xmi:id="EAID_src06dfae38c7297a1bb0755fe12a7a1e09" xmi:type="uml:Property"><type xmi:idref="EAID_c56464b2686246bf9c587e3863085ad0"/></ownedEnd></packagedElement></uml:Model></xmi:XMI>
