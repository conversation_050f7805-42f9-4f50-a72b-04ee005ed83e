<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="bc723413-5282-4937-bd37-705d7ba0948d">
      <SHORT-NAME>StartApplication</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="83e80b9b-afd2-475e-9e0d-e4956ac62ac3">
          <SHORT-NAME>StartApplication_swc</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="5806965e-ff5b-462d-bf69-68dbde052b80">
              <SHORT-NAME>DataTypes</SHORT-NAME>
              <ELEMENTS>
                <IMPLEMENTATION-DATA-TYPE UUID="a1080826-eb6f-454b-bfc9-e15c82f4998a">
                  <SHORT-NAME>ComM_ModeType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/StartApplication/StartApplication_swc/DataTypes/CompuMethods/ComM_ModeType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/StartApplication/StartApplication_swc/DataTypes/DataConstrs/ComM_ModeType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="dcd45bfa-be9f-4494-9886-d1797f891141">
                  <SHORT-NAME>Dem_EventStatusType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/StartApplication/StartApplication_swc/DataTypes/CompuMethods/Dem_EventStatusType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/StartApplication/StartApplication_swc/DataTypes/DataConstrs/Dem_EventStatusType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="e02faa07-6a37-44e0-8143-42031334a58b">
                  <SHORT-NAME>Dcm_Data2ByteType</SHORT-NAME>
                  <CATEGORY>ARRAY</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-CALIBRATION-ACCESS>READ-ONLY</SW-CALIBRATION-ACCESS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="7cd4c760-a712-4051-9066-7977a1a266fd">
                      <SHORT-NAME>Dcm_Data2ByteTypeElement</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <ARRAY-SIZE>2</ARRAY-SIZE>
                      <ARRAY-SIZE-SEMANTICS>FIXED-SIZE</ARRAY-SIZE-SEMANTICS>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="65b2fe3a-bd10-4030-acf2-defe15bd6724">
                  <SHORT-NAME>Dcm_NegativeResponseCodeType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/StartApplication/StartApplication_swc/DataTypes/CompuMethods/Dcm_NegativeResponseCodeType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/StartApplication/StartApplication_swc/DataTypes/DataConstrs/Dcm_NegativeResponseCodeType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="5bcdc996-70af-44bd-bc12-6e90bcb95b9d">
                  <SHORT-NAME>DataArray_Type_2</SHORT-NAME>
                  <CATEGORY>ARRAY</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-CALIBRATION-ACCESS>READ-ONLY</SW-CALIBRATION-ACCESS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="e48d2152-4780-410a-a0ea-4accc24f0bad">
                      <SHORT-NAME>DataArray_Type_2Element</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <ARRAY-SIZE>2</ARRAY-SIZE>
                      <ARRAY-SIZE-SEMANTICS>FIXED-SIZE</ARRAY-SIZE-SEMANTICS>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="606a1441-63a9-4759-ba61-ff7c7f01cd6d">
                  <SHORT-NAME>Dem_OperationCycleStateType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/StartApplication/StartApplication_swc/DataTypes/CompuMethods/Dem_OperationCycleStateType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/StartApplication/StartApplication_swc/DataTypes/DataConstrs/Dem_OperationCycleStateType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="e980cea5-7b2f-44ff-bc33-00a718b78c54">
                  <SHORT-NAME>Dt_SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="8c7a5f50-ce81-4a31-b04a-feb7561e8204">
                      <SHORT-NAME>Re_SigRx_Prof1C_CRC_omsg_RxCycle_E2eProf1C_500_10_oCAN_d6a2f34d_Rx</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="1b37bce0-42b3-4db3-830a-d3c0ca15ecdd">
                      <SHORT-NAME>Re_SigRx_Prof1C_DataId_HiByte_LoNib_omsg_RxCycle_E2eProf1C_500_10_oCAN_bdeb2c98_Rx</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="4bf47287-3174-4c57-a619-b42245e46290">
                      <SHORT-NAME>Re_SigRx_Prof1C_Sig3Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_95ddcbc9_Rx</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="17305f4f-d6cf-4c4c-86d8-3cc4be37a190">
                      <SHORT-NAME>Re_SigRx_Prof1C_SQ_omsg_RxCycle_E2eProf1C_500_10_oCAN_263a4149_Rx</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="e81f8fda-0be7-41a9-bda7-bea1743c62bf">
                      <SHORT-NAME>Re_SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="16535a09-4fd3-4a6f-83aa-da2bf2612822">
                      <SHORT-NAME>Re_SigRx_Prof1C_Sig4Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_a713ffd7_Rx</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="9f96768c-6aa1-4777-905f-dfd7d23ea149">
                  <SHORT-NAME>EnumMEM_BlockStateType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/StartApplication/StartApplication_swc/DataTypes/CompuMethods/EnumMEM_BlockStateType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/StartApplication/StartApplication_swc/DataTypes/DataConstrs/EnumMEM_BlockStateType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="47b7efa7-88d0-4786-86fc-1656de7ea47f">
                  <SHORT-NAME>EnumActiveComponentType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/StartApplication/StartApplication_swc/DataTypes/CompuMethods/EnumActiveComponentType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/StartApplication/StartApplication_swc/DataTypes/DataConstrs/EnumActiveComponentType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="87841757-fc62-4bb6-bff0-af93ee3d17e8">
                  <SHORT-NAME>NvM_ServiceIdType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/StartApplication/StartApplication_swc/DataTypes/CompuMethods/NvM_ServiceIdType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/StartApplication/StartApplication_swc/DataTypes/DataConstrs/NvM_ServiceIdType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="61671361-153b-4e03-a226-37fc8129c922">
                  <SHORT-NAME>NvM_RequestResultType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/StartApplication/StartApplication_swc/DataTypes/CompuMethods/NvM_RequestResultType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/StartApplication/StartApplication_swc/DataTypes/DataConstrs/NvM_RequestResultType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
              </ELEMENTS>
              <AR-PACKAGES>
                <AR-PACKAGE UUID="36647e71-788e-4979-850c-d7a005005250">
                  <SHORT-NAME>DataConstrs</SHORT-NAME>
                  <ELEMENTS>
                    <DATA-CONSTR UUID="406a6ae6-94a8-4802-88fb-8e5035f784b3">
                      <SHORT-NAME>ComM_ModeType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="a8eaf08d-f14d-4440-9b66-75b979dfba98">
                      <SHORT-NAME>Dem_EventStatusType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="e328536f-54cd-4103-94dc-1745b3de9876">
                      <SHORT-NAME>Dcm_NegativeResponseCodeType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">147</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="fb680802-689a-41df-972e-b15e556923c2">
                      <SHORT-NAME>Dem_OperationCycleStateType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="f3465415-5ad9-41ce-8abc-fb44f85a500c">
                      <SHORT-NAME>EnumMEM_BlockStateType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">6</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="7c0174f5-4953-445d-a564-c61ede7373e2">
                      <SHORT-NAME>EnumActiveComponentType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="*************-4d88-b645-ecad5e58f9e1">
                      <SHORT-NAME>NvM_ServiceIdType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">12</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="e9f4e350-f0ae-4123-a028-16e9d785def6">
                      <SHORT-NAME>NvM_RequestResultType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">8</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                  </ELEMENTS>
                </AR-PACKAGE>
                <AR-PACKAGE UUID="e54f7b5e-12c6-4a5d-95bd-e2031f81bb3d">
                  <SHORT-NAME>CompuMethods</SHORT-NAME>
                  <ELEMENTS>
                    <COMPU-METHOD UUID="07855c47-0fcb-446b-9ea8-ab2bfe0ae2dc">
                      <SHORT-NAME>EnumActiveComponentType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>STARTAPPLICATION_ACTIVE_COMPONENT_COM_RXTX</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>STARTAPPLICATION_ACTIVE_COMPONENT_COM_RXTX</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>STARTAPPLICATION_ACTIVE_COMPONENT_MEM</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>STARTAPPLICATION_ACTIVE_COMPONENT_MEM</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>STARTAPPLICATION_ACTIVE_COMPONENT_DIAG</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>STARTAPPLICATION_ACTIVE_COMPONENT_DIAG</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>STARTAPPLICATION_ACTIVE_COMPONENT_COM_TXONLY</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>STARTAPPLICATION_ACTIVE_COMPONENT_COM_TXONLY</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>STARTAPPLICATION_ACTIVE_COMPONENT_INVALID</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">255</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>STARTAPPLICATION_ACTIVE_COMPONENT_INVALID</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="b390b6a5-4b39-4b1e-9de8-461b099d2ec9">
                      <SHORT-NAME>ComM_ModeType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>COMM_NO_COMMUNICATION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>COMM_NO_COMMUNICATION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>COMM_SILENT_COMMUNICATION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>COMM_SILENT_COMMUNICATION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>COMM_FULL_COMMUNICATION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>COMM_FULL_COMMUNICATION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="0c859c1c-6935-4c93-bb34-ccaae5d640f1">
                      <SHORT-NAME>Dem_EventStatusType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DEM_EVENT_STATUS_PASSED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DEM_EVENT_STATUS_PASSED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DEM_EVENT_STATUS_FAILED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DEM_EVENT_STATUS_FAILED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DEM_EVENT_STATUS_PREPASSED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DEM_EVENT_STATUS_PREPASSED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DEM_EVENT_STATUS_PREFAILED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DEM_EVENT_STATUS_PREFAILED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DEM_EVENT_STATUS_FDC_THRESHOLD_REACHED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DEM_EVENT_STATUS_FDC_THRESHOLD_REACHED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="4805bb7a-1631-4625-9465-81a9a5d8c4ea">
                      <SHORT-NAME>Dcm_NegativeResponseCodeType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_POSITIVERESPONSE</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_POSITIVERESPONSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_GENERALREJECT</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">16</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">16</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_GENERALREJECT</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_SERVICENOTSUPPORTED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">17</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">17</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_SERVICENOTSUPPORTED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_SUBFUNCTIONNOTSUPPORTED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">18</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">18</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_SUBFUNCTIONNOTSUPPORTED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_INCORRECTMESSAGELENGTHORINVALIDFORMAT</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">19</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">19</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_INCORRECTMESSAGELENGTHORINVALIDFORMAT</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_RESPONSETOOLONG</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">20</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">20</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_RESPONSETOOLONG</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_BUSYREPEATREQUEST</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">33</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">33</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_BUSYREPEATREQUEST</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_CONDITIONSNOTCORRECT</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">34</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">34</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_CONDITIONSNOTCORRECT</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_REQUESTSEQUENCEERROR</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">36</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">36</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_REQUESTSEQUENCEERROR</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_NORESPONSEFROMSUBNETCOMPONENT</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">37</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">37</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_NORESPONSEFROMSUBNETCOMPONENT</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_FAILUREPREVENTSEXECUTIONOFREQUESTEDACTION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">38</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">38</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_FAILUREPREVENTSEXECUTIONOFREQUESTEDACTION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_REQUESTOUTOFRANGE</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">49</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">49</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_REQUESTOUTOFRANGE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_SECURITYACCESSDENIED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">51</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">51</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_SECURITYACCESSDENIED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_INVALIDKEY</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">53</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">53</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_INVALIDKEY</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_EXCEEDNUMBEROFATTEMPTS</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">54</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">54</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_EXCEEDNUMBEROFATTEMPTS</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_REQUIREDTIMEDELAYNOTEXPIRED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">55</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">55</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_REQUIREDTIMEDELAYNOTEXPIRED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_UPLOADDOWNLOADNOTACCEPTED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">112</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">112</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_UPLOADDOWNLOADNOTACCEPTED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_TRANSFERDATASUSPENDED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">113</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">113</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_TRANSFERDATASUSPENDED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_GENERALPROGRAMMINGFAILURE</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">114</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">114</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_GENERALPROGRAMMINGFAILURE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_WRONGBLOCKSEQUENCECOUNTER</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">115</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">115</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_WRONGBLOCKSEQUENCECOUNTER</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_REQUESTCORRECTLYRECEIVEDRESPONSEPENDING</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">120</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">120</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_REQUESTCORRECTLYRECEIVEDRESPONSEPENDING</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_SUBFUNCTIONNOTSUPPORTEDINACTIVESESSION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">126</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">126</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_SUBFUNCTIONNOTSUPPORTEDINACTIVESESSION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_SERVICENOTSUPPORTEDINACTIVESESSION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">127</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">127</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_SERVICENOTSUPPORTEDINACTIVESESSION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_RPMTOOHIGH</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">129</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">129</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_RPMTOOHIGH</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_RPMTOOLOW</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">130</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">130</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_RPMTOOLOW</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_ENGINEISRUNNING</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">131</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">131</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_ENGINEISRUNNING</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_ENGINEISNOTRUNNING</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">132</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">132</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_ENGINEISNOTRUNNING</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_ENGINERUNTIMETOOLOW</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">133</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">133</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_ENGINERUNTIMETOOLOW</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_TEMPERATURETOOHIGH</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">134</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">134</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_TEMPERATURETOOHIGH</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_TEMPERATURETOOLOW</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">135</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">135</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_TEMPERATURETOOLOW</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VEHICLESPEEDTOOHIGH</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">136</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">136</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VEHICLESPEEDTOOHIGH</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VEHICLESPEEDTOOLOW</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">137</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">137</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VEHICLESPEEDTOOLOW</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_THROTTLE_PEDALTOOHIGH</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">138</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">138</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_THROTTLE_PEDALTOOHIGH</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_THROTTLE_PEDALTOOLOW</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">139</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">139</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_THROTTLE_PEDALTOOLOW</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_TRANSMISSIONRANGENOTINNEUTRAL</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">140</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">140</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_TRANSMISSIONRANGENOTINNEUTRAL</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_TRANSMISSIONRANGENOTINGEAR</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">141</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">141</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_TRANSMISSIONRANGENOTINGEAR</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_BRAKESWITCH_NOTCLOSED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">143</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">143</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_BRAKESWITCH_NOTCLOSED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_SHIFTERLEVERNOTINPARK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">144</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">144</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_SHIFTERLEVERNOTINPARK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_TORQUECONVERTERCLUTCHLOCKED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">145</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">145</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_TORQUECONVERTERCLUTCHLOCKED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VOLTAGETOOHIGH</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">146</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">146</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VOLTAGETOOHIGH</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VOLTAGETOOLOW</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">147</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">147</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VOLTAGETOOLOW</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="0a1be96b-2223-4eed-bdb0-b1519a2fdeb5">
                      <SHORT-NAME>Dem_OperationCycleStateType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DEM_CYCLE_STATE_START</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DEM_CYCLE_STATE_START</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DEM_CYCLE_STATE_END</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DEM_CYCLE_STATE_END</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="408f0270-9ab0-44c4-b44a-0c3aad3ddcc5">
                      <SHORT-NAME>EnumMEM_BlockStateType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>MEM_STATE_UNKNOWN</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>MEM_STATE_UNKNOWN</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>MEM_STATE_WRITE_PENDING</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>MEM_STATE_WRITE_PENDING</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>MEM_STATE_WRITE_FINISHED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>MEM_STATE_WRITE_FINISHED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>MEM_STATE_READ_PENDING</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>MEM_STATE_READ_PENDING</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>MEM_STATE_READ_FINISHED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>MEM_STATE_READ_FINISHED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>MEM_STATE_WRITE_FAILED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">5</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">5</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>MEM_STATE_WRITE_FAILED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>MEM_STATE_READ_FAILED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">6</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">6</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>MEM_STATE_READ_FAILED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="5cc2878c-9933-4680-a516-3bde3b5cc251">
                      <SHORT-NAME>NvM_ServiceIdType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_READ_BLOCK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">6</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">6</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_READ_BLOCK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_WRITE_BLOCK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">7</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">7</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_WRITE_BLOCK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_RESTORE_BLOCK_DEFAULTS</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">8</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">8</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_RESTORE_BLOCK_DEFAULTS</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_ERASE_BLOCK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">9</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">9</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_ERASE_BLOCK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_INVALIDATE_NV_BLOCK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">11</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">11</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_INVALIDATE_NV_BLOCK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_READ_ALL</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">12</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">12</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_READ_ALL</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="5d0ce1a3-229d-4a56-a960-47a1217227d0">
                      <SHORT-NAME>NvM_RequestResultType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_REQ_OK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_REQ_OK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_REQ_NOT_OK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_REQ_NOT_OK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_REQ_PENDING</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_REQ_PENDING</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_REQ_INTEGRITY_FAILED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_REQ_INTEGRITY_FAILED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_REQ_BLOCK_SKIPPED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_REQ_BLOCK_SKIPPED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_REQ_NV_INVALIDATED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">5</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">5</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_REQ_NV_INVALIDATED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_REQ_CANCELED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">6</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">6</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_REQ_CANCELED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_REQ_REDUNDANCY_FAILED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">7</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">7</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_REQ_REDUNDANCY_FAILED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>NVM_REQ_RESTORED_FROM_ROM</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">8</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">8</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>NVM_REQ_RESTORED_FROM_ROM</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                  </ELEMENTS>
                </AR-PACKAGE>
              </AR-PACKAGES>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="327bee04-bff0-4647-b256-cdad16fe2dff">
              <SHORT-NAME>Interfaces</SHORT-NAME>
              <ELEMENTS>
                <SENDER-RECEIVER-INTERFACE UUID="b8ba40e5-db3d-4f9c-a577-e70b24c57f43">
                  <SHORT-NAME>PiGeneric</SHORT-NAME>
                  <IS-SERVICE>false</IS-SERVICE>
                  <DATA-ELEMENTS>
                    <VARIABLE-DATA-PROTOTYPE UUID="168e7f51-526c-433f-a734-232f56f81466">
                      <SHORT-NAME>DeGeneric</SHORT-NAME>
                      <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                    </VARIABLE-DATA-PROTOTYPE>
                  </DATA-ELEMENTS>
                </SENDER-RECEIVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="d355ed62-f12d-4a55-b351-977c5393118b">
                  <SHORT-NAME>PiUR_CN_FlexRay_oChannel_A_8b187a93</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>VENDOR-SPECIFIC</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="ebc3f082-f1ed-4327-a32c-5df88f44554f">
                      <SHORT-NAME>GetCurrentComMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="072084d4-2a8d-40cf-8c40-3526ba43677d">
                          <SHORT-NAME>ComMode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/ComM_ModeType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_FlexRay_oChannel_A_8b187a93/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="cf4b543c-ae2e-4983-91c7-ec00bc60e8e7">
                      <SHORT-NAME>GetMaxComMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="a76945d0-e8c6-45cd-8b98-2baae8e2a882">
                          <SHORT-NAME>ComMode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/ComM_ModeType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_FlexRay_oChannel_A_8b187a93/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="7d618e7b-3693-4097-a2d9-6837d61e4fae">
                      <SHORT-NAME>GetRequestedComMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="31d339b7-9299-48e8-a9dd-63752d0ea73d">
                          <SHORT-NAME>ComMode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/ComM_ModeType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_FlexRay_oChannel_A_8b187a93/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="3b8b7574-d1d7-4f34-9cb7-518ced43d89d">
                      <SHORT-NAME>RequestComMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="8b48aac5-041b-4bfa-a2b9-e57281742417">
                          <SHORT-NAME>ComMode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/ComM_ModeType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_FlexRay_oChannel_A_8b187a93/E_NOT_OK</POSSIBLE-ERROR-REF>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_FlexRay_oChannel_A_8b187a93/E_MODE_LIMITATION</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="0975d7ea-c90c-4f5a-9d1b-2c715cc93fb4">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                    <APPLICATION-ERROR UUID="b96ae6c9-d159-4512-ab17-d685b63950dd">
                      <SHORT-NAME>E_MODE_LIMITATION</SHORT-NAME>
                      <ERROR-CODE>2</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="51554cbd-8ad7-43f0-96e4-6d48171a1e50">
                  <SHORT-NAME>PiUR_CN_LIN00_0a7bdc9c</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>VENDOR-SPECIFIC</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="db667aae-09fc-46af-9989-c92639fea3c0">
                      <SHORT-NAME>GetCurrentComMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="13484872-dfbc-40ce-8afb-81e0fb7cfdf3">
                          <SHORT-NAME>ComMode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/ComM_ModeType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_LIN00_0a7bdc9c/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="24e72a3a-dd63-470c-9e6d-08ab6692444b">
                      <SHORT-NAME>GetMaxComMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="a0d6ae36-a745-4810-bab6-bd9efc8f7317">
                          <SHORT-NAME>ComMode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/ComM_ModeType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_LIN00_0a7bdc9c/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="4315baf8-c871-4671-989d-5edff58a2e03">
                      <SHORT-NAME>GetRequestedComMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="64b7ab37-b309-45a7-b0ec-52f87fc297c3">
                          <SHORT-NAME>ComMode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/ComM_ModeType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_LIN00_0a7bdc9c/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="798bd252-7e81-4e4f-a432-f86c1b90d51a">
                      <SHORT-NAME>RequestComMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="da37d557-ba86-4bed-a233-b7f9415901d2">
                          <SHORT-NAME>ComMode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/ComM_ModeType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_LIN00_0a7bdc9c/E_NOT_OK</POSSIBLE-ERROR-REF>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_LIN00_0a7bdc9c/E_MODE_LIMITATION</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="2630ef31-4bde-498c-9cb5-e964cc1ac275">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                    <APPLICATION-ERROR UUID="65b110b4-1150-4528-8ac1-************">
                      <SHORT-NAME>E_MODE_LIMITATION</SHORT-NAME>
                      <ERROR-CODE>2</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="37d249f2-d56c-427e-a9d6-c02ca9f0b3a0">
                  <SHORT-NAME>PiUR_CN_CAN_52ce3533</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>VENDOR-SPECIFIC</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="411290eb-58d9-4915-b428-8ded3f48b3c6">
                      <SHORT-NAME>GetCurrentComMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="7e9846f9-862e-4565-9a89-32a4ca866f16">
                          <SHORT-NAME>ComMode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/ComM_ModeType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_CAN_52ce3533/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="1e047d2e-1398-4d6e-ac5f-cea886dc02d1">
                      <SHORT-NAME>GetMaxComMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="7543733d-4685-42d2-b5ce-58a639ee69bc">
                          <SHORT-NAME>ComMode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/ComM_ModeType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_CAN_52ce3533/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="d7d0d83a-ebe2-4928-9f46-3adb9c7965eb">
                      <SHORT-NAME>GetRequestedComMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="ac1fe3cb-fe18-444e-88a3-b65baa4fdf43">
                          <SHORT-NAME>ComMode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/ComM_ModeType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_CAN_52ce3533/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="ed2872cb-0e05-4087-bd82-174e44bc16b5">
                      <SHORT-NAME>RequestComMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="1968e396-5672-48df-8f7e-207ff6c80b90">
                          <SHORT-NAME>ComMode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/ComM_ModeType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_CAN_52ce3533/E_NOT_OK</POSSIBLE-ERROR-REF>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_CAN_52ce3533/E_MODE_LIMITATION</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="9955f988-b635-4138-a83b-39e36cd0854c">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                    <APPLICATION-ERROR UUID="51a87660-a453-4bc4-822d-fa1619c6acb3">
                      <SHORT-NAME>E_MODE_LIMITATION</SHORT-NAME>
                      <ERROR-CODE>2</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="a7fe8029-302d-479c-ab3e-9de7a9f99fa2">
                  <SHORT-NAME>PiDiagnosticMonitor_DEM_EVENT_StartApplication</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>VENDOR-SPECIFIC</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="9f0d70e5-0d33-47ad-b9e2-7d8151e64f68">
                      <SHORT-NAME>SetEventStatus</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="83dd9af4-fe36-4151-8359-8f31e0543e9d">
                          <SHORT-NAME>EventStatus</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/Dem_EventStatusType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiDiagnosticMonitor_DEM_EVENT_StartApplication/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="da32ab76-923b-4421-9c41-6dc8a4c812d2">
                      <SHORT-NAME>ResetEventStatus</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiDiagnosticMonitor_DEM_EVENT_StartApplication/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="64e0c41c-265e-4dea-89da-a505071f3545">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="fb8b1472-6c31-49d7-9bf4-1aefb2936bd9">
                  <SHORT-NAME>PiDcmDataService_DID_StartApplication</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>VENDOR-SPECIFIC</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="5f6e3f1d-1b11-443a-b53a-a787b035dbda">
                      <SHORT-NAME>WriteData</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="748739b0-0bc0-4cdf-9f2d-1eaa6c1762ff">
                          <SHORT-NAME>Data</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/Dcm_Data2ByteType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="a19c29b5-fb36-4192-9892-3e70a4496bd7">
                          <SHORT-NAME>ErrorCode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/Dcm_NegativeResponseCodeType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="09659fb1-60ba-42a2-adb8-bf3bbfd67164">
                      <SHORT-NAME>ReadData</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="73001b04-0c46-4e90-b723-32dd128d4060">
                          <SHORT-NAME>Data</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/Dcm_Data2ByteType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="3983fd7f-ed9e-4677-be42-872005711e2f">
                      <SHORT-NAME>ConditionCheckRead</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="22745c7b-dd76-486d-b6b8-f205f61e63d1">
                          <SHORT-NAME>ErrorCode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/Dcm_NegativeResponseCodeType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="f6dbfdb1-2682-4ec0-869a-1b3b7811c0e6">
                  <SHORT-NAME>PiDemDataService_DemDataElementClass_StartApplication</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>VENDOR-SPECIFIC</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="985f6969-4269-4a0e-a909-dde95aebaecc">
                      <SHORT-NAME>ReadData</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="97cc6087-356b-454f-9edf-068cb1f4ea1d">
                          <SHORT-NAME>Data</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/DataArray_Type_2</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="362dd74b-4e43-4c98-8cda-fb34553bc5dc">
                  <SHORT-NAME>PiDemOpCycle_PowerCycle</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>VENDOR-SPECIFIC</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="8c34d4fc-85a8-4b0f-9e10-f717d0170fe0">
                      <SHORT-NAME>SetOperationCycleState</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="26fe88bc-aafa-4bfe-8866-1f760002cd1b">
                          <SHORT-NAME>CycleState</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/Dem_OperationCycleStateType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiDemOpCycle_PowerCycle/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="53a7668e-8528-4f46-a14c-11505c059302">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <SENDER-RECEIVER-INTERFACE UUID="8b912369-4393-4bab-b9ef-b9e5f6231ed0">
                  <SHORT-NAME>PiSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx</SHORT-NAME>
                  <IS-SERVICE>false</IS-SERVICE>
                  <DATA-ELEMENTS>
                    <VARIABLE-DATA-PROTOTYPE UUID="fd854932-8829-42e6-b3c7-bd06b002df5f">
                      <SHORT-NAME>DeSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx</SHORT-NAME>
                      <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</TYPE-TREF>
                    </VARIABLE-DATA-PROTOTYPE>
                  </DATA-ELEMENTS>
                </SENDER-RECEIVER-INTERFACE>
                <SENDER-RECEIVER-INTERFACE UUID="c7d06369-df48-4c21-9bd3-27ec4c767a3e">
                  <SHORT-NAME>PiSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</SHORT-NAME>
                  <IS-SERVICE>false</IS-SERVICE>
                  <DATA-ELEMENTS>
                    <VARIABLE-DATA-PROTOTYPE UUID="b3a61539-d827-486a-af56-3cecea5150c0">
                      <SHORT-NAME>DeSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</SHORT-NAME>
                      <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</TYPE-TREF>
                    </VARIABLE-DATA-PROTOTYPE>
                  </DATA-ELEMENTS>
                </SENDER-RECEIVER-INTERFACE>
                <SENDER-RECEIVER-INTERFACE UUID="cb44a0ba-2010-4436-abba-b77d10439115">
                  <SHORT-NAME>PiSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</SHORT-NAME>
                  <IS-SERVICE>false</IS-SERVICE>
                  <DATA-ELEMENTS>
                    <VARIABLE-DATA-PROTOTYPE UUID="da3f244a-5410-49c7-adba-32aff1926d3b">
                      <SHORT-NAME>DeSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</SHORT-NAME>
                      <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</TYPE-TREF>
                    </VARIABLE-DATA-PROTOTYPE>
                  </DATA-ELEMENTS>
                </SENDER-RECEIVER-INTERFACE>
                <SENDER-RECEIVER-INTERFACE UUID="200bc709-1008-4777-838d-d17d947d99bb">
                  <SHORT-NAME>PiSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</SHORT-NAME>
                  <IS-SERVICE>false</IS-SERVICE>
                  <DATA-ELEMENTS>
                    <VARIABLE-DATA-PROTOTYPE UUID="629992ae-22c9-4e7b-a1b0-6efb1bce3647">
                      <SHORT-NAME>DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</SHORT-NAME>
                      <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/Dt_SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</TYPE-TREF>
                    </VARIABLE-DATA-PROTOTYPE>
                  </DATA-ELEMENTS>
                </SENDER-RECEIVER-INTERFACE>
                <SENDER-RECEIVER-INTERFACE UUID="447894d4-a0dd-4ce7-9cea-d10fbad8049a">
                  <SHORT-NAME>PiSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx</SHORT-NAME>
                  <IS-SERVICE>false</IS-SERVICE>
                  <DATA-ELEMENTS>
                    <VARIABLE-DATA-PROTOTYPE UUID="0c18e867-0b49-476c-90b9-13b14fea880c">
                      <SHORT-NAME>DeSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx</SHORT-NAME>
                      <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</TYPE-TREF>
                    </VARIABLE-DATA-PROTOTYPE>
                  </DATA-ELEMENTS>
                </SENDER-RECEIVER-INTERFACE>
                <SENDER-RECEIVER-INTERFACE UUID="3aa8d3ed-b9b9-48dd-8d4c-f457fc5a3aa5">
                  <SHORT-NAME>PiStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx</SHORT-NAME>
                  <IS-SERVICE>false</IS-SERVICE>
                  <DATA-ELEMENTS>
                    <VARIABLE-DATA-PROTOTYPE UUID="13a89412-0935-4cc4-977f-f290d0168905">
                      <SHORT-NAME>DeStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx</SHORT-NAME>
                      <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</TYPE-TREF>
                    </VARIABLE-DATA-PROTOTYPE>
                  </DATA-ELEMENTS>
                </SENDER-RECEIVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="51db198a-995c-4a6e-a2a2-f768a9064b8e">
                  <SHORT-NAME>PiPS_StartApplication_NvMBlock1</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>VENDOR-SPECIFIC</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="a1556d47-38da-4d27-8c00-d9887efe4006">
                      <SHORT-NAME>WriteBlock</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="7f3b31ef-cfba-433a-8241-a7fe089c4cb6">
                          <SHORT-NAME>SrcPtr</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiPS_StartApplication_NvMBlock1/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="3f73c3a2-1b67-476b-a594-c3d69d493bb4">
                      <SHORT-NAME>ReadBlock</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="ff469e37-796e-4def-b562-0d35935cba0f">
                          <SHORT-NAME>DstPtr</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiPS_StartApplication_NvMBlock1/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="2a6902d0-62fe-4ec2-bba2-d4539f7ec196">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="ed84f095-5fdc-4aa0-8216-728c44c05a8c">
                  <SHORT-NAME>PiNvM_RpNotifyJobEnd_StartApplication_NvMBlock1</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>VENDOR-SPECIFIC</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="d0e43957-2e73-4d4f-9f48-0b13a35b3d1f">
                      <SHORT-NAME>JobFinished</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="71087d23-e456-4450-bf53-66257eef0bab">
                          <SHORT-NAME>ServiceId</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/NvM_ServiceIdType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="9263a678-fc42-46e3-ace9-2c1a4da253bf">
                          <SHORT-NAME>JobResult</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="e1421dc3-4ff2-4ded-8fc6-d22254b6465c">
                  <SHORT-NAME>PiPS_StartApplication_NvMBlock2</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>VENDOR-SPECIFIC</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="642e70e0-47dc-47f5-8daf-fc640a614d3e">
                      <SHORT-NAME>WriteBlock</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="70d1f80e-1ea4-4e56-879e-a7c1afad4e0d">
                          <SHORT-NAME>SrcPtr</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiPS_StartApplication_NvMBlock2/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="c928a5e1-e1cc-431a-a267-a751addb86fe">
                      <SHORT-NAME>ReadBlock</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="441dd5df-f420-499b-8c53-1fad4ea17a68">
                          <SHORT-NAME>DstPtr</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/StartApplication/StartApplication_swc/Interfaces/PiPS_StartApplication_NvMBlock2/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="2879bd9d-feff-4405-a94c-a08066f1b4c3">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="339be843-3335-40a1-a900-6432b1e7353e">
                  <SHORT-NAME>PiNvM_RpNotifyJobEnd_StartApplication_NvMBlock2</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>VENDOR-SPECIFIC</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="fb3d7c6e-504a-47c2-8455-3c6807ead98d">
                      <SHORT-NAME>JobFinished</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="c63006d5-3eaf-4972-8096-199259fd8423">
                          <SHORT-NAME>ServiceId</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/NvM_ServiceIdType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="22df63e6-1018-46e5-9432-c932966e42eb">
                          <SHORT-NAME>JobResult</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                </CLIENT-SERVER-INTERFACE>
                <SENDER-RECEIVER-INTERFACE UUID="9e8605e5-9d53-4175-978f-34733ad9d56d">
                  <SHORT-NAME>PiSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx</SHORT-NAME>
                  <IS-SERVICE>false</IS-SERVICE>
                  <DATA-ELEMENTS>
                    <VARIABLE-DATA-PROTOTYPE UUID="ecf05387-3c12-4ae8-910b-61859417fdae">
                      <SHORT-NAME>DeSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx</SHORT-NAME>
                      <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                    </VARIABLE-DATA-PROTOTYPE>
                  </DATA-ELEMENTS>
                </SENDER-RECEIVER-INTERFACE>
                <SENDER-RECEIVER-INTERFACE UUID="7f283877-26e3-42ca-8681-6c8724b9968f">
                  <SHORT-NAME>PiSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx</SHORT-NAME>
                  <IS-SERVICE>false</IS-SERVICE>
                  <DATA-ELEMENTS>
                    <VARIABLE-DATA-PROTOTYPE UUID="e5016584-2776-40b0-864d-4bf585610daf">
                      <SHORT-NAME>DeSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx</SHORT-NAME>
                      <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                    </VARIABLE-DATA-PROTOTYPE>
                  </DATA-ELEMENTS>
                </SENDER-RECEIVER-INTERFACE>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="42fd4bd3-**************-19a408c48bf4">
              <SHORT-NAME>ComponentTypes</SHORT-NAME>
              <ELEMENTS>
                <APPLICATION-SW-COMPONENT-TYPE UUID="dd4881f1-fab9-4627-b55a-6fe92e821276">
                  <SHORT-NAME>StartApplication</SHORT-NAME>
                  <PORTS>
                    <R-PORT-PROTOTYPE UUID="a32fc1ee-a540-4f65-9984-46b73ed287b0">
                      <SHORT-NAME>UR_CN_FlexRay_oChannel_A_8b187a93</SHORT-NAME>
                      <REQUIRED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_FlexRay_oChannel_A_8b187a93</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <R-PORT-PROTOTYPE UUID="756cf99e-2c4d-4fe7-a0d7-7c551075c2ed">
                      <SHORT-NAME>UR_CN_LIN00_0a7bdc9c</SHORT-NAME>
                      <REQUIRED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_LIN00_0a7bdc9c</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <R-PORT-PROTOTYPE UUID="94611c98-ce5c-4f68-a73a-56254a30093c">
                      <SHORT-NAME>UR_CN_CAN_52ce3533</SHORT-NAME>
                      <REQUIRED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_CAN_52ce3533</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <R-PORT-PROTOTYPE UUID="c39188c3-4ae0-484f-99cf-4d1e8989520c">
                      <SHORT-NAME>PpDiagnosticMonitor_DEM_EVENT_StartApplication</SHORT-NAME>
                      <REQUIRED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiDiagnosticMonitor_DEM_EVENT_StartApplication</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="67e6392e-521b-4ecc-bdc8-437afbd01a0f">
                      <SHORT-NAME>PpDcmDataService_DID_StartApplication</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiDcmDataService_DID_StartApplication</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="af571146-4e00-49ea-b5c6-639357a4377c">
                      <SHORT-NAME>PpDemDataService_DemDataElementClass_StartApplication</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiDemDataService_DemDataElementClass_StartApplication</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <R-PORT-PROTOTYPE UUID="dd30e3ac-d9c4-47da-8f83-429b4fdfb58f">
                      <SHORT-NAME>PpDemOpCycle_PowerCycle</SHORT-NAME>
                      <REQUIRED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiDemOpCycle_PowerCycle</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="4cba2ac2-8752-41e4-ab99-962900d0003e">
                      <SHORT-NAME>PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx</SHORT-NAME>
                      <PROVIDED-COM-SPECS>
                        <NONQUEUED-SENDER-COM-SPEC>
                          <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx/DeSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx</DATA-ELEMENT-REF>
                          <INIT-VALUE>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </INIT-VALUE>
                        </NONQUEUED-SENDER-COM-SPEC>
                      </PROVIDED-COM-SPECS>
                      <PROVIDED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <R-PORT-PROTOTYPE UUID="fcf421bf-6bc2-4649-99d3-0d227b190efb">
                      <SHORT-NAME>PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</SHORT-NAME>
                      <REQUIRED-COM-SPECS>
                        <NONQUEUED-RECEIVER-COM-SPEC>
                          <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx/DeSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</DATA-ELEMENT-REF>
                          <HANDLE-OUT-OF-RANGE>NONE</HANDLE-OUT-OF-RANGE>
                          <HANDLE-OUT-OF-RANGE-STATUS>SILENT</HANDLE-OUT-OF-RANGE-STATUS>
                          <INIT-VALUE>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <VALUE>1</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </INIT-VALUE>
                        </NONQUEUED-RECEIVER-COM-SPEC>
                      </REQUIRED-COM-SPECS>
                      <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="b37f3661-347b-4f2d-b9ad-6c27aa7ba35d">
                      <SHORT-NAME>PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</SHORT-NAME>
                      <PROVIDED-COM-SPECS>
                        <NONQUEUED-SENDER-COM-SPEC>
                          <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx/DeSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</DATA-ELEMENT-REF>
                          <INIT-VALUE>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </INIT-VALUE>
                        </NONQUEUED-SENDER-COM-SPEC>
                      </PROVIDED-COM-SPECS>
                      <PROVIDED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <R-PORT-PROTOTYPE UUID="6c3e8baa-e304-4d65-9545-3594c6e2407e">
                      <SHORT-NAME>PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</SHORT-NAME>
                      <REQUIRED-COM-SPECS>
                        <NONQUEUED-RECEIVER-COM-SPEC>
                          <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</DATA-ELEMENT-REF>
                          <HANDLE-OUT-OF-RANGE>NONE</HANDLE-OUT-OF-RANGE>
                          <HANDLE-OUT-OF-RANGE-STATUS>SILENT</HANDLE-OUT-OF-RANGE-STATUS>
                          <INIT-VALUE>
                            <RECORD-VALUE-SPECIFICATION>
                              <FIELDS>
                                <NUMERICAL-VALUE-SPECIFICATION>
                                  <VALUE>0</VALUE>
                                </NUMERICAL-VALUE-SPECIFICATION>
                                <NUMERICAL-VALUE-SPECIFICATION>
                                  <VALUE>0</VALUE>
                                </NUMERICAL-VALUE-SPECIFICATION>
                                <NUMERICAL-VALUE-SPECIFICATION>
                                  <VALUE>0</VALUE>
                                </NUMERICAL-VALUE-SPECIFICATION>
                                <NUMERICAL-VALUE-SPECIFICATION>
                                  <VALUE>0</VALUE>
                                </NUMERICAL-VALUE-SPECIFICATION>
                                <NUMERICAL-VALUE-SPECIFICATION>
                                  <VALUE>0</VALUE>
                                </NUMERICAL-VALUE-SPECIFICATION>
                                <NUMERICAL-VALUE-SPECIFICATION>
                                  <VALUE>0</VALUE>
                                </NUMERICAL-VALUE-SPECIFICATION>
                              </FIELDS>
                            </RECORD-VALUE-SPECIFICATION>
                          </INIT-VALUE>
                        </NONQUEUED-RECEIVER-COM-SPEC>
                      </REQUIRED-COM-SPECS>
                      <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="944118f8-268f-4aca-bdc1-58e775e1eddd">
                      <SHORT-NAME>PpSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx</SHORT-NAME>
                      <PROVIDED-COM-SPECS>
                        <NONQUEUED-SENDER-COM-SPEC>
                          <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx/DeSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx</DATA-ELEMENT-REF>
                          <INIT-VALUE>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </INIT-VALUE>
                        </NONQUEUED-SENDER-COM-SPEC>
                      </PROVIDED-COM-SPECS>
                      <PROVIDED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <R-PORT-PROTOTYPE UUID="d7ed9a22-5c10-4029-8745-65bc9a611863">
                      <SHORT-NAME>PpStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx</SHORT-NAME>
                      <REQUIRED-COM-SPECS>
                        <NONQUEUED-RECEIVER-COM-SPEC>
                          <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx/DeStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx</DATA-ELEMENT-REF>
                          <HANDLE-OUT-OF-RANGE>NONE</HANDLE-OUT-OF-RANGE>
                          <HANDLE-OUT-OF-RANGE-STATUS>SILENT</HANDLE-OUT-OF-RANGE-STATUS>
                          <INIT-VALUE>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </INIT-VALUE>
                        </NONQUEUED-RECEIVER-COM-SPEC>
                      </REQUIRED-COM-SPECS>
                      <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="e9bf636f-5adf-434c-bc1d-46989fddc80e">
                      <SHORT-NAME>PpNvM_RpNotifyJobEnd_StartApplication_NvMBlock1</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiNvM_RpNotifyJobEnd_StartApplication_NvMBlock1</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <R-PORT-PROTOTYPE UUID="e9cb01ca-ab1a-4ef3-bac8-fdf1d4a29196">
                      <SHORT-NAME>PpPS_StartApplication_NvMBlock1</SHORT-NAME>
                      <REQUIRED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiPS_StartApplication_NvMBlock1</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="297ff343-aac8-4d7f-a1b0-4d5cd2f0e666">
                      <SHORT-NAME>PpNvM_RpNotifyJobEnd_StartApplication_NvMBlock2</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiNvM_RpNotifyJobEnd_StartApplication_NvMBlock2</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <R-PORT-PROTOTYPE UUID="d6ae4b29-ef67-41e0-a2c4-f85ab4ba85fa">
                      <SHORT-NAME>PpPS_StartApplication_NvMBlock2</SHORT-NAME>
                      <REQUIRED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiPS_StartApplication_NvMBlock2</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <R-PORT-PROTOTYPE UUID="*************-41c8-8b1e-87f709b92a20">
                      <SHORT-NAME>PpSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx</SHORT-NAME>
                      <REQUIRED-COM-SPECS>
                        <NONQUEUED-RECEIVER-COM-SPEC>
                          <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx/DeSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx</DATA-ELEMENT-REF>
                          <HANDLE-OUT-OF-RANGE>NONE</HANDLE-OUT-OF-RANGE>
                          <HANDLE-OUT-OF-RANGE-STATUS>SILENT</HANDLE-OUT-OF-RANGE-STATUS>
                          <INIT-VALUE>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </INIT-VALUE>
                        </NONQUEUED-RECEIVER-COM-SPEC>
                      </REQUIRED-COM-SPECS>
                      <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="46a6e42a-ef64-4f17-a8d4-fcfe8c8e8ea5">
                      <SHORT-NAME>PpSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx</SHORT-NAME>
                      <PROVIDED-COM-SPECS>
                        <NONQUEUED-SENDER-COM-SPEC>
                          <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx/DeSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx</DATA-ELEMENT-REF>
                          <INIT-VALUE>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </INIT-VALUE>
                        </NONQUEUED-SENDER-COM-SPEC>
                      </PROVIDED-COM-SPECS>
                      <PROVIDED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/StartApplication/StartApplication_swc/Interfaces/PiSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                  </PORTS>
                  <INTERNAL-BEHAVIORS>
                    <SWC-INTERNAL-BEHAVIOR UUID="df0ae31f-1e1a-461e-8da8-1f580a68fdbc">
                      <SHORT-NAME>StartApplicationInternalBehavior</SHORT-NAME>
                      <AR-TYPED-PER-INSTANCE-MEMORYS>
                        <VARIABLE-DATA-PROTOTYPE UUID="50facf25-1406-417d-a401-7ee69595ee75">
                          <SHORT-NAME>Uptime</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="e4cdb579-00df-4341-9b1b-71381cbc81de">
                          <SHORT-NAME>TxCtrlSigValue</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="ef700172-190e-4872-9937-ab6b19220bb3">
                          <SHORT-NAME>TxDataSigValue</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="97e980ff-fbe9-4fc1-b09d-4e44b9d3d6f5">
                          <SHORT-NAME>ActiveComponent</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/EnumActiveComponentType</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="2bbc5bcc-a153-462d-94b2-97be0b6bc308">
                          <SHORT-NAME>DIAG_LastRxData</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="fde0bd98-50d2-40c6-bf29-22e99c0a57d8">
                          <SHORT-NAME>RxDataSgBuffer</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/Dt_SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="4e83c4bb-c713-41ff-ab11-8c3ffc14be70">
                          <SHORT-NAME>COM_RxSigValue0</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="ba24f254-43f1-4abb-a7bc-e1a067aea5a2">
                          <SHORT-NAME>COM_TxSigValue0</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="d2a51c5a-7318-4b5b-bc49-e215a7a7a3fd">
                          <SHORT-NAME>Shadow_PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx_DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2_a3f6706d</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/Dt_SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="9db527cb-1c41-4f59-bead-c67a550d82e8">
                          <SHORT-NAME>COM_RxSigValue1</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="dd9b57ec-0d7c-4f61-9489-95d66d2b2f54">
                          <SHORT-NAME>COM_TxSigValue1</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="c758d48c-055e-48ac-9ca6-12c74c6d332e">
                          <SHORT-NAME>MEM_ActiveBlock</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="f6c52566-b7cf-461c-ac2d-3f31944e37e5">
                          <SHORT-NAME>MEM_DataBuffer1</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="7384b010-1706-4920-aa3b-09f9df0fc968">
                          <SHORT-NAME>MEM_DataBuffer2</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="f5ce049e-8370-4686-996d-7391af89fc04">
                          <SHORT-NAME>MEM_BlockState1</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/EnumMEM_BlockStateType</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="95712f5e-5cd4-4303-83ea-a5a4d71e24e6">
                          <SHORT-NAME>MEM_BlockState2</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/StartApplication/StartApplication_swc/DataTypes/EnumMEM_BlockStateType</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="61f2ef09-1346-4281-9607-1b389bba4741">
                          <SHORT-NAME>COM_RxSigValue2</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                        <VARIABLE-DATA-PROTOTYPE UUID="527ed23a-d458-42f5-88ca-5d72d9aa02a2">
                          <SHORT-NAME>COM_TxSigValue2</SHORT-NAME>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</TYPE-TREF>
                        </VARIABLE-DATA-PROTOTYPE>
                      </AR-TYPED-PER-INSTANCE-MEMORYS>
                      <EVENTS>
                        <TIMING-EVENT UUID="26130783-322b-4db5-ae84-bc21ad73f1d0">
                          <SHORT-NAME>Timer_StartApplication_Cyclic1ms</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/StartApplication_Cyclic1ms</START-ON-EVENT-REF>
                          <PERIOD>0.001</PERIOD>
                        </TIMING-EVENT>
                        <TIMING-EVENT UUID="56dc92b9-4832-4d5e-a5cc-fc4572be4999">
                          <SHORT-NAME>Timer_StartApplication_Cyclic10ms</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/StartApplication_Cyclic10ms</START-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </TIMING-EVENT>
                        <TIMING-EVENT UUID="adb3cec7-338b-44c4-b6f9-9387bee6a4c9">
                          <SHORT-NAME>Timer_StartApplication_Cyclic250ms</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/StartApplication_Cyclic250ms</START-ON-EVENT-REF>
                          <PERIOD>0.25</PERIOD>
                        </TIMING-EVENT>
                        <TIMING-EVENT UUID="622e97a2-92f8-4b85-bc9b-53673182b491">
                          <SHORT-NAME>Timer_StartApplication_Cyclic1000ms</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/StartApplication_Cyclic1000ms</START-ON-EVENT-REF>
                          <PERIOD>1</PERIOD>
                        </TIMING-EVENT>
                        <INIT-EVENT UUID="e975cd82-bc75-4d69-ac8b-e1d7ae7fa440">
                          <SHORT-NAME>Timer_StartApplication_Init</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/StartApplication_Init</START-ON-EVENT-REF>
                        </INIT-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="95a74954-61fa-4abe-9c59-ea9f7973c3f2">
                          <SHORT-NAME>OpEventStartApplication_DIAG_DcmReadData_ReadData_PpDcmDataService_DID_StartApplication</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/StartApplication_DIAG_DcmReadData</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpDcmDataService_DID_StartApplication</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiDcmDataService_DID_StartApplication/ReadData</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="864f108d-1713-4e0c-a57e-6440c8658272">
                          <SHORT-NAME>OpEventStartApplication_DIAG_DcmWriteData_WriteData_PpDcmDataService_DID_StartApplication</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/StartApplication_DIAG_DcmWriteData</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpDcmDataService_DID_StartApplication</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiDcmDataService_DID_StartApplication/WriteData</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="e53bf663-fcbb-408d-b444-056c8c2298c6">
                          <SHORT-NAME>OpEventStartApplication_DIAG_ConditionCheckRead_ConditionCheckRead_PpDcmDataService_DID_StartApplication</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/StartApplication_DIAG_ConditionCheckRead</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpDcmDataService_DID_StartApplication</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiDcmDataService_DID_StartApplication/ConditionCheckRead</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="500c5a15-6361-4e35-b428-75c5670ae5fd">
                          <SHORT-NAME>OpEventStartApplication_DIAG_DemReadData_ReadData_PpDemDataService_DemDataElementClass_StartApplication</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/StartApplication_DIAG_DemReadData</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpDemDataService_DemDataElementClass_StartApplication</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiDemDataService_DemDataElementClass_StartApplication/ReadData</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <DATA-RECEIVED-EVENT UUID="3f5f2fb2-b61d-4400-9cd9-df59d4d4cd82">
                          <SHORT-NAME>DataReceivedEvent_StartApplication_OnDataRec_RxCtrl_PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_DeSigna_7b663139</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/StartApplication_OnDataRec_RxCtrl</START-ON-EVENT-REF>
                          <DATA-IREF>
                            <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</CONTEXT-R-PORT-REF>
                            <TARGET-DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx/DeSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</TARGET-DATA-ELEMENT-REF>
                          </DATA-IREF>
                        </DATA-RECEIVED-EVENT>
                        <DATA-RECEIVED-EVENT UUID="42da2569-a477-45a5-8ebe-c96a7983f5fe">
                          <SHORT-NAME>DataReceivedEvent_StartApplication_OnDataRec_RxData_PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af_7f5b60fc</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/StartApplication_OnDataRec_RxData</START-ON-EVENT-REF>
                          <DATA-IREF>
                            <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</CONTEXT-R-PORT-REF>
                            <TARGET-DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</TARGET-DATA-ELEMENT-REF>
                          </DATA-IREF>
                        </DATA-RECEIVED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="03ffc01d-2ae9-438d-ae2d-5e0e21f8d86b">
                          <SHORT-NAME>OpEventStartApplication_MEM_JobFinished_StartApplication_NvMBlock1_JobFinished_PpNvM_RpNotifyJobEnd_StartApplication_NvMBlock1</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/StartApplication_MEM_JobFinished_StartApplication_NvMBlock1</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpNvM_RpNotifyJobEnd_StartApplication_NvMBlock1</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiNvM_RpNotifyJobEnd_StartApplication_NvMBlock1/JobFinished</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="60847c47-df17-4ab1-8b49-df4f8aed2c1d">
                          <SHORT-NAME>OpEventStartApplication_MEM_JobFinished_StartApplication_NvMBlock2_JobFinished_PpNvM_RpNotifyJobEnd_StartApplication_NvMBlock2</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/StartApplication_MEM_JobFinished_StartApplication_NvMBlock2</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpNvM_RpNotifyJobEnd_StartApplication_NvMBlock2</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiNvM_RpNotifyJobEnd_StartApplication_NvMBlock2/JobFinished</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                      </EVENTS>
                      <EXPLICIT-INTER-RUNNABLE-VARIABLES>
                        <VARIABLE-DATA-PROTOTYPE UUID="19b8ae5c-d251-4ba1-9cc2-1d83ebaba73a">
                          <SHORT-NAME>IrvOccuranceCounterDid</SHORT-NAME>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</TYPE-TREF>
                          <INIT-VALUE>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </INIT-VALUE>
                        </VARIABLE-DATA-PROTOTYPE>
                      </EXPLICIT-INTER-RUNNABLE-VARIABLES>
                      <HANDLE-TERMINATION-AND-RESTART>NO-SUPPORT</HANDLE-TERMINATION-AND-RESTART>
                      <PORT-API-OPTIONS>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/UR_CN_FlexRay_oChannel_A_8b187a93</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/UR_CN_LIN00_0a7bdc9c</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/UR_CN_CAN_52ce3533</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpDiagnosticMonitor_DEM_EVENT_StartApplication</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpDcmDataService_DID_StartApplication</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpDemDataService_DemDataElementClass_StartApplication</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpDemOpCycle_PowerCycle</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpNvM_RpNotifyJobEnd_StartApplication_NvMBlock1</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpPS_StartApplication_NvMBlock1</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpNvM_RpNotifyJobEnd_StartApplication_NvMBlock2</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpPS_StartApplication_NvMBlock2</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx</PORT-REF>
                        </PORT-API-OPTION>
                      </PORT-API-OPTIONS>
                      <RUNNABLES>
                        <RUNNABLE-ENTITY UUID="0e93c6ee-4004-4fad-bde9-d1c678de8c5e">
                          <SHORT-NAME>StartApplication_Cyclic1ms</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>false</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StartApplication_Cyclic1ms</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="068a4a0d-3856-4b20-9443-09f548bc6e67">
                          <SHORT-NAME>StartApplication_Cyclic10ms</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>false</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StartApplication_Cyclic10ms</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="9968be2b-4ae1-45a4-870a-97a48a3cbe65">
                          <SHORT-NAME>StartApplication_Cyclic250ms</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>false</CAN-BE-INVOKED-CONCURRENTLY>
                          <DATA-RECEIVE-POINT-BY-ARGUMENTS>
                            <VARIABLE-ACCESS UUID="e1c38029-c6ff-4742-bd66-ffd7ae22e239">
                              <SHORT-NAME>DataReceivePointStartApplication_Cyclic250ms_PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_DeSignal_Rx20b_1dbb57ee</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx/DeSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                            <VARIABLE-ACCESS UUID="bf344397-cc44-4c59-ae48-3e0c196bee79">
                              <SHORT-NAME>DataReceivePointStartApplication_Cyclic250ms_PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx_De_ab8f4a62</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                            <VARIABLE-ACCESS UUID="79bd23f3-690b-406a-89b0-db907eddaf80">
                              <SHORT-NAME>DataReceivePointStartApplication_Cyclic250ms_PpStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx_De_c5a501ec</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx/DeStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                            <VARIABLE-ACCESS UUID="03a9e4dd-aa3e-486e-87bb-cfa6fbce1d2c">
                              <SHORT-NAME>DataReceivePointStartApplication_Cyclic250ms_PpSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx_DeSig_S_ca1f65e4</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx/DeSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </DATA-RECEIVE-POINT-BY-ARGUMENTS>
                          <DATA-SEND-POINTS>
                            <VARIABLE-ACCESS UUID="015a8f6a-61bf-456b-a196-567f154414ce">
                              <SHORT-NAME>DataSendPointStartApplication_Cyclic250ms_PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx_DeSignal_Tx24bit_Cy_a73a705f</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx/DeSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                            <VARIABLE-ACCESS UUID="4d375b8c-3b29-43db-9a92-1ca6ec0f5bab">
                              <SHORT-NAME>DataSendPointStartApplication_Cyclic250ms_PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx_DeSignal_Tx10bit_988b147d</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx/DeSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                            <VARIABLE-ACCESS UUID="52a04bf1-563b-457c-8258-65d46c593ad8">
                              <SHORT-NAME>DataSendPointStartApplication_Cyclic250ms_PpSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx_DeSignal_SomeTxSignal_o_a997a995</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx/DeSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                            <VARIABLE-ACCESS UUID="3feeba60-0241-4441-a3ad-be6669dfcfa3">
                              <SHORT-NAME>DataSendPointStartApplication_Cyclic250ms_PpSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx_DeSig_StartA_04398d4d</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx/DeSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </DATA-SEND-POINTS>
                          <READ-LOCAL-VARIABLES>
                            <VARIABLE-ACCESS UUID="38853df1-3b53-44ed-90ef-0772698f9ce8">
                              <SHORT-NAME>StartApplication_Cyclic250ms_IrvOccuranceCounterDid_DIRECT_READ</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <LOCAL-VARIABLE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/IrvOccuranceCounterDid</LOCAL-VARIABLE-REF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </READ-LOCAL-VARIABLES>
                          <SERVER-CALL-POINTS>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="67fa3ace-8205-4eb9-9463-57f975b90598">
                              <SHORT-NAME>OpServerCallStartApplication_Cyclic250ms_GetCurrentComMode_UR_CN_FlexRay_oChannel_A_8b187a93</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/UR_CN_FlexRay_oChannel_A_8b187a93</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_FlexRay_oChannel_A_8b187a93/GetCurrentComMode</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="2d8505a3-2f6f-4d6f-890d-f141f646f247">
                              <SHORT-NAME>OpServerCallStartApplication_Cyclic250ms_RequestComMode_UR_CN_FlexRay_oChannel_A_8b187a93</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/UR_CN_FlexRay_oChannel_A_8b187a93</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_FlexRay_oChannel_A_8b187a93/RequestComMode</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="1ada88d2-b49f-4f75-821d-0edc17b7d60f">
                              <SHORT-NAME>OpServerCallStartApplication_Cyclic250ms_GetCurrentComMode_UR_CN_LIN00_0a7bdc9c</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/UR_CN_LIN00_0a7bdc9c</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_LIN00_0a7bdc9c/GetCurrentComMode</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="f2ff13c6-c772-4b64-87c4-be7438a3d99f">
                              <SHORT-NAME>OpServerCallStartApplication_Cyclic250ms_RequestComMode_UR_CN_LIN00_0a7bdc9c</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/UR_CN_LIN00_0a7bdc9c</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_LIN00_0a7bdc9c/RequestComMode</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="e2c994d2-09ec-4752-9801-4c8850e417ed">
                              <SHORT-NAME>OpServerCallStartApplication_Cyclic250ms_GetCurrentComMode_UR_CN_CAN_52ce3533</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/UR_CN_CAN_52ce3533</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_CAN_52ce3533/GetCurrentComMode</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="12cfa10e-2293-4c17-a5c4-44912d69a288">
                              <SHORT-NAME>OpServerCallStartApplication_Cyclic250ms_RequestComMode_UR_CN_CAN_52ce3533</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/UR_CN_CAN_52ce3533</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_CAN_52ce3533/RequestComMode</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="02751772-605d-47e0-9a26-73dd1fd5ed08">
                              <SHORT-NAME>OpServerCallStartApplication_Cyclic250ms_ReadBlock_PpPS_StartApplication_NvMBlock1</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpPS_StartApplication_NvMBlock1</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiPS_StartApplication_NvMBlock1/ReadBlock</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="68f2722f-9f89-4af0-b5d5-886d23355d45">
                              <SHORT-NAME>OpServerCallStartApplication_Cyclic250ms_ReadBlock_PpPS_StartApplication_NvMBlock2</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpPS_StartApplication_NvMBlock2</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiPS_StartApplication_NvMBlock2/ReadBlock</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                          </SERVER-CALL-POINTS>
                          <SYMBOL>StartApplication_Cyclic250ms</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="0585992b-e30a-417b-991c-4661f08ceef9">
                          <SHORT-NAME>StartApplication_Cyclic1000ms</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>false</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StartApplication_Cyclic1000ms</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="08607ee5-084a-4a46-8603-1f5af5b9fb17">
                          <SHORT-NAME>StartApplication_Init</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SERVER-CALL-POINTS>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="593f541f-bd86-405a-a9dc-bc942ede27ab">
                              <SHORT-NAME>OpServerCallStartApplication_Init_RequestComMode_UR_CN_FlexRay_oChannel_A_8b187a93</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/UR_CN_FlexRay_oChannel_A_8b187a93</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_FlexRay_oChannel_A_8b187a93/RequestComMode</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="ae45bc01-ae91-431c-9e9b-b87c5986787b">
                              <SHORT-NAME>OpServerCallStartApplication_Init_RequestComMode_UR_CN_LIN00_0a7bdc9c</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/UR_CN_LIN00_0a7bdc9c</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_LIN00_0a7bdc9c/RequestComMode</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="a965b836-3194-4607-9e5a-3e1dc0920006">
                              <SHORT-NAME>OpServerCallStartApplication_Init_RequestComMode_UR_CN_CAN_52ce3533</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/UR_CN_CAN_52ce3533</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiUR_CN_CAN_52ce3533/RequestComMode</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                          </SERVER-CALL-POINTS>
                          <SYMBOL>StartApplication_Init</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="18fa963f-d77d-43f5-aac4-c86eaea2c527">
                          <SHORT-NAME>StartApplication_OnDataRec_RxCtrl</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>false</CAN-BE-INVOKED-CONCURRENTLY>
                          <DATA-RECEIVE-POINT-BY-ARGUMENTS>
                            <VARIABLE-ACCESS UUID="f0d2d09b-0a23-4b93-ac4c-3fa85d808276">
                              <SHORT-NAME>DataReceivePointStartApplication_OnDataRec_RxCtrl_PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_DeSignal_68a5dd37</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx/DeSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </DATA-RECEIVE-POINT-BY-ARGUMENTS>
                          <DATA-SEND-POINTS>
                            <VARIABLE-ACCESS UUID="a4f44278-a4be-40d4-9cd1-437ab489a109">
                              <SHORT-NAME>DataSendPointStartApplication_OnDataRec_RxCtrl_PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx_DeSignal_Tx_f95bebfc</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx/DeSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </DATA-SEND-POINTS>
                          <SERVER-CALL-POINTS>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="f4c66c34-e7d2-4014-a4ce-535de646a3b1">
                              <SHORT-NAME>OpServerCallStartApplication_OnDataRec_RxCtrl_SetOperationCycleState_PpDemOpCycle_PowerCycle</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpDemOpCycle_PowerCycle</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiDemOpCycle_PowerCycle/SetOperationCycleState</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                          </SERVER-CALL-POINTS>
                          <SYMBOL>StartApplication_OnDataRec_RxCtrl</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="e1f3b2fd-eaa6-43a0-8769-610c2d834311">
                          <SHORT-NAME>StartApplication_OnDataRec_RxData</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>false</CAN-BE-INVOKED-CONCURRENTLY>
                          <DATA-RECEIVE-POINT-BY-ARGUMENTS>
                            <VARIABLE-ACCESS UUID="3e71b8f8-363b-43cd-aa16-5cf14211e3f8">
                              <SHORT-NAME>DataReceivePointStartApplication_OnDataRec_RxData_PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_9d149428</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </DATA-RECEIVE-POINT-BY-ARGUMENTS>
                          <DATA-SEND-POINTS>
                            <VARIABLE-ACCESS UUID="0ad8586a-bcc2-44a1-b689-59be7f905fd0">
                              <SHORT-NAME>DataSendPointStartApplication_OnDataRec_RxData_PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx_DeSignal_Tx_493707fb</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx/DeSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </DATA-SEND-POINTS>
                          <READ-LOCAL-VARIABLES>
                            <VARIABLE-ACCESS UUID="ffb2ec09-5f8a-4940-bab6-4218ecc868cf">
                              <SHORT-NAME>StartApplication_OnDataRec_RxData_IrvOccuranceCounterDid_DIRECT_READ</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <LOCAL-VARIABLE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/IrvOccuranceCounterDid</LOCAL-VARIABLE-REF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </READ-LOCAL-VARIABLES>
                          <SERVER-CALL-POINTS>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="fa31626b-bba9-418b-93ae-834a43716ed5">
                              <SHORT-NAME>OpServerCallStartApplication_OnDataRec_RxData_SetEventStatus_PpDiagnosticMonitor_DEM_EVENT_StartApplication</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpDiagnosticMonitor_DEM_EVENT_StartApplication</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiDiagnosticMonitor_DEM_EVENT_StartApplication/SetEventStatus</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="c9fdd719-bd6a-4bcc-96bd-149494b309ac">
                              <SHORT-NAME>OpServerCallStartApplication_OnDataRec_RxData_WriteBlock_PpPS_StartApplication_NvMBlock1</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpPS_StartApplication_NvMBlock1</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiPS_StartApplication_NvMBlock1/WriteBlock</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                            <SYNCHRONOUS-SERVER-CALL-POINT UUID="9899f6b3-27bb-40b0-abf3-ec5fa8324681">
                              <SHORT-NAME>OpServerCallStartApplication_OnDataRec_RxData_WriteBlock_PpPS_StartApplication_NvMBlock2</SHORT-NAME>
                              <OPERATION-IREF>
                                <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpPS_StartApplication_NvMBlock2</CONTEXT-R-PORT-REF>
                                <TARGET-REQUIRED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/StartApplication/StartApplication_swc/Interfaces/PiPS_StartApplication_NvMBlock2/WriteBlock</TARGET-REQUIRED-OPERATION-REF>
                              </OPERATION-IREF>
                              <TIMEOUT>0</TIMEOUT>
                            </SYNCHRONOUS-SERVER-CALL-POINT>
                          </SERVER-CALL-POINTS>
                          <SYMBOL>StartApplication_OnDataRec_RxData</SYMBOL>
                          <WRITTEN-LOCAL-VARIABLES>
                            <VARIABLE-ACCESS UUID="d43f0ec4-f0c2-43cb-9160-d37275d9f8f7">
                              <SHORT-NAME>StartApplication_OnDataRec_RxData_IrvOccuranceCounterDid_DIRECT_WRITE</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <LOCAL-VARIABLE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/IrvOccuranceCounterDid</LOCAL-VARIABLE-REF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </WRITTEN-LOCAL-VARIABLES>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="9844a42d-b2c5-461b-9622-013d407f867b">
                          <SHORT-NAME>StartApplication_DIAG_DcmReadData</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <READ-LOCAL-VARIABLES>
                            <VARIABLE-ACCESS UUID="20028cd2-15ac-455d-9d4f-1d73714da07b">
                              <SHORT-NAME>StartApplication_DIAG_DcmReadData_IrvOccuranceCounterDid_DIRECT_READ</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <LOCAL-VARIABLE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/IrvOccuranceCounterDid</LOCAL-VARIABLE-REF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </READ-LOCAL-VARIABLES>
                          <SYMBOL>StartApplication_DIAG_DcmReadData</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="b009e963-11e2-4bd7-a43f-dbb6c1bf829d">
                          <SHORT-NAME>StartApplication_DIAG_DcmWriteData</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StartApplication_DIAG_DcmWriteData</SYMBOL>
                          <WRITTEN-LOCAL-VARIABLES>
                            <VARIABLE-ACCESS UUID="c55330e7-c238-440e-98eb-e73a960686ff">
                              <SHORT-NAME>StartApplication_DIAG_DcmWriteData_IrvOccuranceCounterDid_DIRECT_WRITE</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <LOCAL-VARIABLE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/IrvOccuranceCounterDid</LOCAL-VARIABLE-REF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </WRITTEN-LOCAL-VARIABLES>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="7568e9cb-9769-4644-ac61-b7b6ac50322d">
                          <SHORT-NAME>StartApplication_DIAG_ConditionCheckRead</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StartApplication_DIAG_ConditionCheckRead</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="1a2f3ec7-d152-4cf1-ad12-b6bd52f6692a">
                          <SHORT-NAME>StartApplication_DIAG_DemReadData</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <READ-LOCAL-VARIABLES>
                            <VARIABLE-ACCESS UUID="5f79f6a9-51d1-4a9a-b66f-00b1b4090b35">
                              <SHORT-NAME>StartApplication_DIAG_DemReadData_IrvOccuranceCounterDid_DIRECT_READ</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <LOCAL-VARIABLE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior/IrvOccuranceCounterDid</LOCAL-VARIABLE-REF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </READ-LOCAL-VARIABLES>
                          <SYMBOL>StartApplication_DIAG_DemReadData</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="6358c9b3-5341-4a33-ba12-da02339af9e5">
                          <SHORT-NAME>StartApplication_MEM_JobFinished_StartApplication_NvMBlock1</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <DATA-SEND-POINTS>
                            <VARIABLE-ACCESS UUID="c891198d-e7f5-490c-9f9e-acbd6e3a25aa">
                              <SHORT-NAME>DataSendPointStartApplication_MEM_JobFinished_StartApplication_NvMBlock1_PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oC_51e2b8d9</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx/DeSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                            <VARIABLE-ACCESS UUID="ae8945f4-30ab-4bd1-af36-bfa224f653ad">
                              <SHORT-NAME>DataSendPointStartApplication_MEM_JobFinished_StartApplication_NvMBlock1_PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_43a1bbac</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx/DeSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </DATA-SEND-POINTS>
                          <SYMBOL>StartApplication_MEM_JobFinished_StartApplication_NvMBlock1</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="14e95d68-573e-426c-b59b-c04109f9afb2">
                          <SHORT-NAME>StartApplication_MEM_JobFinished_StartApplication_NvMBlock2</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <DATA-SEND-POINTS>
                            <VARIABLE-ACCESS UUID="b94ae609-5aea-4f42-8c85-11575edb888f">
                              <SHORT-NAME>DataSendPointStartApplication_MEM_JobFinished_StartApplication_NvMBlock2_PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oC_53ebae1d</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx/DeSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                            <VARIABLE-ACCESS UUID="ff596fb6-59a6-460a-b6a4-49fe81043481">
                              <SHORT-NAME>DataSendPointStartApplication_MEM_JobFinished_StartApplication_NvMBlock2_PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_6ba1ab99</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/StartApplication/StartApplication_swc/Interfaces/PiSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx/DeSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </DATA-SEND-POINTS>
                          <SYMBOL>StartApplication_MEM_JobFinished_StartApplication_NvMBlock2</SYMBOL>
                        </RUNNABLE-ENTITY>
                      </RUNNABLES>
                      <SUPPORTS-MULTIPLE-INSTANTIATION>false</SUPPORTS-MULTIPLE-INSTANTIATION>
                    </SWC-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </APPLICATION-SW-COMPONENT-TYPE>
                <SWC-IMPLEMENTATION UUID="81dcf726-fe98-46f4-975f-ed37199b3fe0">
                  <SHORT-NAME>StartApplicationImplementation</SHORT-NAME>
                  <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
                  <BEHAVIOR-REF DEST="SWC-INTERNAL-BEHAVIOR">/StartApplication/StartApplication_swc/ComponentTypes/StartApplication/StartApplicationInternalBehavior</BEHAVIOR-REF>
                </SWC-IMPLEMENTATION>
              </ELEMENTS>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
