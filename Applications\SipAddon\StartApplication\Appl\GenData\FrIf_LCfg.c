/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: FrIf
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: FrIf_LCfg.c
 *   Generation Time: 2025-08-05 10:37:18
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/


#define FRIF_LCFG_SOURCE

/* -----------------------------------------------------------------------------
    &&&~ Includes
 ----------------------------------------------------------------------------- */
 
 #include "FrIf_Priv.h"
 
 #include "FrNm_Cbk.h"
 #include "PduR_FrIf.h"
 
 
  

 /* -----------------------------------------------------------------------------
    &&&~ Constants and Variables
 ----------------------------------------------------------------------------- */


#define FRIF_START_SEC_VAR_NOINIT_UNSPECIFIED
/* PRQA S 5087 1 */ /* MD_MSR_MemMap */
#include "MemMap.h"

static VAR(FrIf_NumberOfPduType, FRIF_VAR_NOINIT) FrIf_TxPduWasSentFlags_0[5];
static VAR(uint32, FRIF_VAR_NOINIT) FrIf_FrameBuffer_0[64];
static VAR(uint8, FRIF_VAR_NOINIT) FrIf_TxPduDirtyBits_0[1];
static VAR(uint8, FRIF_VAR_NOINIT) FrIf_TxPduTxRequestCounters_0[1];

#define FRIF_STOP_SEC_VAR_NOINIT_UNSPECIFIED
/* PRQA S 5087 1 */ /* MD_MSR_MemMap */
#include "MemMap.h"

/* -----------------------------------------------------------------------------
    &&&~ TriggerTransmit
 ----------------------------------------------------------------------------- */


#ifndef PDUR_FRIFTRIGGERTRANSMIT
#define PDUR_FRIFTRIGGERTRANSMIT                PduR_FrIfTriggerTransmit
#endif
#ifndef FRNM_TRIGGERTRANSMIT
#define FRNM_TRIGGERTRANSMIT                    FrNm_TriggerTransmit
#endif
#ifndef FRTP_TRIGGERTRANSMIT					 
/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define FRTP_TRIGGERTRANSMIT(PduId, Payload)	E_NOT_OK
#endif
#ifndef FRXCP_TRIGGERTRANSMIT					 
/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define FRXCP_TRIGGERTRANSMIT(PduId, Payload)	E_NOT_OK
#endif
#ifndef FRTSYN_TRIGGERTRANSMIT					 
/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define FRTSYN_TRIGGERTRANSMIT(PduId, Payload)	E_NOT_OK
#endif
#ifndef FRARTP_TRIGGERTRANSMIT					 
/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define FRARTP_TRIGGERTRANSMIT(PduId, Payload)	E_NOT_OK
#endif


/* -----------------------------------------------------------------------------
    &&&~ Rx Indication
 ----------------------------------------------------------------------------- */

#ifndef PDUR_FRIFRXINDICATION
#define PDUR_FRIFRXINDICATION				PduR_FrIfRxIndication
#endif
#ifndef FRNM_RXINDICATION
#define FRNM_RXINDICATION					FrNm_RxIndication
#endif
#ifndef FRTP_RXINDICATION					 
/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define FRTP_RXINDICATION(PduId, Payload)
#endif
#ifndef FRXCP_RXINDICATION					 
/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define FRXCP_RXINDICATION(PduId, Payload)
#endif
#ifndef FRTSYN_RXINDICATION					 
/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define FRTSYN_RXINDICATION(PduId, Payload)
#endif
#ifndef FRARTP_RXINDICATION					 
/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define FRARTP_RXINDICATION(PduId, Payload)
#endif


/* -----------------------------------------------------------------------------
    &&&~ Tx Confirmation
 ----------------------------------------------------------------------------- */

#ifndef PDUR_FRIFTXCONFIRMATION
/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define PDUR_FRIFTXCONFIRMATION(PduId)
#endif
#ifndef FRNM_TXCONFIRMATION
#define FRNM_TXCONFIRMATION                     FrNm_TxConfirmation
#endif
#ifndef FRTP_TXCONFIRMATION
/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define FRTP_TXCONFIRMATION(PduId)
#endif
#ifndef FRXCP_TXCONFIRMATION
/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define FRXCP_TXCONFIRMATION(PduId)
#endif
#ifndef FRARTP_TXCONFIRMATION
/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define FRARTP_TXCONFIRMATION(PduId)
#endif

#define FRIF_START_SEC_CONST_UNSPECIFIED
/* PRQA S 5087 1 */ /* MD_MSR_MemMap */
#include "MemMap.h"


/* -----------------------------------------------------------------------------
    &&&~ FrTransceiver function arrays
 ----------------------------------------------------------------------------- */

#define FRIF_STOP_SEC_CONST_UNSPECIFIED
/* PRQA S 5087 1 */ /* MD_MSR_MemMap */
#include "MemMap.h"

#define FRIF_START_SEC_CODE
/* PRQA S 5087 1 */ /* MD_MSR_MemMap */
#include "MemMap.h"

/**********************************************************************************************************************
  FrIf_CheckChannelStatus()
**********************************************************************************************************************/
/*! \brief       
 *  \details     
 *  \pre         
 *  \context     
 *  \reentrant   
 *  \synchronous 
 *********************************************************************************************************************/

FUNC(void, FRIF_CODE) FrIf_CheckChannelStatus(uint8 ClstIdx, Fr_ChannelType FrChannel, uint16 ChannelStatus)
{
# if (FRIF_PROD_ERROR_DETECT == STD_ON)
  Dem_EventIdType ErrorId_Acs = 0;
  Dem_EventIdType ErrorId_Nit = 0;
  Dem_EventIdType ErrorId_Sw = 0;



  if (1u /* Bit0: (vSS!ValidFrame) */ >= ChannelStatus) /* ESCAN00066846 */
  {
    if (ErrorId_Acs != 0u)
    {
      Dem_ReportErrorStatus(ErrorId_Acs, DEM_EVENT_STATUS_PASSED);
    }
    if (ErrorId_Nit != 0u)
    {
      Dem_ReportErrorStatus(ErrorId_Nit, DEM_EVENT_STATUS_PASSED);
    }
    if (ErrorId_Sw != 0u)
    {
      Dem_ReportErrorStatus(ErrorId_Sw, DEM_EVENT_STATUS_PASSED);
    }
  }
  else
  {
    if (ErrorId_Nit != 0u)
    {
      /* FRIF_E_NIT */
      if (   ((ChannelStatus & FRIF_NIT_SYNTAX_ERROR) == FRIF_NIT_SYNTAX_ERROR )
          || ((ChannelStatus & FRIF_NIT_B_VIOLATION)  == FRIF_NIT_B_VIOLATION  )
         ) 
      {
        /* \trace SPEC-30130, SPEC-30035 */
        Dem_ReportErrorStatus(ErrorId_Nit, DEM_EVENT_STATUS_FAILED);
      }
    }
    
    if (ErrorId_Sw != 0u)
    {
      /* FRIF_E_SW */
      if (   ((ChannelStatus & FRIF_SW_SYNTAX_ERROR) == FRIF_SW_SYNTAX_ERROR)
          || ((ChannelStatus & FRIF_SW_B_VIOLATION)  == FRIF_SW_B_VIOLATION )
          || ((ChannelStatus & FRIF_SW_TX_CONFLICT)  == FRIF_SW_TX_CONFLICT )
         )
      {
        /* \trace SPEC-30084, SPEC-29889 */
        Dem_ReportErrorStatus(ErrorId_Sw, DEM_EVENT_STATUS_FAILED);
      }
    }
    
    if (ErrorId_Acs != 0u)
    {
      /* FRIF_E_ACS */
      if (   ((ChannelStatus & FRIF_ACS_SYNTAX_ERROR) == FRIF_ACS_SYNTAX_ERROR ) 
          || ((ChannelStatus & FRIF_ACS_CONTENT_ERROR)== FRIF_ACS_CONTENT_ERROR) 
          || ((ChannelStatus & FRIF_ACS_B_VIOLATION)  == FRIF_ACS_B_VIOLATION  )  
          || ((ChannelStatus & FRIF_ACS_TX_CONFLICT)  == FRIF_ACS_TX_CONFLICT  )
         ) 
      {
        /* \trace SPEC-29861, SPEC-29933 */
        Dem_ReportErrorStatus(ErrorId_Acs, DEM_EVENT_STATUS_FAILED);
      }
    }
  }
#else
  FRIF_DUMMY_STATEMENT(ClstIdx); /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */
  FRIF_DUMMY_STATEMENT(FrChannel); /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */
  FRIF_DUMMY_STATEMENT(ChannelStatus); /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */
#endif  
} /* FrIf_CheckChannelStatus() */ /* PRQA S 6010, 6030 */ /* MD_MSR_STPTH, MD_MSR_STCYC */

/* -----------------------------------------------------------------------------
    &&&~ upperlayer wrapper functions
 ----------------------------------------------------------------------------- */

FUNC(Std_ReturnType,FRIF_CODE) FrIf_TriggerTransmitFunctions(uint8 PduOwner, PduIdType FrTxPduId, P2VAR(uint8, AUTOMATIC, FRIF_VAR_NOINIT) Payload, P2VAR(PduLengthType, AUTOMATIC, FRIF_VAR_NOINIT) Length)
{
    Std_ReturnType Result;
    PduInfoType PduInfo;
    PduInfo.SduDataPtr = (uint8*) Payload;
    PduInfo.SduLength = *Length;

    switch(PduOwner)
    {
        case 0: Result = PDUR_FRIFTRIGGERTRANSMIT(FrTxPduId, &PduInfo); break;         /* SBSW_FRIF_FCT_CALL_PTR2PDUINFO */
        case 1: Result = FRNM_TRIGGERTRANSMIT(FrTxPduId, &PduInfo); break;             /* SBSW_FRIF_FCT_CALL_PTR2PDUINFO */
        case 2: Result = FRTP_TRIGGERTRANSMIT(FrTxPduId, &PduInfo); break;             /* SBSW_FRIF_FCT_CALL_PTR2PDUINFO */
        case 3: Result = FRXCP_TRIGGERTRANSMIT(FrTxPduId, &PduInfo); break;            /* SBSW_FRIF_FCT_CALL_PTR2PDUINFO */
        case 4: Result = E_NOT_OK; break; /* Pdu with PduOwner 'None' -> return E_NOT_OK to prevent Pdu transmission (do nothing) */
        case 5: Result = FRTSYN_TRIGGERTRANSMIT(FrTxPduId, &PduInfo); break;           /* SBSW_FRIF_FCT_CALL_PTR2PDUINFO */
        case 6: Result = FRARTP_TRIGGERTRANSMIT(FrTxPduId, &PduInfo); break;           /* SBSW_FRIF_FCT_CALL_PTR2PDUINFO */
        default: (void) Det_ReportError(FRIF_MODULE_ID,0, FRIF_SID_TRIGGER_TRANSMIT_FCTS, FRIF_E_INVALID_PDU_OWNER); Result = E_NOT_OK; break;
    }
    if (Result == E_OK)
    {
        if (PduInfo.SduLength > *Length)
        {
            Result = E_NOT_OK; /* UL returned invalid SduLength - this case should be never entered! (note: this check is only performed if DET is enabled) */
        }
    }
    *Length = PduInfo.SduLength;                                                        /* SBSW_FRIF_PTR_WRT_UNCHANGED */
    return Result;
} /* FrIf_TriggerTransmitFunctions() */ /* PRQA S 6030 */ /* MD_MSR_STCYC */

FUNC(void,FRIF_CODE) FrIf_TxConfirmationFunctions(uint8 PduOwner, PduIdType FrTxPduId)
{
    switch(PduOwner)
    {
        case 0: PDUR_FRIFTXCONFIRMATION(FrTxPduId); break;
        case 1: FRNM_TXCONFIRMATION(FrTxPduId); break;
        case 2: FRTP_TXCONFIRMATION(FrTxPduId); break;
        case 3: FRXCP_TXCONFIRMATION(FrTxPduId); break;
        case 4: break; /* Pdu with PduOwner 'None' -> do nothing */
        case 5: break; /* Pdu with PduOwner 'FrTSyn' -> do nothing */
        case 6: FRARTP_TXCONFIRMATION(FrTxPduId); break;
        default: (void) Det_ReportError(FRIF_MODULE_ID,0, FRIF_SID_TX_CONFIRMATION_FCTS, FRIF_E_INVALID_PDU_OWNER); break; /* PRQA S 2016 */ /* MD_MSR_EmptyClause */
    }
} /* FrIf_TxConfirmationFunctions() */ /* PRQA S 2006 */ /* MD_MSR_14.7 */

FUNC(void,FRIF_CODE) FrIf_RxIndicationFunctions(uint8 PduOwner, PduIdType FrRxPduId, P2VAR(uint8, AUTOMATIC, FRIF_VAR_NOINIT) Payload, PduLengthType Length)
{
    PduInfoType PduInfo;
    PduInfo.SduDataPtr = (uint8*) Payload;
     PduInfo.SduLength = Length;

    switch(PduOwner)
    {
        case 0: PDUR_FRIFRXINDICATION(FrRxPduId, &PduInfo); break;                     /* SBSW_FRIF_FCT_CALL_PTR2PDUINFO */
        case 1: FRNM_RXINDICATION(FrRxPduId, &PduInfo); break;                         /* SBSW_FRIF_FCT_CALL_PTR2PDUINFO */
        case 2: FRTP_RXINDICATION(FrRxPduId, &PduInfo); break;                         /* SBSW_FRIF_FCT_CALL_PTR2PDUINFO */
        case 3: FRXCP_RXINDICATION(FrRxPduId, &PduInfo); break;                        /* SBSW_FRIF_FCT_CALL_PTR2PDUINFO */
        case 4: break; /* Pdu with PduOwner 'None' -> do nothing */
        case 5: FRTSYN_RXINDICATION(FrRxPduId, &PduInfo); break;                       /* SBSW_FRIF_FCT_CALL_PTR2PDUINFO */
        case 6: FRARTP_RXINDICATION(FrRxPduId, &PduInfo); break;                       /* SBSW_FRIF_FCT_CALL_PTR2PDUINFO */
        default: (void) Det_ReportError(FRIF_MODULE_ID,0, FRIF_SID_RX_INDICATION_FCTS, FRIF_E_INVALID_PDU_OWNER); break; /* PRQA S 2016 */ /* MD_MSR_EmptyClause */
    }
} /* FrIf_RxIndicationFunctions() */ /* PRQA S 2006 */ /* MD_MSR_14.7 */

#define FRIF_STOP_SEC_CODE
/* PRQA S 5087 1 */ /* MD_MSR_MemMap */
#include "MemMap.h"

#define FRIF_START_SEC_CONST_UNSPECIFIED
/* PRQA S 5087 1 */ /* MD_MSR_MemMap */
#include "MemMap.h"


/* -----------------------------------------------------------------------------
    &&&~ Tx PDU Translation Table
 ----------------------------------------------------------------------------- */

CONST(FrIf_TxPduTranslationTupleType, FRIF_CONST) FrIf_TxPduTranslationTable[] =
{
  {0u, 0u} /*  pdu_TxDyn_64_0bd27c77_Tx, TX INVALID HANDLE  */ , 
  {1u, 0u} /*  pdu_TxDyn_16_44389cbf_Tx, TX INVALID HANDLE  */ , 
  {2u, 0u} /*  PDU_nm_MyECU_Fr_50a02afb_Tx, TX INVALID HANDLE  */ , 
  {3u, 0u} /*  pdu_TxStat_40_505d7b88_Tx, TX INVALID HANDLE  */ , 
  {4u, 0u} /*  pdu_TxStat_64_2f41aa7f_Tx, TX INVALID HANDLE  */ , 
  {5u, 0u} /*  PDU_Fr_StartAppl_MyECU_TX_161591f9_Tx, TX INVALID HANDLE  */ , 
  {6u, 0u} /*  PDU_Transmit_MyECU_dcbaa590_Tx, TX INVALID HANDLE  */ 
};

/* -----------------------------------------------------------------------------
    &&&~ Tx Pdu Descriptors
 ----------------------------------------------------------------------------- */
 
static CONST(FrIf_TxPduDescriptorType, FRIF_CONST) FrIf_TxPduDescriptors_0[] = 
{
  {0u, PduRConf_PduRDestPdu_pdu_TxDyn_64_0bd27c77_Tx, 8u, 0x00u} /*  pdu_TxDyn_64_0bd27c77_Tx, Index: 0, FrIfTxPduId: 0  */ , 
  {0u, PduRConf_PduRDestPdu_pdu_TxDyn_16_44389cbf_Tx, 2u, 0x00u} /*  pdu_TxDyn_16_44389cbf_Tx, Index: 1, FrIfTxPduId: 1  */ , 
  {0u, FrNmConf_FrNmTxPdu_PDU_nm_MyECU_Fr_50a02afb_Tx, 8u, 0x11u} /*  PDU_nm_MyECU_Fr_50a02afb_Tx, Index: 2, FrIfTxPduId: 2  */ , 
  {0u, PduRConf_PduRDestPdu_pdu_TxStat_40_505d7b88_Tx, 5u, 0x00u} /*  pdu_TxStat_40_505d7b88_Tx, Index: 3, FrIfTxPduId: 3  */ , 
  {0u, PduRConf_PduRDestPdu_pdu_TxStat_64_2f41aa7f_Tx, 8u, 0x00u} /*  pdu_TxStat_64_2f41aa7f_Tx, Index: 4, FrIfTxPduId: 4  */ , 
  {0u, PduRConf_PduRDestPdu_PDU_Fr_StartAppl_MyECU_TX_161591f9_Tx, 16u, 0x00u} /*  PDU_Fr_StartAppl_MyECU_TX_161591f9_Tx, Index: 5, FrIfTxPduId: 5  */ , 
  {0u, PduRConf_PduRDestPdu_PDU_Transmit_MyECU_dcbaa590_Tx, 8u, 0x00u} /*  PDU_Transmit_MyECU_dcbaa590_Tx, Index: 6, FrIfTxPduId: 6  */ 
};

/* -----------------------------------------------------------------------------
    &&&~ Tx Frame Layout Elements
 ----------------------------------------------------------------------------- */

static CONST(FrIf_FrameLayoutElementType, FRIF_CONST) FrIf_TxFrameLayoutElements_0[] = 
{
  {0u, 0u, 0u, 0x00u} /*  pdu_TxDyn_64_e9108ac9_Tx  */ , 
  {1u, 8u, 0u, 0x00u} /*  pdu_TxDyn_16_991d51a2_Tx  */ , 
  {2u, 0u, 0u, 0x00u} /*  PDU_nm_MyECU_Fr_0e5662f0_Tx  */ , 
  {3u, 0u, 5u, 0x01u} /*  pdu_TxStat_40_fe95993a_Tx  */ , 
  {4u, 6u, 5u, 0x02u} /*  pdu_TxStat_64_254afde6_Tx  */ , 
  {5u, 0u, 0u, 0x00u} /*  PDU_Fr_StartAppl_MyECU_TX_5a88efbc_Tx  */ , 
  {6u, 0u, 0u, 0x00u} /*  PDU_Transmit_MyECU_d0c303d0_Tx  */ 
};


/* -----------------------------------------------------------------------------
    &&&~ Tx Frame Descriptors
 ----------------------------------------------------------------------------- */
static CONST(FrIf_FrameDescriptorType, FRIF_CONST) FrIf_TxFrameDescriptors_0[] = 
{
  {0u, 2u, 10u, 0x00u} /*  FT_005F0108_af5cc8ad_Tx  */ , 
  {2u, 1u, 8u, 0x01u} /*  FT_005A0040_75dc2c79_Tx  */ , 
  {3u, 2u, 16u, 0x40u} /*  FT_00100104_bb944e7e_Tx  */ , 
  {5u, 1u, 16u, 0x00u} /*  FT_00080001_1f1414c5_Tx  */ , 
  {6u, 1u, 16u, 0x00u} /*  FT_00020001_65829600_Tx  */ 
};

/* -----------------------------------------------------------------------------
    &&&~ Rx Pdu Descriptors
 ----------------------------------------------------------------------------- */

static CONST(FrIf_RxPduDescriptorType, FRIF_CONST) FrIf_RxPduDescriptors_0[] = 
{
  {PduRConf_PduRSrcPdu_PduRSrcPdu_45b853db, 8u, 0x00u} /*  pdu_RxDyn_64_600910d1_Rx  */ , 
  {FrNmConf_FrNmRxPdu_PDU_nm_OtherECU_Fr_30557908_Rx, 8u, 0x01u} /*  PDU_nm_OtherECU_Fr_30557908_Rx  */ , 
  {FrNmConf_FrNmRxPdu_PDU_nm_RearECU_Fr_2fa98195_Rx, 8u, 0x01u} /*  PDU_nm_RearECU_Fr_2fa98195_Rx  */ , 
  {PduRConf_PduRSrcPdu_PduRSrcPdu_2bf83137, 4u, 0x00u} /*  pdu_RxStat_10_51092b83_Rx  */ , 
  {PduRConf_PduRSrcPdu_PduRSrcPdu_9474dc2d, 7u, 0x00u} /*  pdu_RxStat_30_25bd2d72_Rx  */ , 
  {PduRConf_PduRSrcPdu_PduRSrcPdu_b2f650ce, 16u, 0x00u} /*  PDU_Fr_StartAppl_BothECU_RX_7a67d8c4_Rx  */ , 
  {PduRConf_PduRSrcPdu_PduRSrcPdu_5699e50b, 16u, 0x00u} /*  PDU_Dummy_RearECU_9177c4f3_Rx  */ 
};


/* -----------------------------------------------------------------------------
    &&&~ Rx Frame Layout Elements
 ----------------------------------------------------------------------------- */

static CONST(FrIf_FrameLayoutElementType, FRIF_CONST) FrIf_RxFrameLayoutElements_0[] = 
{
  {0u, 0u, 0u, 0x00u} /*  pdu_RxDyn_64_0c6377bf_Rx  */ , 
  {1u, 0u, 0u, 0x00u} /*  PDU_nm_OtherECU_Fr_52301af6_Rx  */ , 
  {2u, 0u, 0u, 0x00u} /*  PDU_nm_RearECU_Fr_211522c4_Rx  */ , 
  {3u, 0u, 4u, 0x01u} /*  pdu_RxStat_10_70f03a48_Rx  */ , 
  {4u, 5u, 4u, 0x02u} /*  pdu_RxStat_30_7bae1d4b_Rx  */ , 
  {5u, 0u, 0u, 0x00u} /*  PDU_Fr_StartAppl_BothECU_RX_74f96013_Rx  */ , 
  {6u, 0u, 0u, 0x00u} /*  PDU_Dummy_RearECU_b25fe6e9_Rx  */ 
};


/* -----------------------------------------------------------------------------
    &&&~ Rx Frame Descriptors
 ----------------------------------------------------------------------------- */

static CONST(FrIf_FrameDescriptorType, FRIF_CONST) FrIf_RxFrameDescriptors_0[] = 
{
  {0u, 1u, 8u, 0x00u} /*  FT_00670304_8aa49a29_Rx  */ , 
  {1u, 1u, 8u, 0x00u} /*  FT_005A0140_8f53e10d_Rx  */ , 
  {2u, 1u, 8u, 0x00u} /*  FT_005A0240_5bb2b0d0_Rx  */ , 
  {3u, 2u, 12u, 0x40u} /*  FT_000F0002_986124ab_Rx  */ , 
  {5u, 1u, 16u, 0x00u} /*  FT_000A0001_0e7f58b5_Rx  */ , 
  {6u, 1u, 16u, 0x00u} /*  FT_00030001_e9c801d5_Rx  */ 
};




/* -----------------------------------------------------------------------------
    &&&~ Task Schedule Data
 ----------------------------------------------------------------------------- */

static CONST(FrIf_JobDescriptorType, FRIF_CONST) FrIf_JobDescriptors_0[] = 
{
  {
    625u /*  StartTimeInMacroTicks  */ , 
    2550u /*  StartTimeProtectedRange  */ , 
    3046u /*  EndTimeProtectedRange  */ , 
    1250u /*  MaxDelay  */ , 
    0u /*  MinColumnNumber  */ , 
    0u /*  MaxColumnNumber  */ , 
    255u /*  MinTxConfJobNo  */ , 
    254u /*  MaxTxConfJobNo  */ , 
    0u /*  JobConfig  */ 
  } /*  FrIf_TxJob_02  */ , 
  {
    2534u /*  StartTimeInMacroTicks  */ , 
    34u /*  StartTimeProtectedRange  */ , 
    2516u /*  EndTimeProtectedRange  */ , 
    1875u /*  MaxDelay  */ , 
    0u /*  MinColumnNumber  */ , 
    2u /*  MaxColumnNumber  */ , 
    2u /*  MinTxConfJobNo  */ , 
    2u /*  MaxTxConfJobNo  */ , 
    4u /*  JobConfig  */ 
  } /*  FrIf_RxJob_01  */ , 
  {
    3125u /*  StartTimeInMacroTicks  */ , 
    34u /*  StartTimeProtectedRange  */ , 
    2516u /*  EndTimeProtectedRange  */ , 
    1250u /*  MaxDelay  */ , 
    1u /*  MinColumnNumber  */ , 
    3u /*  MaxColumnNumber  */ , 
    255u /*  MinTxConfJobNo  */ , 
    254u /*  MaxTxConfJobNo  */ , 
    0u /*  JobConfig  */ 
  } /*  FrIf_TxJob_01  */ , 
  {
    4929u /*  StartTimeInMacroTicks  */ , 
    2550u /*  StartTimeProtectedRange  */ , 
    3046u /*  EndTimeProtectedRange  */ , 
    1875u /*  MaxDelay  */ , 
    3u /*  MinColumnNumber  */ , 
    3u /*  MaxColumnNumber  */ , 
    0u /*  MinTxConfJobNo  */ , 
    0u /*  MaxTxConfJobNo  */ , 
    12u /*  JobConfig  */ 
  } /*  FrIf_RxJob_02  */ 
};


/* -----------------------------------------------------------------------------
    &&&~ Rx Handle tables
 ----------------------------------------------------------------------------- */

/* PRQA S 3408 1 */ /* MD_FrIf_3408 */
CONST(FrIf_RxHandleType, FRIF_CONST) RxHandles_0_0[] = 
{
  3u /*  FT_000F0002_986124ab_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ 
} /*  FrIf_RxJob_01  */ ;
/* PRQA S 3408 1 */ /* MD_FrIf_3408 */
CONST(FrIf_RxHandleType, FRIF_CONST) RxHandles_0_1[] = 
{
  4u /*  FT_000A0001_0e7f58b5_Rx  */ 
} /*  FrIf_RxJob_01  */ ;
/* PRQA S 3408 1 */ /* MD_FrIf_3408 */
CONST(FrIf_RxHandleType, FRIF_CONST) RxHandles_0_2[] = 
{
  5u /*  FT_00030001_e9c801d5_Rx  */ 
} /*  FrIf_RxJob_01  */ ;
/* PRQA S 3408 1 */ /* MD_FrIf_3408 */
CONST(FrIf_RxHandleType, FRIF_CONST) RxHandles_0_3[] = 
{
  6u /*  RX INVALID HANDLE  */ , 
  1u /*  FT_005A0140_8f53e10d_Rx  */ , 
  2u /*  FT_005A0240_5bb2b0d0_Rx  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  6u /*  RX INVALID HANDLE  */ , 
  0u /*  FT_00670304_8aa49a29_Rx  */ 
} /*  FrIf_RxJob_02  */ ;


/* PRQA S 3408 1 */ /* MD_FrIf_3408 */
CONSTP2CONST(FrIf_RxHandleType, FRIF_CONST, FRIF_CONST) FrIf_RxHandles_0[] = 
{
  RxHandles_0_0
  ,RxHandles_0_1
  ,RxHandles_0_2
  ,RxHandles_0_3
};


/* PRQA S 3408 1 */ /* MD_FrIf_3408 */
CONST(FrIf_CycleMaskType, FRIF_CONST) FrIf_RxCycleMasks_0[] = 
{
  1U
  , 0U
  , 0U
  , 63U
};


/* -----------------------------------------------------------------------------
    &&&~ Tx Handle tables
 ----------------------------------------------------------------------------- */

/* PRQA S 3408 1 */ /* MD_FrIf_3408 */
CONST(FrIf_TxHandleType, FRIF_CONST) TxHandles_0_0[] = 
{
  1u /*  FT_005A0040_75dc2c79_Tx  */ , 
  0u /*  FT_005F0108_af5cc8ad_Tx  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  0u /*  FT_005F0108_af5cc8ad_Tx  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  0u /*  FT_005F0108_af5cc8ad_Tx  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  0u /*  FT_005F0108_af5cc8ad_Tx  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  0u /*  FT_005F0108_af5cc8ad_Tx  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  0u /*  FT_005F0108_af5cc8ad_Tx  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  0u /*  FT_005F0108_af5cc8ad_Tx  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  0u /*  FT_005F0108_af5cc8ad_Tx  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ 
} /*  FrIf_TxJob_02  */ ;
/* PRQA S 3408 1 */ /* MD_FrIf_3408 */
CONST(FrIf_TxHandleType, FRIF_CONST) TxHandles_0_1[] = 
{
  2u /*  FT_00100104_bb944e7e_Tx  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ , 
  5u /*  TX INVALID HANDLE  */ 
} /*  FrIf_TxJob_01  */ ;
/* PRQA S 3408 1 */ /* MD_FrIf_3408 */
CONST(FrIf_TxHandleType, FRIF_CONST) TxHandles_0_2[] = 
{
  3u /*  FT_00080001_1f1414c5_Tx  */ 
} /*  FrIf_TxJob_01  */ ;
/* PRQA S 3408 1 */ /* MD_FrIf_3408 */
CONST(FrIf_TxHandleType, FRIF_CONST) TxHandles_0_3[] = 
{
  4u /*  FT_00020001_65829600_Tx  */ 
} /*  FrIf_TxJob_01  */ ;


/* PRQA S 3408 1 */ /* MD_FrIf_3408 */
CONSTP2CONST(FrIf_TxHandleType, FRIF_CONST, FRIF_CONST) FrIf_TxHandles_0[] = 
{ 
  TxHandles_0_0
  ,TxHandles_0_1
  ,TxHandles_0_2
  ,TxHandles_0_3
};


/* PRQA S 3408 1 */ /* MD_FrIf_3408 */
CONST(FrIf_CycleMaskType, FRIF_CONST) FrIf_TxCycleMasks_0[] = 
{
  63U
  , 3U
  , 0U
  , 0U
};


#define FRIF_STOP_SEC_CONST_UNSPECIFIED
/* PRQA S 5087 1 */ /* MD_MSR_MemMap */
#include "MemMap.h"


#define FRIF_START_SEC_CONST_UNSPECIFIED
/* PRQA S 5087 1 */ /* MD_MSR_MemMap */
#include "MemMap.h"


/* -----------------------------------------------------------------------------
    &&&~ Constants
 ----------------------------------------------------------------------------- */

CONST(FrIf_ConfigType, FRIF_CONST) FrIf_Config = 0u;


CONSTP2CONST(FrIf_TxPduDescriptorType, FRIF_CONST, FRIF_CONST) FrIf_Ctrl2TxPduDescriptors[] =
{
  FrIf_TxPduDescriptors_0 /*  MyECU_6c1a0d52  */ 
};


CONSTP2CONST(FrIf_RxPduDescriptorType, FRIF_CONST, FRIF_CONST) FrIf_Ctrl2RxPduDescriptors[] =
{
  FrIf_RxPduDescriptors_0 /*  MyECU_6c1a0d52  */ 
};


CONSTP2CONST(FrIf_FrameLayoutElementType, FRIF_CONST, FRIF_CONST) FrIf_Ctrl2TxFrameLayoutElements[] =
{
  FrIf_TxFrameLayoutElements_0 /*  MyECU_6c1a0d52  */ 
};


CONSTP2CONST(FrIf_FrameLayoutElementType, FRIF_CONST, FRIF_CONST) FrIf_Ctrl2RxFrameLayoutElements[] =
{
  FrIf_RxFrameLayoutElements_0 /*  MyECU_6c1a0d52  */ 
};


CONSTP2CONST(FrIf_FrameDescriptorType, FRIF_CONST, FRIF_CONST) FrIf_Ctrl2TxFrameDescriptors[] =
{
  FrIf_TxFrameDescriptors_0 /*  MyECU_6c1a0d52  */ 
};


CONSTP2CONST(FrIf_FrameDescriptorType, FRIF_CONST, FRIF_CONST) FrIf_Ctrl2RxFrameDescriptors[] =
{
  FrIf_RxFrameDescriptors_0 /*  MyECU_6c1a0d52  */ 
};


CONST(PduIdType, FRIF_CONST) FrIf_Ctrl2MaxTxPduId[] = 
{
  6u /*  MyECU_6c1a0d52  */ 
};


CONST(FrIf_TxHandleType, FRIF_CONST) FrIf_Ctrl2MaxTxFrameId[] = 
{
  4u /*  MyECU_6c1a0d52  */ 
};


CONST(FrIf_TxHandleType, FRIF_CONST) FrIf_Ctrl2InvalidTxHandle[] = 
{
  5u /*  MyECU_6c1a0d52  */ 
};


CONST(FrIf_RxHandleType, FRIF_CONST) FrIf_Ctrl2InvalidRxHandle[] = 
{
  6u /*  MyECU_6c1a0d52  */ 
};


CONSTP2VAR(FrIf_NumberOfPduType, FRIF_CONST, FRIF_VAR_NOINIT) FrIf_Ctrl2TxPduWasSentFlags[] = 
{
  FrIf_TxPduWasSentFlags_0 /*  MyECU_6c1a0d52  */ 
};


CONST(PduIdType, FRIF_CONST) FrIf_Ctrl2NumberOfTxRequestCounters[] = 
{
  0u /*  MyECU_6c1a0d52  */ 
};


CONSTP2VAR(uint32, FRIF_CONST, FRIF_VAR_NOINIT) FrIf_Ctrl2FrameBuffer[] =
{
  FrIf_FrameBuffer_0 /*  MyECU_6c1a0d52  */ 
};




CONSTP2VAR(uint8, FRIF_CONST, FRIF_VAR_NOINIT) FrIf_Ctrl2TxPduDirtyBits[] =
{
  FrIf_TxPduDirtyBits_0 /*  MyECU_6c1a0d52  */ 
};

CONSTP2VAR(uint8, FRIF_CONST, FRIF_VAR_NOINIT) FrIf_Ctrl2TxPduTxRequestCounters[] =
{
  FrIf_TxPduTxRequestCounters_0 /*  MyECU_6c1a0d52  */ 
};


CONSTP2CONST(FrIf_JobDescriptorType, FRIF_CONST, FRIF_CONST) FrIf_Clst2JobDescriptors[] =
{
  FrIf_JobDescriptors_0 /*  FlexRay_e64a27e1  */ 
};


CONSTP2CONST(FrIf_TxHandlePtrType, FRIF_CONST, FRIF_CONST) FrIf_Clst2TxComHandleTable[] =
{
  FrIf_TxHandles_0 /*  FlexRay_e64a27e1  */ 
};


CONSTP2CONST(FrIf_RxHandlePtrType, FRIF_CONST, FRIF_CONST) FrIf_Clst2RxComHandleTable[] =
{
  FrIf_RxHandles_0 /*  FlexRay_e64a27e1  */ 
};


CONSTP2CONST(FrIf_CycleMaskType, FRIF_CONST, FRIF_CONST) FrIf_Clst2TxCycleMasks[] =
{
  FrIf_TxCycleMasks_0 /*  FlexRay_e64a27e1  */ 
};


CONSTP2CONST(FrIf_CycleMaskType, FRIF_CONST, FRIF_CONST) FrIf_Clst2RxCycleMasks[] =
{
  FrIf_RxCycleMasks_0 /*  FlexRay_e64a27e1  */ 
};


CONST(uint16, FRIF_CONST) FrIf_Clst2MacroTicksPerCycle[] = 
{
  5000u /*  FlexRay_e64a27e1  */ 
};


CONST(uint16, FRIF_CONST) FrIf_Clst2MacroTickLengthInNanoSeconds[] = 
{
  1000u /*  FlexRay_e64a27e1  */ 
};


CONST(uint8, FRIF_CONST) FrIf_Clst2NumberOfJobs[] = 
{
  4u /*  FlexRay_e64a27e1  */ 
};



#define FRIF_STOP_SEC_CONST_UNSPECIFIED
/* PRQA S 5087 1 */ /* MD_MSR_MemMap */
#include "MemMap.h"

