<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<testmodule starttime="2020-09-01 10:02:31" timestamp="  21.464461" verdicts="2_basic">
  <testgroup>
    <testgroup>
      <testcase starttime="2020-09-01 10:02:31" timestamp="  21.464461">
        <preparation starttime="2020-09-01 10:02:31" timestamp="  21.464461">
          <testpattern timestamp="  21.464461" name="set" type="testprimitive">
            <title>Clear error log</title>
            <details timestamp="  21.464461">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ErrorLog&apos;</name>
                <description></description>
              </info>
            </details>
            <teststep timestamp="  21.464461" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  21.464461" result="pass"/>
          </testpattern>
          <testpattern timestamp="  21.464461" name="statechange" type="testpattern">
            <title>Activate COM Tx Only Use Case</title>
            <teststep timestamp="  21.464461" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::UseCaseActivator&apos;: 3 (Com_TxOnly)</teststep>
            <teststep timestamp="  21.464461" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  22.664461" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1200ms (max=1200ms)</teststep>
            <teststep timestamp="  22.664461" level="2" type="auto" ident="2" result="pass">Waited for 1200 ms.</teststep>
            <teststep timestamp="  22.664461" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::UseCaseActivator&apos; value passed: = 3 (Com_TxOnly), condition: == 3 (Com_TxOnly)</teststep>
            <teststep timestamp="  22.664461" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  22.664461" result="pass"/>
          </testpattern>
          <testpattern timestamp="  22.664461" name="awaitvaluematch" type="testprimitive">
            <title>Verify that the current CANoe version is suitable for the Start Application</title>
            <teststep timestamp="  22.664461" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  22.664461" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
            <teststep timestamp="  22.664461" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::CANoeVersionInfo&apos;: = 100000, condition: &gt;= 80200</teststep>
            <result timestamp="  22.664461" result="pass"/>
          </testpattern>
          <testpattern timestamp="  22.664461" name="set" type="testprimitive">
            <title>Initialize variables</title>
            <details timestamp="  22.664461">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ComSignalPairSelector&apos;</name>
                <description>0 (Vita_CAN0_to_Vita_CAN0) </description>
              </info>
            </details>
            <details timestamp="  22.664461">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ComSendCtrl&apos;</name>
                <description>2</description>
              </info>
            </details>
            <teststep timestamp="  22.664461" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  22.664461" result="pass"/>
          </testpattern>
          <testpattern timestamp="  22.664461" name="set" type="testprimitive">
            <title>Initialize ComInput</title>
            <details timestamp="  22.664461">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ComInput&apos;</name>
                <description>0</description>
              </info>
            </details>
            <teststep timestamp="  22.664461" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  22.664461" result="pass"/>
          </testpattern>
          <testpattern timestamp="  22.664461" name="awaitvaluematch" type="testprimitive">
            <title>Wait for rx data processing on ECU side.</title>
            <teststep timestamp="  22.782472" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComActualOutput&apos; Elapsed time=118.012ms (max=10000ms)</teststep>
            <teststep timestamp="  22.782472" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 3 value condition(s).</teststep>
            <teststep timestamp="  22.782472" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
            <teststep timestamp="  22.782472" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComActualOutput&apos;: = 0, condition: == 0</teststep>
            <teststep timestamp="  22.782472" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComExpectedOutput&apos;: = 0, condition: == 0</teststep>
            <result timestamp="  22.782472" result="pass"/>
          </testpattern>
          <prepend endtime="2020-09-01 10:02:33" endtimestamp="  22.782472" />
        </preparation>
        <testpattern timestamp="  22.782472" name="set" type="testprimitive">
          <title>Set sensor value to 254, this will be scaled to the maximum value of the Rx signal</title>
          <details timestamp="  22.782472">
            <title>Set specified value.</title>
            <info>
              <name>System variable &apos;StartApplication::ComInput&apos;</name>
              <description>254</description>
            </info>
          </details>
          <teststep timestamp="  22.782472" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <result timestamp="  22.782472" result="pass"/>
        </testpattern>
        <testpattern timestamp="  22.782472" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.</title>
          <teststep timestamp="  22.783972" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=1.50001ms (max=10000ms)</teststep>
          <teststep timestamp="  22.783972" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  22.783972" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = -254, condition: != 0</teststep>
          <result timestamp="  22.783972" result="pass"/>
        </testpattern>
        <testpattern timestamp="  22.783972" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal</title>
          <teststep timestamp="  23.032974" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=249.002ms (max=10000ms)</teststep>
          <teststep timestamp="  23.032974" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  23.032974" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
          <result timestamp="  23.032974" result="pass"/>
        </testpattern>
        <testpattern timestamp="  23.032974" name="set" type="testprimitive">
          <title>Set sensor value to 128, this will be scaled to the median value of the Rx signal</title>
          <details timestamp="  23.032974">
            <title>Set specified value.</title>
            <info>
              <name>System variable &apos;StartApplication::ComInput&apos;</name>
              <description>128</description>
            </info>
          </details>
          <teststep timestamp="  23.032974" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <result timestamp="  23.032974" result="pass"/>
        </testpattern>
        <testpattern timestamp="  23.032974" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.</title>
          <teststep timestamp="  23.033974" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=1.00001ms (max=10000ms)</teststep>
          <teststep timestamp="  23.033974" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  23.033974" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 126, condition: != 0</teststep>
          <result timestamp="  23.033974" result="pass"/>
        </testpattern>
        <testpattern timestamp="  23.033974" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal</title>
          <teststep timestamp="  23.282476" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=248.502ms (max=10000ms)</teststep>
          <teststep timestamp="  23.282476" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  23.282476" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
          <result timestamp="  23.282476" result="pass"/>
        </testpattern>
        <testpattern timestamp="  23.282476" name="set" type="testprimitive">
          <title>Set sensor value to 0, this will be scaled to the minimum value of the Rx signal</title>
          <details timestamp="  23.282476">
            <title>Set specified value.</title>
            <info>
              <name>System variable &apos;StartApplication::ComInput&apos;</name>
              <description>0</description>
            </info>
          </details>
          <teststep timestamp="  23.282476" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <result timestamp="  23.282476" result="pass"/>
        </testpattern>
        <testpattern timestamp="  23.282476" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.</title>
          <teststep timestamp="  23.283976" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=1.50001ms (max=10000ms)</teststep>
          <teststep timestamp="  23.283976" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  23.283976" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 128, condition: != 0</teststep>
          <result timestamp="  23.283976" result="pass"/>
        </testpattern>
        <testpattern timestamp="  23.283976" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal</title>
          <teststep timestamp="  23.532978" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=249.002ms (max=10000ms)</teststep>
          <teststep timestamp="  23.532978" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  23.532978" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
          <result timestamp="  23.532978" result="pass"/>
        </testpattern>
        <completion starttime="2020-09-01 10:02:33" timestamp="  23.532978">
          <testpattern timestamp="  23.532978" name="set" type="testprimitive">
            <title>Switch back to Com Rx/Tx use case</title>
            <details timestamp="  23.532978">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::UseCaseActivator&apos;</name>
                <description>0 (Com_RxTx) </description>
              </info>
            </details>
            <teststep timestamp="  23.532978" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  23.532978" result="pass"/>
          </testpattern>
          <comment timestamp="  23.532978">
            <text>Value of system variable &apos;StartApplication::ErrorLog&apos;  is </text>
          </comment>
          <compend endtime="2020-09-01 10:02:33" endtimestamp="  23.532978" />
        </completion>
        <verdict time="2020-09-01 10:02:33" timestamp="  23.532978" endtime="2020-09-01 10:02:33" endtimestamp="  23.532978" result="pass" />
        <title>CAN-FD Data Transmission ( Vita_CAN0 )</title>
        <ident>TCASE-394602</ident>
        <description>Verify that the application can transmit data on CAN_FD using signals or signal groups.\n                        Ecu Rx group signal: &apos;SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx&apos;,\n                        Ecu Tx signal: &apos;Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx&apos;.</description>
      </testcase>
      <testcase starttime="2020-09-01 10:02:33" timestamp="  23.532978">
        <preparation starttime="2020-09-01 10:02:33" timestamp="  23.532978">
          <testpattern timestamp="  23.532978" name="set" type="testprimitive">
            <title>Clear error log</title>
            <details timestamp="  23.532978">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ErrorLog&apos;</name>
                <description></description>
              </info>
            </details>
            <teststep timestamp="  23.532978" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  23.532978" result="pass"/>
          </testpattern>
          <testpattern timestamp="  23.532978" name="statechange" type="testpattern">
            <title>Activate COM Use Case</title>
            <teststep timestamp="  23.532978" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::UseCaseActivator&apos;: 0 (Com_RxTx)</teststep>
            <teststep timestamp="  23.532978" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  24.732978" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1200ms (max=1200ms)</teststep>
            <teststep timestamp="  24.732978" level="2" type="auto" ident="2" result="pass">Waited for 1200 ms.</teststep>
            <teststep timestamp="  24.732978" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::UseCaseActivator&apos; value passed: = 0 (Com_RxTx), condition: == 0 (Com_RxTx)</teststep>
            <teststep timestamp="  24.732978" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  24.732978" result="pass"/>
          </testpattern>
          <testpattern timestamp="  24.732978" name="awaitvaluematch" type="testprimitive">
            <title>Verify that the current CANoe version is suitable for the Start Application</title>
            <teststep timestamp="  24.732978" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  24.732978" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
            <teststep timestamp="  24.732978" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::CANoeVersionInfo&apos;: = 100000, condition: &gt;= 80200</teststep>
            <result timestamp="  24.732978" result="pass"/>
          </testpattern>
          <testpattern timestamp="  24.732978" name="set" type="testprimitive">
            <title>Initialize variables</title>
            <details timestamp="  24.732978">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ComSignalPairSelector&apos;</name>
                <description>0 (Vita_CAN0_to_Vita_CAN0) </description>
              </info>
            </details>
            <details timestamp="  24.732978">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ComSendCtrl&apos;</name>
                <description>2</description>
              </info>
            </details>
            <teststep timestamp="  24.732978" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  24.732978" result="pass"/>
          </testpattern>
          <testpattern timestamp="  24.732978" name="set" type="testprimitive">
            <title>Initialize ComInput</title>
            <details timestamp="  24.732978">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ComInput&apos;</name>
                <description>0</description>
              </info>
            </details>
            <teststep timestamp="  24.732978" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  24.732978" result="pass"/>
          </testpattern>
          <testpattern timestamp="  24.732978" name="awaitvaluematch" type="testprimitive">
            <title>Wait for rx data processing on ECU side.</title>
            <teststep timestamp="  24.732978" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  24.732978" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 3 value condition(s).</teststep>
            <teststep timestamp="  24.732978" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
            <teststep timestamp="  24.732978" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComActualOutput&apos;: = 0, condition: == 0</teststep>
            <teststep timestamp="  24.732978" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComExpectedOutput&apos;: = 0, condition: == 0</teststep>
            <result timestamp="  24.732978" result="pass"/>
          </testpattern>
          <prepend endtime="2020-09-01 10:02:35" endtimestamp="  24.732978" />
        </preparation>
        <testpattern timestamp="  24.732978" name="set" type="testprimitive">
          <title>Set sensor value to 254, this will be scaled to the maximum value of the Rx signal</title>
          <details timestamp="  24.732978">
            <title>Set specified value.</title>
            <info>
              <name>System variable &apos;StartApplication::ComInput&apos;</name>
              <description>254</description>
            </info>
          </details>
          <teststep timestamp="  24.732978" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <result timestamp="  24.732978" result="pass"/>
        </testpattern>
        <testpattern timestamp="  24.732978" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.</title>
          <teststep timestamp="  24.735485" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=2.50707ms (max=10000ms)</teststep>
          <teststep timestamp="  24.735485" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  24.735485" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = -254, condition: != 0</teststep>
          <result timestamp="  24.735485" result="pass"/>
        </testpattern>
        <testpattern timestamp="  24.735485" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal</title>
          <teststep timestamp="  24.782986" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=47.5002ms (max=10000ms)</teststep>
          <teststep timestamp="  24.782986" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  24.782986" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
          <result timestamp="  24.782986" result="pass"/>
        </testpattern>
        <testpattern timestamp="  24.782986" name="set" type="testprimitive">
          <title>Set sensor value to 128, this will be scaled to the median value of the Rx signal</title>
          <details timestamp="  24.782986">
            <title>Set specified value.</title>
            <info>
              <name>System variable &apos;StartApplication::ComInput&apos;</name>
              <description>128</description>
            </info>
          </details>
          <teststep timestamp="  24.782986" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <result timestamp="  24.782986" result="pass"/>
        </testpattern>
        <testpattern timestamp="  24.782986" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.</title>
          <teststep timestamp="  24.784506" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=1.52062ms (max=10000ms)</teststep>
          <teststep timestamp="  24.784506" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  24.784506" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 126, condition: != 0</teststep>
          <result timestamp="  24.784506" result="pass"/>
        </testpattern>
        <testpattern timestamp="  24.784506" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal</title>
          <teststep timestamp="  25.032987" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=248.48ms (max=10000ms)</teststep>
          <teststep timestamp="  25.032987" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  25.032987" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
          <result timestamp="  25.032987" result="pass"/>
        </testpattern>
        <testpattern timestamp="  25.032987" name="set" type="testprimitive">
          <title>Set sensor value to 0, this will be scaled to the minimum value of the Rx signal</title>
          <details timestamp="  25.032987">
            <title>Set specified value.</title>
            <info>
              <name>System variable &apos;StartApplication::ComInput&apos;</name>
              <description>0</description>
            </info>
          </details>
          <teststep timestamp="  25.032987" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <result timestamp="  25.032987" result="pass"/>
        </testpattern>
        <testpattern timestamp="  25.032987" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.</title>
          <teststep timestamp="  25.034987" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=2.00001ms (max=10000ms)</teststep>
          <teststep timestamp="  25.034987" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  25.034987" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 128, condition: != 0</teststep>
          <result timestamp="  25.034987" result="pass"/>
        </testpattern>
        <testpattern timestamp="  25.034987" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal</title>
          <teststep timestamp="  25.282988" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=248.002ms (max=10000ms)</teststep>
          <teststep timestamp="  25.282988" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  25.282988" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
          <result timestamp="  25.282988" result="pass"/>
        </testpattern>
        <completion starttime="2020-09-01 10:02:35" timestamp="  25.282988">
          <comment timestamp="  25.282988">
            <text>Value of system variable &apos;StartApplication::ErrorLog&apos;  is </text>
          </comment>
          <compend endtime="2020-09-01 10:02:35" endtimestamp="  25.282988" />
        </completion>
        <verdict time="2020-09-01 10:02:35" timestamp="  25.282988" endtime="2020-09-01 10:02:35" endtimestamp="  25.282988" result="pass" />
        <title>CAN Data Reception ( Vita_CAN0 )</title>
        <ident>TCASE-375706</ident>
        <description>Verify that the application can receive data on CAN using signals or signal groups.\n                        Ecu Rx group signal: &apos;SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx&apos;,\n                        Ecu Tx signal: &apos;Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx&apos;.</description>
      </testcase>
      <title>CAN Data Transmission and Reception</title>
    </testgroup>
    <testgroup>
      <skipped starttime="2020-09-01 10:02:35" timestamp="  25.282988">
        <title>LIN Data Transmission ( Vita_LIN0 )</title>
        <ident>TCASE-375709</ident>
        <description>Verify that the application can transmit data on LIN using signals.\n                        Ecu Rx group signal: &apos;SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx&apos;,\n                        Ecu Tx signal: &apos;Sig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx&apos;.</description>
      </skipped>
      <skipped starttime="2020-09-01 10:02:35" timestamp="  25.282988">
        <title>LIN Data Reception ( Vita_LIN0 )</title>
        <ident>TCASE-375710</ident>
        <description>Verify that the application can receive data on LIN using signals.\n                        Ecu Rx signal: &apos;Sig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx&apos;,\n                        Ecu Tx signal: &apos;Sig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx&apos;.</description>
      </skipped>
      <title>LIN Data Transmission and Reception</title>
    </testgroup>
    <testgroup>
      <testcase starttime="2020-09-01 10:02:35" timestamp="  25.282988">
        <preparation starttime="2020-09-01 10:02:35" timestamp="  25.282988">
          <testpattern timestamp="  25.282988" name="set" type="testprimitive">
            <title>Clear error log</title>
            <details timestamp="  25.282988">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ErrorLog&apos;</name>
                <description></description>
              </info>
            </details>
            <teststep timestamp="  25.282988" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  25.282988" result="pass"/>
          </testpattern>
          <testpattern timestamp="  25.282988" name="statechange" type="testpattern">
            <title>Activate COM Tx Only Use Case</title>
            <teststep timestamp="  25.282988" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::UseCaseActivator&apos;: 3 (Com_TxOnly)</teststep>
            <teststep timestamp="  25.282988" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  26.482988" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1200ms (max=1200ms)</teststep>
            <teststep timestamp="  26.482988" level="2" type="auto" ident="2" result="pass">Waited for 1200 ms.</teststep>
            <teststep timestamp="  26.482988" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::UseCaseActivator&apos; value passed: = 3 (Com_TxOnly), condition: == 3 (Com_TxOnly)</teststep>
            <teststep timestamp="  26.482988" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  26.482988" result="pass"/>
          </testpattern>
          <testpattern timestamp="  26.482988" name="awaitvaluematch" type="testprimitive">
            <title>Verify that the current CANoe version is suitable for the Start Application</title>
            <teststep timestamp="  26.482988" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  26.482988" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
            <teststep timestamp="  26.482988" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::CANoeVersionInfo&apos;: = 100000, condition: &gt;= 80200</teststep>
            <result timestamp="  26.482988" result="pass"/>
          </testpattern>
          <testpattern timestamp="  26.482988" name="set" type="testprimitive">
            <title>Initialize variables</title>
            <details timestamp="  26.482988">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ComSignalPairSelector&apos;</name>
                <description>2 (Vita_FR0_to_Vita_FR0) </description>
              </info>
            </details>
            <details timestamp="  26.482988">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ComSendCtrl&apos;</name>
                <description>2</description>
              </info>
            </details>
            <teststep timestamp="  26.482988" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  26.482988" result="pass"/>
          </testpattern>
          <testpattern timestamp="  26.482988" name="set" type="testprimitive">
            <title>Initialize ComInput</title>
            <details timestamp="  26.482988">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ComInput&apos;</name>
                <description>0</description>
              </info>
            </details>
            <teststep timestamp="  26.482988" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  26.482988" result="pass"/>
          </testpattern>
          <testpattern timestamp="  26.482988" name="awaitvaluematch" type="testprimitive">
            <title>Wait for rx data processing on ECU side.</title>
            <teststep timestamp="  26.482988" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  26.482988" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 3 value condition(s).</teststep>
            <teststep timestamp="  26.482988" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
            <teststep timestamp="  26.482988" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComActualOutput&apos;: = 0, condition: == 0</teststep>
            <teststep timestamp="  26.482988" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComExpectedOutput&apos;: = 0, condition: == 0</teststep>
            <result timestamp="  26.482988" result="pass"/>
          </testpattern>
          <prepend endtime="2020-09-01 10:02:36" endtimestamp="  26.482988" />
        </preparation>
        <testpattern timestamp="  26.482988" name="set" type="testprimitive">
          <title>Set sensor value to 254, this will be scaled to the maximum value of the Rx signal</title>
          <details timestamp="  26.482988">
            <title>Set specified value.</title>
            <info>
              <name>System variable &apos;StartApplication::ComInput&apos;</name>
              <description>254</description>
            </info>
          </details>
          <teststep timestamp="  26.482988" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <result timestamp="  26.482988" result="pass"/>
        </testpattern>
        <testpattern timestamp="  26.482988" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.</title>
          <teststep timestamp="  26.484997" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=2.00835ms (max=10000ms)</teststep>
          <teststep timestamp="  26.484997" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  26.484997" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = -254, condition: != 0</teststep>
          <result timestamp="  26.484997" result="pass"/>
        </testpattern>
        <testpattern timestamp="  26.484997" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal</title>
          <teststep timestamp="  26.549260" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=64.2629ms (max=10000ms)</teststep>
          <teststep timestamp="  26.549260" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  26.549260" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
          <result timestamp="  26.549260" result="pass"/>
        </testpattern>
        <testpattern timestamp="  26.549260" name="set" type="testprimitive">
          <title>Set sensor value to 128, this will be scaled to the median value of the Rx signal</title>
          <details timestamp="  26.549260">
            <title>Set specified value.</title>
            <info>
              <name>System variable &apos;StartApplication::ComInput&apos;</name>
              <description>128</description>
            </info>
          </details>
          <teststep timestamp="  26.549260" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <result timestamp="  26.549260" result="pass"/>
        </testpattern>
        <testpattern timestamp="  26.549260" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.</title>
          <teststep timestamp="  26.550997" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=1.73755ms (max=10000ms)</teststep>
          <teststep timestamp="  26.550997" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  26.550997" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 126, condition: != 0</teststep>
          <result timestamp="  26.550997" result="pass"/>
        </testpattern>
        <testpattern timestamp="  26.550997" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal</title>
          <teststep timestamp="  26.849264" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=298.267ms (max=10000ms)</teststep>
          <teststep timestamp="  26.849264" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  26.849264" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
          <result timestamp="  26.849264" result="pass"/>
        </testpattern>
        <testpattern timestamp="  26.849264" name="set" type="testprimitive">
          <title>Set sensor value to 0, this will be scaled to the minimum value of the Rx signal</title>
          <details timestamp="  26.849264">
            <title>Set specified value.</title>
            <info>
              <name>System variable &apos;StartApplication::ComInput&apos;</name>
              <description>0</description>
            </info>
          </details>
          <teststep timestamp="  26.849264" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <result timestamp="  26.849264" result="pass"/>
        </testpattern>
        <testpattern timestamp="  26.849264" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.</title>
          <teststep timestamp="  26.850499" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=1.23524ms (max=10000ms)</teststep>
          <teststep timestamp="  26.850499" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  26.850499" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 128, condition: != 0</teststep>
          <result timestamp="  26.850499" result="pass"/>
        </testpattern>
        <testpattern timestamp="  26.850499" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal</title>
          <teststep timestamp="  27.049267" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=198.768ms (max=10000ms)</teststep>
          <teststep timestamp="  27.049267" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  27.049267" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
          <result timestamp="  27.049267" result="pass"/>
        </testpattern>
        <completion starttime="2020-09-01 10:02:37" timestamp="  27.049267">
          <testpattern timestamp="  27.049267" name="set" type="testprimitive">
            <title>Switch back to Com Rx/Tx use case</title>
            <details timestamp="  27.049267">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::UseCaseActivator&apos;</name>
                <description>0 (Com_RxTx) </description>
              </info>
            </details>
            <teststep timestamp="  27.049267" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  27.049267" result="pass"/>
          </testpattern>
          <comment timestamp="  27.049267">
            <text>Value of system variable &apos;StartApplication::ErrorLog&apos;  is </text>
          </comment>
          <compend endtime="2020-09-01 10:02:37" endtimestamp="  27.049267" />
        </completion>
        <verdict time="2020-09-01 10:02:37" timestamp="  27.049267" endtime="2020-09-01 10:02:37" endtimestamp="  27.049267" result="pass" />
        <title>FlexRay Data Transmission ( Vita_FR0 )</title>
        <ident>TCASE-375707</ident>
        <description>Verify that the application can transmit data on FlexRay using signals or signal groups.\n                        Ecu Rx group signal: &apos;SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx&apos;,\n                        Ecu Tx signal: &apos;Signal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx&apos;.</description>
      </testcase>
      <testcase starttime="2020-09-01 10:02:37" timestamp="  27.049267">
        <preparation starttime="2020-09-01 10:02:37" timestamp="  27.049267">
          <testpattern timestamp="  27.049267" name="set" type="testprimitive">
            <title>Clear error log</title>
            <details timestamp="  27.049267">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ErrorLog&apos;</name>
                <description></description>
              </info>
            </details>
            <teststep timestamp="  27.049267" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  27.049267" result="pass"/>
          </testpattern>
          <testpattern timestamp="  27.049267" name="statechange" type="testpattern">
            <title>Activate COM Use Case</title>
            <teststep timestamp="  27.049267" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::UseCaseActivator&apos;: 0 (Com_RxTx)</teststep>
            <teststep timestamp="  27.049267" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  28.249267" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1200ms (max=1200ms)</teststep>
            <teststep timestamp="  28.249267" level="2" type="auto" ident="2" result="pass">Waited for 1200 ms.</teststep>
            <teststep timestamp="  28.249267" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::UseCaseActivator&apos; value passed: = 0 (Com_RxTx), condition: == 0 (Com_RxTx)</teststep>
            <teststep timestamp="  28.249267" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  28.249267" result="pass"/>
          </testpattern>
          <testpattern timestamp="  28.249267" name="awaitvaluematch" type="testprimitive">
            <title>Verify that the current CANoe version is suitable for the Start Application</title>
            <teststep timestamp="  28.249267" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  28.249267" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
            <teststep timestamp="  28.249267" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::CANoeVersionInfo&apos;: = 100000, condition: &gt;= 80200</teststep>
            <result timestamp="  28.249267" result="pass"/>
          </testpattern>
          <testpattern timestamp="  28.249267" name="set" type="testprimitive">
            <title>Initialize variables</title>
            <details timestamp="  28.249267">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ComSignalPairSelector&apos;</name>
                <description>2 (Vita_FR0_to_Vita_FR0) </description>
              </info>
            </details>
            <details timestamp="  28.249267">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ComSendCtrl&apos;</name>
                <description>2</description>
              </info>
            </details>
            <teststep timestamp="  28.249267" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  28.249267" result="pass"/>
          </testpattern>
          <testpattern timestamp="  28.249267" name="set" type="testprimitive">
            <title>Initialize ComInput</title>
            <details timestamp="  28.249267">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ComInput&apos;</name>
                <description>0</description>
              </info>
            </details>
            <teststep timestamp="  28.249267" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  28.249267" result="pass"/>
          </testpattern>
          <testpattern timestamp="  28.249267" name="awaitvaluematch" type="testprimitive">
            <title>Wait for rx data processing on ECU side.</title>
            <teststep timestamp="  28.249267" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  28.249267" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 3 value condition(s).</teststep>
            <teststep timestamp="  28.249267" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
            <teststep timestamp="  28.249267" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComActualOutput&apos;: = 0, condition: == 0</teststep>
            <teststep timestamp="  28.249267" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComExpectedOutput&apos;: = 0, condition: == 0</teststep>
            <result timestamp="  28.249267" result="pass"/>
          </testpattern>
          <prepend endtime="2020-09-01 10:02:38" endtimestamp="  28.249267" />
        </preparation>
        <testpattern timestamp="  28.249267" name="set" type="testprimitive">
          <title>Set sensor value to 254, this will be scaled to the maximum value of the Rx signal</title>
          <details timestamp="  28.249267">
            <title>Set specified value.</title>
            <info>
              <name>System variable &apos;StartApplication::ComInput&apos;</name>
              <description>254</description>
            </info>
          </details>
          <teststep timestamp="  28.249267" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <result timestamp="  28.249267" result="pass"/>
        </testpattern>
        <testpattern timestamp="  28.249267" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.</title>
          <teststep timestamp="  28.254554" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=5.28676ms (max=10000ms)</teststep>
          <teststep timestamp="  28.254554" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  28.254554" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = -254, condition: != 0</teststep>
          <result timestamp="  28.254554" result="pass"/>
        </testpattern>
        <testpattern timestamp="  28.254554" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal</title>
          <teststep timestamp="  28.349283" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=94.7297ms (max=10000ms)</teststep>
          <teststep timestamp="  28.349283" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  28.349283" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
          <result timestamp="  28.349283" result="pass"/>
        </testpattern>
        <testpattern timestamp="  28.349283" name="set" type="testprimitive">
          <title>Set sensor value to 128, this will be scaled to the median value of the Rx signal</title>
          <details timestamp="  28.349283">
            <title>Set specified value.</title>
            <info>
              <name>System variable &apos;StartApplication::ComInput&apos;</name>
              <description>128</description>
            </info>
          </details>
          <teststep timestamp="  28.349283" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <result timestamp="  28.349283" result="pass"/>
        </testpattern>
        <testpattern timestamp="  28.349283" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.</title>
          <teststep timestamp="  28.354555" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=5.27189ms (max=10000ms)</teststep>
          <teststep timestamp="  28.354555" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  28.354555" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 126, condition: != 0</teststep>
          <result timestamp="  28.354555" result="pass"/>
        </testpattern>
        <testpattern timestamp="  28.354555" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal</title>
          <teststep timestamp="  28.549287" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=194.731ms (max=10000ms)</teststep>
          <teststep timestamp="  28.549287" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  28.549287" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
          <result timestamp="  28.549287" result="pass"/>
        </testpattern>
        <testpattern timestamp="  28.549287" name="set" type="testprimitive">
          <title>Set sensor value to 0, this will be scaled to the minimum value of the Rx signal</title>
          <details timestamp="  28.549287">
            <title>Set specified value.</title>
            <info>
              <name>System variable &apos;StartApplication::ComInput&apos;</name>
              <description>0</description>
            </info>
          </details>
          <teststep timestamp="  28.549287" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <result timestamp="  28.549287" result="pass"/>
        </testpattern>
        <testpattern timestamp="  28.549287" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.</title>
          <teststep timestamp="  28.554559" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=5.27197ms (max=10000ms)</teststep>
          <teststep timestamp="  28.554559" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  28.554559" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 128, condition: != 0</teststep>
          <result timestamp="  28.554559" result="pass"/>
        </testpattern>
        <testpattern timestamp="  28.554559" name="awaitvaluematch" type="testprimitive">
          <title>Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal</title>
          <teststep timestamp="  28.849292" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;ComCmpOutputs&apos; Elapsed time=294.733ms (max=10000ms)</teststep>
          <teststep timestamp="  28.849292" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  28.849292" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::ComCmpOutputs&apos;: = 0, condition: == 0</teststep>
          <result timestamp="  28.849292" result="pass"/>
        </testpattern>
        <completion starttime="2020-09-01 10:02:39" timestamp="  28.849292">
          <comment timestamp="  28.849292">
            <text>Value of system variable &apos;StartApplication::ErrorLog&apos;  is </text>
          </comment>
          <compend endtime="2020-09-01 10:02:39" endtimestamp="  28.849292" />
        </completion>
        <verdict time="2020-09-01 10:02:39" timestamp="  28.849292" endtime="2020-09-01 10:02:39" endtimestamp="  28.849292" result="pass" />
        <title>FlexRay Data Reception ( Vita_FR0 )</title>
        <ident>TCASE-375708</ident>
        <description>Verify that the application can receive data on FlexRay using signals or signal groups.\n                        Ecu Rx signal: &apos;StartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx&apos;,\n                        Ecu Tx signal: &apos;Signal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx&apos;.</description>
      </testcase>
      <title>FlexRay Data Transmission and Reception</title>
    </testgroup>
    <title>Usecase COM</title>
  </testgroup>
  <testgroup>
    <testgroup>
      <testcase starttime="2020-09-01 10:02:39" timestamp="  28.849292">
        <preparation starttime="2020-09-01 10:02:39" timestamp="  28.849292">
          <testpattern timestamp="  28.849292" name="set" type="testprimitive">
            <title>Clear error log</title>
            <details timestamp="  28.849292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ErrorLog&apos;</name>
                <description></description>
              </info>
            </details>
            <teststep timestamp="  28.849292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  28.849292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  28.849292" name="statechange" type="testpattern">
            <title>Activate DIAG Use Case</title>
            <teststep timestamp="  28.849292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::UseCaseActivator&apos;: 2 (Diag)</teststep>
            <teststep timestamp="  28.849292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  30.049292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1200ms (max=1200ms)</teststep>
            <teststep timestamp="  30.049292" level="2" type="auto" ident="2" result="pass">Waited for 1200 ms.</teststep>
            <teststep timestamp="  30.049292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::UseCaseActivator&apos; value passed: = 2 (Diag), condition: == 2 (Diag)</teststep>
            <teststep timestamp="  30.049292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  30.049292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  30.049292" name="awaitvaluematch" type="testprimitive">
            <title>Verify that the current CANoe version is suitable for the Start Application</title>
            <teststep timestamp="  30.049292" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  30.049292" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
            <teststep timestamp="  30.049292" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::CANoeVersionInfo&apos;: = 100000, condition: &gt;= 80200</teststep>
            <result timestamp="  30.049292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  30.049292" name="set" type="testprimitive">
            <title>Initialize variables</title>
            <details timestamp="  30.049292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagChannel&apos;</name>
                <description>0 (Vita_CAN0) </description>
              </info>
            </details>
            <details timestamp="  30.049292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagActivateDefaultSession&apos;</name>
                <description>0</description>
              </info>
            </details>
            <teststep timestamp="  30.049292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  30.049292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  30.049292" name="wait" type="controlfunction">
            <title>Wait to allow System Variables to change</title>
            <teststep timestamp="  30.059292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=10ms (max=10ms)</teststep>
            <teststep timestamp="  30.059292" level="1" type="auto" ident="1" result="pass">Waited for 10 ms.</teststep>
            <result timestamp="  30.059292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  30.059292" name="awaitvaluematch" type="testprimitive">
            <title>Wait until there is no diagnostic request active and no further requests are queued.</title>
            <teststep timestamp="  30.059292" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  30.059292" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
            <teststep timestamp="  30.059292" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::DiagRequestState&apos;: = 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE), condition: == 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE)</teststep>
            <result timestamp="  30.059292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  30.059292" name="statechange" type="testpattern">
            <title>Reset response code</title>
            <teststep timestamp="  30.059292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagResponseCode&apos;: 0 (0x00: No Response received)</teststep>
            <teststep timestamp="  30.059292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  30.069292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=10ms (max=10ms)</teststep>
            <teststep timestamp="  30.069292" level="2" type="auto" ident="2" result="pass">Waited for 10 ms.</teststep>
            <teststep timestamp="  30.069292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 0 (0x00: No Response received), condition: == 0 (0x00: No Response received)</teststep>
            <teststep timestamp="  30.069292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  30.069292" result="pass"/>
          </testpattern>
          <prepend endtime="2020-09-01 10:02:40" endtimestamp="  30.069292" />
        </preparation>
        <testpattern timestamp="  30.069292" name="statechange" type="testpattern">
          <title>Try to activate default session via DSC</title>
          <teststep timestamp="  30.069292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagActivateDefaultSession&apos;: 1</teststep>
          <teststep timestamp="  30.069292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  31.069292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
          <teststep timestamp="  31.069292" level="2" type="auto" ident="2" result="pass">Waited for 1000 ms.</teststep>
          <teststep timestamp="  31.069292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagActivateDefaultSession&apos; value passed: = 1, condition: == 1</teststep>
          <teststep timestamp="  31.069292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <result timestamp="  31.069292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  31.069292" name="awaitvaluematch" type="testprimitive">
          <title>Verify that a response can be received: either positive response (0x50) or negative response (0x7F)</title>
          <teststep timestamp="  31.069292" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
          <teststep timestamp="  31.069292" level="2" type="auto" ident="1" result="pass">Waited for occurrence of any of 2 value condition(s).</teststep>
          <teststep timestamp="  31.069292" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::DiagResponseCode&apos;: = 80 (0x50: Positive Response DSC), condition: == 80 (0x50: Positive Response DSC)</teststep>
          <teststep timestamp="  31.069292" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::DiagResponseCode&apos;: = 80 (0x50: Positive Response DSC), condition: == 127 (0x7F: Negative Response)</teststep>
          <result timestamp="  31.069292" result="pass"/>
        </testpattern>
        <completion starttime="2020-09-01 10:02:41" timestamp="  31.069292">
          <comment timestamp="  31.069292">
            <text>Value of system variable &apos;StartApplication::ErrorLog&apos;  is </text>
          </comment>
          <compend endtime="2020-09-01 10:02:41" endtimestamp="  31.069292" />
        </completion>
        <verdict time="2020-09-01 10:02:41" timestamp="  31.069292" endtime="2020-09-01 10:02:41" endtimestamp="  31.069292" result="pass" />
        <title>Basic Request/Response via UDS over CAN ( Vita_CAN0 )</title>
        <ident>TCASE-478915</ident>
        <description>Verify that a DCM request can be sent and a response can be received via UDS over CAN</description>
      </testcase>
      <testcase starttime="2020-09-01 10:02:41" timestamp="  31.069292">
        <preparation starttime="2020-09-01 10:02:41" timestamp="  31.069292">
          <testpattern timestamp="  31.069292" name="set" type="testprimitive">
            <title>Clear error log</title>
            <details timestamp="  31.069292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ErrorLog&apos;</name>
                <description></description>
              </info>
            </details>
            <teststep timestamp="  31.069292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  31.069292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  31.069292" name="statechange" type="testpattern">
            <title>Activate DIAG Use Case</title>
            <teststep timestamp="  31.069292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::UseCaseActivator&apos;: 2 (Diag)</teststep>
            <teststep timestamp="  31.069292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  32.269292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1200ms (max=1200ms)</teststep>
            <teststep timestamp="  32.269292" level="2" type="auto" ident="2" result="pass">Waited for 1200 ms.</teststep>
            <teststep timestamp="  32.269292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::UseCaseActivator&apos; value passed: = 2 (Diag), condition: == 2 (Diag)</teststep>
            <teststep timestamp="  32.269292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  32.269292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  32.269292" name="awaitvaluematch" type="testprimitive">
            <title>Verify that the current CANoe version is suitable for the Start Application</title>
            <teststep timestamp="  32.269292" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  32.269292" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
            <teststep timestamp="  32.269292" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::CANoeVersionInfo&apos;: = 100000, condition: &gt;= 80200</teststep>
            <result timestamp="  32.269292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  32.269292" name="set" type="testprimitive">
            <title>Initialize variables</title>
            <details timestamp="  32.269292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagChannel&apos;</name>
                <description>0 (Vita_CAN0) </description>
              </info>
            </details>
            <details timestamp="  32.269292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagSetEventStatusPassed&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  32.269292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagGetCounter&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  32.269292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagResetCounterWithRxData&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  32.269292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagCounterValue&apos;</name>
                <description>0</description>
              </info>
            </details>
            <teststep timestamp="  32.269292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  32.269292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  32.269292" name="wait" type="controlfunction">
            <title>Wait to allow System Variables to change</title>
            <teststep timestamp="  32.279292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=10ms (max=10ms)</teststep>
            <teststep timestamp="  32.279292" level="1" type="auto" ident="1" result="pass">Waited for 10 ms.</teststep>
            <result timestamp="  32.279292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  32.279292" name="awaitvaluematch" type="testprimitive">
            <title>Wait until there is no diagnostic request active and no further requests are queued.</title>
            <teststep timestamp="  32.279292" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  32.279292" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
            <teststep timestamp="  32.279292" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::DiagRequestState&apos;: = 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE), condition: == 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE)</teststep>
            <result timestamp="  32.279292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  32.279292" name="statechange" type="testpattern">
            <title>Reset response code</title>
            <teststep timestamp="  32.279292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagResponseCode&apos;: 0 (0x00: No Response received)</teststep>
            <teststep timestamp="  32.279292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  32.289292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=10ms (max=10ms)</teststep>
            <teststep timestamp="  32.289292" level="2" type="auto" ident="2" result="pass">Waited for 10 ms.</teststep>
            <teststep timestamp="  32.289292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 0 (0x00: No Response received), condition: == 0 (0x00: No Response received)</teststep>
            <teststep timestamp="  32.289292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  32.289292" result="pass"/>
          </testpattern>
          <prepend endtime="2020-09-01 10:02:42" endtimestamp="  32.289292" />
        </preparation>
        <testpattern timestamp="  32.289292" name="statechange" type="testpattern">
          <title>Initialize: Reset DID value to 0 via RxData</title>
          <teststep timestamp="  32.289292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagResetCounterWithRxData&apos;: 1</teststep>
          <teststep timestamp="  32.289292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  32.389292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  32.389292" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
          <teststep timestamp="  32.389292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResetCounterWithRxData&apos; value passed: = 1, condition: == 1</teststep>
          <teststep timestamp="  32.389292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  32.389292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagResetCounterWithRxData&apos;: 0</teststep>
          <teststep timestamp="  32.389292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  32.489292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  32.489292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  32.489292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  32.489292" name="wait" type="controlfunction">
          <title>Wait to allow for transmission of control signals</title>
          <teststep timestamp="  33.689292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1200ms (max=1200ms)</teststep>
          <teststep timestamp="  33.689292" level="1" type="auto" ident="1" result="pass">Waited for 1200 ms.</teststep>
          <result timestamp="  33.689292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  33.689292" name="statechange" type="testpattern">
          <title>Get DID value via RDBI and verify that it was reset to 0x0000</title>
          <teststep timestamp="  33.689292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetCounter&apos;: 1</teststep>
          <teststep timestamp="  33.689292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  34.689292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
          <teststep timestamp="  34.689292" level="2" type="auto" ident="2" result="pass">Waited for 1000 ms.</teststep>
          <teststep timestamp="  34.689292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagCounterValue&apos; value passed: = 0, condition: == 0</teststep>
          <teststep timestamp="  34.689292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 98 (0x62: Positive Response RDBI), condition: == 98 (0x62: Positive Response RDBI)</teststep>
          <teststep timestamp="  34.689292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  34.689292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetCounter&apos;: 0</teststep>
          <teststep timestamp="  34.689292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  34.789292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  34.789292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  34.789292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  34.789292" name="statechange" type="testpattern">
          <title>Set event status to passed to increment the DID value by 1</title>
          <teststep timestamp="  34.789292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagSetEventStatusPassed&apos;: 1</teststep>
          <teststep timestamp="  34.789292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  34.889292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  34.889292" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
          <teststep timestamp="  34.889292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagSetEventStatusPassed&apos; value passed: = 1, condition: == 1</teststep>
          <teststep timestamp="  34.889292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  34.889292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagSetEventStatusPassed&apos;: 0</teststep>
          <teststep timestamp="  34.889292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  34.989292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  34.989292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  34.989292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  34.989292" name="statechange" type="testpattern">
          <title>Get DID value via RDBI and verify that it was set to 0x0001</title>
          <teststep timestamp="  34.989292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetCounter&apos;: 1</teststep>
          <teststep timestamp="  34.989292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  35.989292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
          <teststep timestamp="  35.989292" level="2" type="auto" ident="2" result="pass">Waited for 1000 ms.</teststep>
          <teststep timestamp="  35.989292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagCounterValue&apos; value passed: = 1, condition: == 1</teststep>
          <teststep timestamp="  35.989292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 98 (0x62: Positive Response RDBI), condition: == 98 (0x62: Positive Response RDBI)</teststep>
          <teststep timestamp="  35.989292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  35.989292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetCounter&apos;: 0</teststep>
          <teststep timestamp="  35.989292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  36.089292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  36.089292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  36.089292" result="pass"/>
        </testpattern>
        <completion starttime="2020-09-01 10:02:46" timestamp="  36.089292">
          <comment timestamp="  36.089292">
            <text>Value of system variable &apos;StartApplication::ErrorLog&apos;  is </text>
          </comment>
          <compend endtime="2020-09-01 10:02:46" endtimestamp="  36.089292" />
        </completion>
        <verdict time="2020-09-01 10:02:46" timestamp="  36.089292" endtime="2020-09-01 10:02:46" endtimestamp="  36.089292" result="pass" />
        <title>Read Diagnostic Data via UDS over CAN ( Vita_CAN0 )</title>
        <ident>TCASE-481982</ident>
        <description>Verify that a diagnostic data record (DID) can be read via UDS over CAN</description>
      </testcase>
      <testcase starttime="2020-09-01 10:02:46" timestamp="  36.089292">
        <preparation starttime="2020-09-01 10:02:46" timestamp="  36.089292">
          <testpattern timestamp="  36.089292" name="set" type="testprimitive">
            <title>Clear error log</title>
            <details timestamp="  36.089292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ErrorLog&apos;</name>
                <description></description>
              </info>
            </details>
            <teststep timestamp="  36.089292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  36.089292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  36.089292" name="statechange" type="testpattern">
            <title>Activate DIAG Use Case</title>
            <teststep timestamp="  36.089292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::UseCaseActivator&apos;: 2 (Diag)</teststep>
            <teststep timestamp="  36.089292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  37.289292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1200ms (max=1200ms)</teststep>
            <teststep timestamp="  37.289292" level="2" type="auto" ident="2" result="pass">Waited for 1200 ms.</teststep>
            <teststep timestamp="  37.289292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::UseCaseActivator&apos; value passed: = 2 (Diag), condition: == 2 (Diag)</teststep>
            <teststep timestamp="  37.289292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  37.289292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  37.289292" name="awaitvaluematch" type="testprimitive">
            <title>Verify that the current CANoe version is suitable for the Start Application</title>
            <teststep timestamp="  37.289292" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  37.289292" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
            <teststep timestamp="  37.289292" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::CANoeVersionInfo&apos;: = 100000, condition: &gt;= 80200</teststep>
            <result timestamp="  37.289292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  37.289292" name="set" type="testprimitive">
            <title>Initialize variables</title>
            <details timestamp="  37.289292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagRequestState&apos;</name>
                <description>0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE) </description>
              </info>
            </details>
            <details timestamp="  37.289292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagChannel&apos;</name>
                <description>0 (Vita_CAN0) </description>
              </info>
            </details>
            <details timestamp="  37.289292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagSetCounter&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  37.289292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagCounterValueFromTxData&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  37.289292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagResetCounterWithRxData&apos;</name>
                <description>0</description>
              </info>
            </details>
            <teststep timestamp="  37.289292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  37.289292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  37.289292" name="wait" type="controlfunction">
            <title>Wait to allow System Variables to change</title>
            <teststep timestamp="  37.299292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=10ms (max=10ms)</teststep>
            <teststep timestamp="  37.299292" level="1" type="auto" ident="1" result="pass">Waited for 10 ms.</teststep>
            <result timestamp="  37.299292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  37.299292" name="awaitvaluematch" type="testprimitive">
            <title>Wait until there is no diagnostic request active and no further requests are queued.</title>
            <teststep timestamp="  37.299292" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  37.299292" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
            <teststep timestamp="  37.299292" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::DiagRequestState&apos;: = 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE), condition: == 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE)</teststep>
            <result timestamp="  37.299292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  37.299292" name="statechange" type="testpattern">
            <title>Reset response code</title>
            <teststep timestamp="  37.299292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagResponseCode&apos;: 0 (0x00: No Response received)</teststep>
            <teststep timestamp="  37.299292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  37.309292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=10ms (max=10ms)</teststep>
            <teststep timestamp="  37.309292" level="2" type="auto" ident="2" result="pass">Waited for 10 ms.</teststep>
            <teststep timestamp="  37.309292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 0 (0x00: No Response received), condition: == 0 (0x00: No Response received)</teststep>
            <teststep timestamp="  37.309292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  37.309292" result="pass"/>
          </testpattern>
          <prepend endtime="2020-09-01 10:02:47" endtimestamp="  37.309292" />
        </preparation>
        <testpattern timestamp="  37.309292" name="statechange" type="testpattern">
          <title>Initialize: Reset DID value to 0 via RxData</title>
          <teststep timestamp="  37.309292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagResetCounterWithRxData&apos;: 0</teststep>
          <teststep timestamp="  37.309292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  37.409292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  37.409292" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
          <teststep timestamp="  37.409292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResetCounterWithRxData&apos; value passed: = 0, condition: == 0</teststep>
          <teststep timestamp="  37.409292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  37.409292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagResetCounterWithRxData&apos;: 0</teststep>
          <teststep timestamp="  37.409292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  37.509292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  37.509292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  37.509292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  37.509292" name="wait" type="controlfunction">
          <title>Wait to allow for transmission of control signals</title>
          <teststep timestamp="  38.709292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1200ms (max=1200ms)</teststep>
          <teststep timestamp="  38.709292" level="1" type="auto" ident="1" result="pass">Waited for 1200 ms.</teststep>
          <result timestamp="  38.709292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  38.709292" name="awaitvaluematch" type="testprimitive">
          <title>Get DID value via TxData and verify that it was reset to 0x0000</title>
          <teststep timestamp="  38.709292" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
          <teststep timestamp="  38.709292" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  38.709292" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::DiagCounterValueFromTxData&apos;: = 0, condition: == 0</teststep>
          <result timestamp="  38.709292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  38.709292" name="statechange" type="testpattern">
          <title>Set DID value to 0x0005 via WDBI</title>
          <teststep timestamp="  38.709292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagSetCounter&apos;: 5</teststep>
          <teststep timestamp="  38.709292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  39.709292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
          <teststep timestamp="  39.709292" level="2" type="auto" ident="2" result="pass">Waited for 1000 ms.</teststep>
          <teststep timestamp="  39.709292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 110 (0x6E: Positive Response WDBI), condition: == 110 (0x6E: Positive Response WDBI)</teststep>
          <teststep timestamp="  39.709292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <result timestamp="  39.709292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  39.709292" name="awaitvaluematch" type="testprimitive">
          <title>Get DID value via TxData and verify that it was set to 0x0005</title>
          <teststep timestamp="  39.709292" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
          <teststep timestamp="  39.709292" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
          <teststep timestamp="  39.709292" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::DiagCounterValueFromTxData&apos;: = 5, condition: == 5</teststep>
          <result timestamp="  39.709292" result="pass"/>
        </testpattern>
        <completion starttime="2020-09-01 10:02:50" timestamp="  39.709292">
          <comment timestamp="  39.709292">
            <text>Value of system variable &apos;StartApplication::ErrorLog&apos;  is </text>
          </comment>
          <compend endtime="2020-09-01 10:02:50" endtimestamp="  39.709292" />
        </completion>
        <verdict time="2020-09-01 10:02:50" timestamp="  39.709292" endtime="2020-09-01 10:02:50" endtimestamp="  39.709292" result="pass" />
        <title>Write Diagnostic Data via UDS over CAN ( Vita_CAN0 )</title>
        <ident>TCASE-483095</ident>
        <description>Verify that a diagnostic data record (DID) can be written via UDS over CAN</description>
      </testcase>
      <title>Diagnostics over CAN</title>
    </testgroup>
    <testgroup>
      <testcase starttime="2020-09-01 10:02:50" timestamp="  39.709292">
        <preparation starttime="2020-09-01 10:02:50" timestamp="  39.709292">
          <testpattern timestamp="  39.709292" name="set" type="testprimitive">
            <title>Clear error log</title>
            <details timestamp="  39.709292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ErrorLog&apos;</name>
                <description></description>
              </info>
            </details>
            <teststep timestamp="  39.709292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  39.709292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  39.709292" name="statechange" type="testpattern">
            <title>Activate DIAG Use Case</title>
            <teststep timestamp="  39.709292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::UseCaseActivator&apos;: 2 (Diag)</teststep>
            <teststep timestamp="  39.709292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  40.909292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1200ms (max=1200ms)</teststep>
            <teststep timestamp="  40.909292" level="2" type="auto" ident="2" result="pass">Waited for 1200 ms.</teststep>
            <teststep timestamp="  40.909292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::UseCaseActivator&apos; value passed: = 2 (Diag), condition: == 2 (Diag)</teststep>
            <teststep timestamp="  40.909292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  40.909292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  40.909292" name="awaitvaluematch" type="testprimitive">
            <title>Verify that the current CANoe version is suitable for the Start Application</title>
            <teststep timestamp="  40.909292" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  40.909292" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
            <teststep timestamp="  40.909292" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::CANoeVersionInfo&apos;: = 100000, condition: &gt;= 80200</teststep>
            <result timestamp="  40.909292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  40.909292" name="set" type="testprimitive">
            <title>Initialize variables</title>
            <details timestamp="  40.909292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagChannel&apos;</name>
                <description>0 (Vita_CAN0) </description>
              </info>
            </details>
            <details timestamp="  40.909292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagSetEventStatusPassed&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  40.909292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagGetCounter&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  40.909292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagGetDTCSnapshot&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  40.909292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagResetCounterWithRxData&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  40.909292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagClearDTC&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  40.909292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagCounterValue&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  40.909292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::DiagSnapshotDataValue&apos;</name>
                <description>0</description>
              </info>
            </details>
            <teststep timestamp="  40.909292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  40.909292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  40.909292" name="wait" type="controlfunction">
            <title>Wait to allow System Variables to change</title>
            <teststep timestamp="  40.919292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=10ms (max=10ms)</teststep>
            <teststep timestamp="  40.919292" level="1" type="auto" ident="1" result="pass">Waited for 10 ms.</teststep>
            <result timestamp="  40.919292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  40.919292" name="awaitvaluematch" type="testprimitive">
            <title>Wait until there is no diagnostic request active and no further requests are queued.</title>
            <teststep timestamp="  40.919292" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  40.919292" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 1 value condition(s).</teststep>
            <teststep timestamp="  40.919292" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::DiagRequestState&apos;: = 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE), condition: == 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE)</teststep>
            <result timestamp="  40.919292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  40.919292" name="statechange" type="testpattern">
            <title>Reset response code</title>
            <teststep timestamp="  40.919292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagResponseCode&apos;: 0 (0x00: No Response received)</teststep>
            <teststep timestamp="  40.919292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  40.929292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=10ms (max=10ms)</teststep>
            <teststep timestamp="  40.929292" level="2" type="auto" ident="2" result="pass">Waited for 10 ms.</teststep>
            <teststep timestamp="  40.929292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 0 (0x00: No Response received), condition: == 0 (0x00: No Response received)</teststep>
            <teststep timestamp="  40.929292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  40.929292" result="pass"/>
          </testpattern>
          <prepend endtime="2020-09-01 10:02:51" endtimestamp="  40.929292" />
        </preparation>
        <testpattern timestamp="  40.929292" name="statechange" type="testpattern">
          <title>Initialize: Reset event counter to 0 via RxData</title>
          <teststep timestamp="  40.929292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagResetCounterWithRxData&apos;: 1</teststep>
          <teststep timestamp="  40.929292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  41.029292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  41.029292" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
          <teststep timestamp="  41.029292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResetCounterWithRxData&apos; value passed: = 1, condition: == 1</teststep>
          <teststep timestamp="  41.029292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  41.029292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagResetCounterWithRxData&apos;: 0</teststep>
          <teststep timestamp="  41.029292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  41.129292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  41.129292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  41.129292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  41.129292" name="wait" type="controlfunction">
          <title>Wait to allow for transmission of control signals</title>
          <teststep timestamp="  42.329292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1200ms (max=1200ms)</teststep>
          <teststep timestamp="  42.329292" level="1" type="auto" ident="1" result="pass">Waited for 1200 ms.</teststep>
          <result timestamp="  42.329292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  42.329292" name="statechange" type="testpattern">
          <title>Initialize: Clear DTC</title>
          <teststep timestamp="  42.329292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagClearDTC&apos;: 1</teststep>
          <teststep timestamp="  42.329292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  43.329292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
          <teststep timestamp="  43.329292" level="2" type="auto" ident="2" result="pass">Waited for 1000 ms.</teststep>
          <teststep timestamp="  43.329292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 84 (0x54: Positive Response CDTCI), condition: == 84 (0x54: Positive Response CDTCI)</teststep>
          <teststep timestamp="  43.329292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  43.329292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagClearDTC&apos;: 0</teststep>
          <teststep timestamp="  43.329292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  43.429292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  43.429292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  43.429292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  43.429292" name="statechange" type="testpattern">
          <title>Get DTC snapshot and verify that no snapshot record is returned (i.e. -1)</title>
          <teststep timestamp="  43.429292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 1</teststep>
          <teststep timestamp="  43.429292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  44.429292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
          <teststep timestamp="  44.429292" level="2" type="auto" ident="2" result="pass">Waited for 1000 ms.</teststep>
          <teststep timestamp="  44.429292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagSnapshotDataValue&apos; value passed: = -1, condition: == -1</teststep>
          <teststep timestamp="  44.429292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</teststep>
          <teststep timestamp="  44.429292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  44.429292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 0</teststep>
          <teststep timestamp="  44.429292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  44.529292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  44.529292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  44.529292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  44.529292" name="statechange" type="testpattern">
          <title>Set event status to passed</title>
          <teststep timestamp="  44.529292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagSetEventStatusPassed&apos;: 1</teststep>
          <teststep timestamp="  44.529292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  44.629292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  44.629292" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
          <teststep timestamp="  44.629292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagSetEventStatusPassed&apos; value passed: = 1, condition: == 1</teststep>
          <teststep timestamp="  44.629292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  44.629292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagSetEventStatusPassed&apos;: 0</teststep>
          <teststep timestamp="  44.629292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  44.729292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  44.729292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  44.729292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  44.729292" name="statechange" type="testpattern">
          <title>Get DTC snapshot and verify that still no snapshot record is returned (i.e. -1)</title>
          <teststep timestamp="  44.729292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 1</teststep>
          <teststep timestamp="  44.729292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  45.729292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
          <teststep timestamp="  45.729292" level="2" type="auto" ident="2" result="pass">Waited for 1000 ms.</teststep>
          <teststep timestamp="  45.729292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagSnapshotDataValue&apos; value passed: = -1, condition: == -1</teststep>
          <teststep timestamp="  45.729292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</teststep>
          <teststep timestamp="  45.729292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  45.729292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 0</teststep>
          <teststep timestamp="  45.729292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  45.829292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  45.829292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  45.829292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  45.829292" name="statechange" type="testpattern">
          <title>Set event status to failed</title>
          <teststep timestamp="  45.829292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagSetEventStatusFailed&apos;: 1</teststep>
          <teststep timestamp="  45.829292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  45.929292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  45.929292" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
          <teststep timestamp="  45.929292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagSetEventStatusFailed&apos; value passed: = 1, condition: == 1</teststep>
          <teststep timestamp="  45.929292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  45.929292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagSetEventStatusFailed&apos;: 0</teststep>
          <teststep timestamp="  45.929292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  46.029292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  46.029292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  46.029292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  46.029292" name="statechange" type="testpattern">
          <title>Get DTC snapshot and verify that a snapshot record with the value 2 is returned</title>
          <teststep timestamp="  46.029292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 1</teststep>
          <teststep timestamp="  46.029292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  47.029292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
          <teststep timestamp="  47.029292" level="2" type="auto" ident="2" result="pass">Waited for 1000 ms.</teststep>
          <teststep timestamp="  47.029292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagSnapshotDataValue&apos; value passed: = 2, condition: == 2</teststep>
          <teststep timestamp="  47.029292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</teststep>
          <teststep timestamp="  47.029292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  47.029292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 0</teststep>
          <teststep timestamp="  47.029292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  47.129292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  47.129292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  47.129292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  47.129292" name="statechange" type="testpattern">
          <title>Set event status to passed</title>
          <teststep timestamp="  47.129292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagSetEventStatusPassed&apos;: 1</teststep>
          <teststep timestamp="  47.129292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  47.229292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  47.229292" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
          <teststep timestamp="  47.229292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagSetEventStatusPassed&apos; value passed: = 1, condition: == 1</teststep>
          <teststep timestamp="  47.229292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  47.229292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagSetEventStatusPassed&apos;: 0</teststep>
          <teststep timestamp="  47.229292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  47.329292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  47.329292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  47.329292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  47.329292" name="statechange" type="testpattern">
          <title>Get DTC snapshot and verify that still a snapshot record with the value 2 is returned</title>
          <teststep timestamp="  47.329292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 1</teststep>
          <teststep timestamp="  47.329292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  48.329292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
          <teststep timestamp="  48.329292" level="2" type="auto" ident="2" result="pass">Waited for 1000 ms.</teststep>
          <teststep timestamp="  48.329292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagSnapshotDataValue&apos; value passed: = 2, condition: == 2</teststep>
          <teststep timestamp="  48.329292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</teststep>
          <teststep timestamp="  48.329292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  48.329292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 0</teststep>
          <teststep timestamp="  48.329292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  48.429292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  48.429292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  48.429292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  48.429292" name="statechange" type="testpattern">
          <title>Set event status to failed</title>
          <teststep timestamp="  48.429292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagSetEventStatusFailed&apos;: 1</teststep>
          <teststep timestamp="  48.429292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  48.529292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  48.529292" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
          <teststep timestamp="  48.529292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagSetEventStatusFailed&apos; value passed: = 1, condition: == 1</teststep>
          <teststep timestamp="  48.529292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  48.529292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagSetEventStatusFailed&apos;: 0</teststep>
          <teststep timestamp="  48.529292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  48.629292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  48.629292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  48.629292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  48.629292" name="statechange" type="testpattern">
          <title>Get DTC snapshot and verify that still a snapshot record with the value 2 is returned</title>
          <teststep timestamp="  48.629292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 1</teststep>
          <teststep timestamp="  48.629292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  49.629292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
          <teststep timestamp="  49.629292" level="2" type="auto" ident="2" result="pass">Waited for 1000 ms.</teststep>
          <teststep timestamp="  49.629292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagSnapshotDataValue&apos; value passed: = 2, condition: == 2</teststep>
          <teststep timestamp="  49.629292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</teststep>
          <teststep timestamp="  49.629292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  49.629292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 0</teststep>
          <teststep timestamp="  49.629292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  49.729292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  49.729292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  49.729292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  49.729292" name="statechange" type="testpattern">
          <title>Clear DTC</title>
          <teststep timestamp="  49.729292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagClearDTC&apos;: 1</teststep>
          <teststep timestamp="  49.729292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  50.729292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
          <teststep timestamp="  50.729292" level="2" type="auto" ident="2" result="pass">Waited for 1000 ms.</teststep>
          <teststep timestamp="  50.729292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 84 (0x54: Positive Response CDTCI), condition: == 84 (0x54: Positive Response CDTCI)</teststep>
          <teststep timestamp="  50.729292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  50.729292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagClearDTC&apos;: 0</teststep>
          <teststep timestamp="  50.729292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  50.829292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  50.829292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  50.829292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  50.829292" name="statechange" type="testpattern">
          <title>Get DTC snapshot and verify that no snapshot record is returned (i.e. -1)</title>
          <teststep timestamp="  50.829292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 1</teststep>
          <teststep timestamp="  50.829292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  51.829292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
          <teststep timestamp="  51.829292" level="2" type="auto" ident="2" result="pass">Waited for 1000 ms.</teststep>
          <teststep timestamp="  51.829292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagSnapshotDataValue&apos; value passed: = -1, condition: == -1</teststep>
          <teststep timestamp="  51.829292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</teststep>
          <teststep timestamp="  51.829292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  51.829292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 0</teststep>
          <teststep timestamp="  51.829292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  51.929292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  51.929292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  51.929292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  51.929292" name="statechange" type="testpattern">
          <title>Set event status to failed</title>
          <teststep timestamp="  51.929292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagSetEventStatusFailed&apos;: 1</teststep>
          <teststep timestamp="  51.929292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  52.029292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  52.029292" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
          <teststep timestamp="  52.029292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagSetEventStatusFailed&apos; value passed: = 1, condition: == 1</teststep>
          <teststep timestamp="  52.029292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  52.029292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagSetEventStatusFailed&apos;: 0</teststep>
          <teststep timestamp="  52.029292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  52.129292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  52.129292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  52.129292" result="pass"/>
        </testpattern>
        <testpattern timestamp="  52.129292" name="statechange" type="testpattern">
          <title>Get DTC snapshot and verify that a snapshot record with the value 5 is returned</title>
          <teststep timestamp="  52.129292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 1</teststep>
          <teststep timestamp="  52.129292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
          <teststep timestamp="  53.129292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
          <teststep timestamp="  53.129292" level="2" type="auto" ident="2" result="pass">Waited for 1000 ms.</teststep>
          <teststep timestamp="  53.129292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagSnapshotDataValue&apos; value passed: = 5, condition: == 5</teststep>
          <teststep timestamp="  53.129292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::DiagResponseCode&apos; value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</teststep>
          <teststep timestamp="  53.129292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
          <teststep timestamp="  53.129292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::DiagGetDTCSnapshot&apos;: 0</teststep>
          <teststep timestamp="  53.129292" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
          <teststep timestamp="  53.229292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
          <teststep timestamp="  53.229292" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
          <result timestamp="  53.229292" result="pass"/>
        </testpattern>
        <completion starttime="2020-09-01 10:03:03" timestamp="  53.229292">
          <comment timestamp="  53.229292">
            <text>Value of system variable &apos;StartApplication::ErrorLog&apos;  is </text>
          </comment>
          <compend endtime="2020-09-01 10:03:03" endtimestamp="  53.229292" />
        </completion>
        <verdict time="2020-09-01 10:03:03" timestamp="  53.229292" endtime="2020-09-01 10:03:03" endtimestamp="  53.229292" result="pass" />
        <title>Diagnostic Event Handling with FreezeFrame and without Debouncing ( Vita_CAN0 )</title>
        <ident>TCASE-379331</ident>
        <description>Verify that a diagnostic data record (DID) can be written and read via UDS over CAN</description>
      </testcase>
      <title>Diagnostic Event Handling</title>
    </testgroup>
    <title>Usecase DIAG</title>
  </testgroup>
  <testgroup>
    <testgroup>
      <testcase starttime="2020-09-01 10:03:03" timestamp="  53.229292">
        <preparation starttime="2020-09-01 10:03:03" timestamp="  53.229292">
          <vardef timestamp="  53.229292" name="valueToWrite" type="integer">
            <testerinput>
              <inputvalue>0</inputvalue>
            </testerinput>
          </vardef>
          <testpattern timestamp="  53.229292" name="set" type="testprimitive">
            <title>Clear error log</title>
            <details timestamp="  53.229292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ErrorLog&apos;</name>
                <description></description>
              </info>
            </details>
            <teststep timestamp="  53.229292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  53.229292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  53.229292" name="statechange" type="testpattern">
            <title>Activate MEM Use Case</title>
            <teststep timestamp="  53.229292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::UseCaseActivator&apos;: 1 (Mem)</teststep>
            <teststep timestamp="  53.229292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  54.429292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1200ms (max=1200ms)</teststep>
            <teststep timestamp="  54.429292" level="2" type="auto" ident="2" result="pass">Waited for 1200 ms.</teststep>
            <teststep timestamp="  54.429292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::UseCaseActivator&apos; value passed: = 1 (Mem), condition: == 1 (Mem)</teststep>
            <teststep timestamp="  54.429292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  54.429292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  54.429292" name="statechange" type="testpattern">
            <title>Set active block</title>
            <teststep timestamp="  54.429292" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvBlockSelector&apos;: 0 (Block 1 (FEE))</teststep>
            <teststep timestamp="  54.429292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  54.929292" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=500ms (max=500ms)</teststep>
            <teststep timestamp="  54.929292" level="2" type="auto" ident="2" result="pass">Waited for 500 ms.</teststep>
            <teststep timestamp="  54.929292" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::MemNvBlockSelector&apos; value passed: = 0 (Block 1 (FEE)), condition: == 0 (Block 1 (FEE))</teststep>
            <teststep timestamp="  54.929292" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  54.929292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  54.929292" name="set" type="testprimitive">
            <title>Initialize variables</title>
            <details timestamp="  54.929292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvStore&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  54.929292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvStoreValue&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  54.929292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvReadCurrValue&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  54.929292">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvPendingExtended&apos;</name>
                <description>0 (UNKNOWN) </description>
              </info>
            </details>
            <teststep timestamp="  54.929292" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  54.929292" result="pass"/>
          </testpattern>
          <testpattern timestamp="  54.929292" name="awaitvaluematch" type="testprimitive">
            <title>Wait for initial read operation after use case activation to complete</title>
            <teststep timestamp="  55.024891" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;MemNvPendingExtended&apos; Elapsed time=95.5998ms (max=10000ms)</teststep>
            <teststep timestamp="  55.024891" level="2" type="auto" ident="1" result="pass">Waited for occurrence of any of 2 value condition(s).</teststep>
            <teststep timestamp="  55.024891" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvPendingExtended&apos;: = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</teststep>
            <teststep timestamp="  55.024891" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvPendingExtended&apos;: = 4 (READ_FINISHED), condition: == 6 (READ_FAILED)</teststep>
            <result timestamp="  55.024891" result="pass"/>
          </testpattern>
          <prepend endtime="2020-09-01 10:03:05" endtimestamp="  55.024891" />
        </preparation>
        <testpattern timestamp="  55.024891" name="for_loop" type="controlfunction">
          <title>Repeat writing and reading of the NV block with the values 0, 15 and 30</title>
          <teststep timestamp="  55.024891" level="0" type="auto" ident="" result="na">Set specified value.\nVariable &apos;valueToWrite&apos;: 0</teststep>
          <teststep timestamp="  55.024891" level="0" type="auto" ident="" result="na">Check variable &apos;valueToWrite&apos; value passed: = 0, condition: &lt; 31</teststep>
          <teststep timestamp="  55.024891" level="0" type="auto" ident="0" result="na">For-loop condition is fulfilled, executing loop body.</teststep>
          <testpattern timestamp="  55.024891" name="set" type="testprimitive">
            <title>Set the new value to write</title>
            <details timestamp="  55.024891">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvStoreValue&apos;</name>
                <description>0</description>
              </info>
            </details>
            <teststep timestamp="  55.024891" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  55.024891" result="pass"/>
          </testpattern>
          <testpattern timestamp="  55.024891" name="statechange" type="testpattern">
            <title>Trigger writing of the value. After the write is complete the read operation is started automatically</title>
            <teststep timestamp="  55.024891" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvStore&apos;: 1</teststep>
            <teststep timestamp="  55.024891" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  55.124891" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
            <teststep timestamp="  55.124891" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
            <teststep timestamp="  55.124891" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::MemNvStore&apos; value passed: = 1, condition: == 1</teststep>
            <teststep timestamp="  55.124891" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <teststep timestamp="  55.124891" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvStore&apos;: 0</teststep>
            <teststep timestamp="  55.124891" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
            <teststep timestamp="  55.224891" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
            <teststep timestamp="  55.224891" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
            <result timestamp="  55.224891" result="pass"/>
          </testpattern>
          <testpattern timestamp="  55.224891" name="wait" type="controlfunction">
            <title>Wait to allow for transmission of control signals</title>
            <teststep timestamp="  56.224891" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
            <teststep timestamp="  56.224891" level="1" type="auto" ident="1" result="pass">Waited for 1000 ms.</teststep>
            <result timestamp="  56.224891" result="pass"/>
          </testpattern>
          <testpattern timestamp="  56.224891" name="awaitvaluematch" type="testprimitive">
            <title>Wait for the read operation to complete and verify that the received value is the one which was previously set to be written</title>
            <teststep timestamp="  56.224891" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  56.224891" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 2 value condition(s).</teststep>
            <teststep timestamp="  56.224891" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvReadCurrValue&apos;: = 0, condition: == 0</teststep>
            <teststep timestamp="  56.224891" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvPendingExtended&apos;: = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</teststep>
            <result timestamp="  56.224891" result="pass"/>
          </testpattern>
          <teststep timestamp="  56.224891" level="0" type="auto" ident="" result="na">Check variable &apos;valueToWrite&apos; value passed: = 15, condition: &lt; 31</teststep>
          <teststep timestamp="  56.224891" level="0" type="auto" ident="15" result="na">For-loop condition is fulfilled, executing loop body.</teststep>
          <testpattern timestamp="  56.224891" name="set" type="testprimitive">
            <title>Set the new value to write</title>
            <details timestamp="  56.224891">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvStoreValue&apos;</name>
                <description>15</description>
              </info>
            </details>
            <teststep timestamp="  56.224891" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  56.224891" result="pass"/>
          </testpattern>
          <testpattern timestamp="  56.224891" name="statechange" type="testpattern">
            <title>Trigger writing of the value. After the write is complete the read operation is started automatically</title>
            <teststep timestamp="  56.224891" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvStore&apos;: 1</teststep>
            <teststep timestamp="  56.224891" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  56.324891" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
            <teststep timestamp="  56.324891" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
            <teststep timestamp="  56.324891" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::MemNvStore&apos; value passed: = 1, condition: == 1</teststep>
            <teststep timestamp="  56.324891" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <teststep timestamp="  56.324891" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvStore&apos;: 0</teststep>
            <teststep timestamp="  56.324891" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
            <teststep timestamp="  56.424891" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
            <teststep timestamp="  56.424891" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
            <result timestamp="  56.424891" result="pass"/>
          </testpattern>
          <testpattern timestamp="  56.424891" name="wait" type="controlfunction">
            <title>Wait to allow for transmission of control signals</title>
            <teststep timestamp="  57.424891" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
            <teststep timestamp="  57.424891" level="1" type="auto" ident="1" result="pass">Waited for 1000 ms.</teststep>
            <result timestamp="  57.424891" result="pass"/>
          </testpattern>
          <testpattern timestamp="  57.424891" name="awaitvaluematch" type="testprimitive">
            <title>Wait for the read operation to complete and verify that the received value is the one which was previously set to be written</title>
            <teststep timestamp="  57.424891" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  57.424891" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 2 value condition(s).</teststep>
            <teststep timestamp="  57.424891" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvReadCurrValue&apos;: = 15, condition: == 15</teststep>
            <teststep timestamp="  57.424891" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvPendingExtended&apos;: = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</teststep>
            <result timestamp="  57.424891" result="pass"/>
          </testpattern>
          <teststep timestamp="  57.424891" level="0" type="auto" ident="" result="na">Check variable &apos;valueToWrite&apos; value passed: = 30, condition: &lt; 31</teststep>
          <teststep timestamp="  57.424891" level="0" type="auto" ident="30" result="na">For-loop condition is fulfilled, executing loop body.</teststep>
          <testpattern timestamp="  57.424891" name="set" type="testprimitive">
            <title>Set the new value to write</title>
            <details timestamp="  57.424891">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvStoreValue&apos;</name>
                <description>30</description>
              </info>
            </details>
            <teststep timestamp="  57.424891" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  57.424891" result="pass"/>
          </testpattern>
          <testpattern timestamp="  57.424891" name="statechange" type="testpattern">
            <title>Trigger writing of the value. After the write is complete the read operation is started automatically</title>
            <teststep timestamp="  57.424891" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvStore&apos;: 1</teststep>
            <teststep timestamp="  57.424891" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  57.524891" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
            <teststep timestamp="  57.524891" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
            <teststep timestamp="  57.524891" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::MemNvStore&apos; value passed: = 1, condition: == 1</teststep>
            <teststep timestamp="  57.524891" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <teststep timestamp="  57.524891" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvStore&apos;: 0</teststep>
            <teststep timestamp="  57.524891" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
            <teststep timestamp="  57.624891" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
            <teststep timestamp="  57.624891" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
            <result timestamp="  57.624891" result="pass"/>
          </testpattern>
          <testpattern timestamp="  57.624891" name="wait" type="controlfunction">
            <title>Wait to allow for transmission of control signals</title>
            <teststep timestamp="  58.624891" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
            <teststep timestamp="  58.624891" level="1" type="auto" ident="1" result="pass">Waited for 1000 ms.</teststep>
            <result timestamp="  58.624891" result="pass"/>
          </testpattern>
          <testpattern timestamp="  58.624891" name="awaitvaluematch" type="testprimitive">
            <title>Wait for the read operation to complete and verify that the received value is the one which was previously set to be written</title>
            <teststep timestamp="  58.624891" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  58.624891" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 2 value condition(s).</teststep>
            <teststep timestamp="  58.624891" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvReadCurrValue&apos;: = 30, condition: == 30</teststep>
            <teststep timestamp="  58.624891" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvPendingExtended&apos;: = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</teststep>
            <result timestamp="  58.624891" result="pass"/>
          </testpattern>
          <teststep timestamp="  58.624891" level="0" type="auto" ident="" result="na">Check variable &apos;valueToWrite&apos; value failed: = 45, condition: &lt; 31</teststep>
          <teststep timestamp="  58.624891" level="0" type="auto" ident="" result="na">For-loop condition is not fulfilled, leaving loop.</teststep>
          <result timestamp="  58.624891" result="pass"/>
        </testpattern>
        <completion starttime="2020-09-01 10:03:09" timestamp="  58.624891">
          <comment timestamp="  58.624891">
            <text>Value of system variable &apos;StartApplication::ErrorLog&apos;  is </text>
          </comment>
          <compend endtime="2020-09-01 10:03:09" endtimestamp="  58.624891" />
        </completion>
        <verdict time="2020-09-01 10:03:09" timestamp="  58.624891" endtime="2020-09-01 10:03:09" endtimestamp="  58.624891" result="pass" />
        <title>Write and Read NV block (Block 1, FEE)</title>
        <ident>TCASE-379342</ident>
        <description>Verify that the application can write and read non-volatile data.</description>
      </testcase>
      <testcase starttime="2020-09-01 10:03:09" timestamp="  58.624891">
        <preparation starttime="2020-09-01 10:03:09" timestamp="  58.624891">
          <vardef timestamp="  58.624891" name="valueToWrite" type="integer">
            <testerinput>
              <inputvalue>0</inputvalue>
            </testerinput>
          </vardef>
          <testpattern timestamp="  58.624891" name="set" type="testprimitive">
            <title>Clear error log</title>
            <details timestamp="  58.624891">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::ErrorLog&apos;</name>
                <description></description>
              </info>
            </details>
            <teststep timestamp="  58.624891" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  58.624891" result="pass"/>
          </testpattern>
          <testpattern timestamp="  58.624891" name="statechange" type="testpattern">
            <title>Activate MEM Use Case</title>
            <teststep timestamp="  58.624891" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::UseCaseActivator&apos;: 1 (Mem)</teststep>
            <teststep timestamp="  58.624891" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  59.824891" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1200ms (max=1200ms)</teststep>
            <teststep timestamp="  59.824891" level="2" type="auto" ident="2" result="pass">Waited for 1200 ms.</teststep>
            <teststep timestamp="  59.824891" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::UseCaseActivator&apos; value passed: = 1 (Mem), condition: == 1 (Mem)</teststep>
            <teststep timestamp="  59.824891" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  59.824891" result="pass"/>
          </testpattern>
          <testpattern timestamp="  59.824891" name="statechange" type="testpattern">
            <title>Set active block</title>
            <teststep timestamp="  59.824891" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvBlockSelector&apos;: 1 (Block 2 (FEE))</teststep>
            <teststep timestamp="  59.824891" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  60.324891" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=500ms (max=500ms)</teststep>
            <teststep timestamp="  60.324891" level="2" type="auto" ident="2" result="pass">Waited for 500 ms.</teststep>
            <teststep timestamp="  60.324891" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::MemNvBlockSelector&apos; value passed: = 1 (Block 2 (FEE)), condition: == 1 (Block 2 (FEE))</teststep>
            <teststep timestamp="  60.324891" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <result timestamp="  60.324891" result="pass"/>
          </testpattern>
          <testpattern timestamp="  60.324891" name="set" type="testprimitive">
            <title>Initialize variables</title>
            <details timestamp="  60.324891">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvStore&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  60.324891">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvStoreValue&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  60.324891">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvReadCurrValue&apos;</name>
                <description>0</description>
              </info>
            </details>
            <details timestamp="  60.324891">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvPendingExtended&apos;</name>
                <description>0 (UNKNOWN) </description>
              </info>
            </details>
            <teststep timestamp="  60.324891" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  60.324891" result="pass"/>
          </testpattern>
          <testpattern timestamp="  60.324891" name="awaitvaluematch" type="testprimitive">
            <title>Wait for initial read operation after use case activation to complete</title>
            <teststep timestamp="  61.025252" level="1" type="auto" ident="Resume reason" result="na">Resumed on sysvar &apos;MemNvPendingExtended&apos; Elapsed time=700.361ms (max=10000ms)</teststep>
            <teststep timestamp="  61.025252" level="2" type="auto" ident="1" result="pass">Waited for occurrence of any of 2 value condition(s).</teststep>
            <teststep timestamp="  61.025252" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvPendingExtended&apos;: = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</teststep>
            <teststep timestamp="  61.025252" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvPendingExtended&apos;: = 4 (READ_FINISHED), condition: == 6 (READ_FAILED)</teststep>
            <result timestamp="  61.025252" result="pass"/>
          </testpattern>
          <prepend endtime="2020-09-01 10:03:11" endtimestamp="  61.025252" />
        </preparation>
        <testpattern timestamp="  61.025252" name="for_loop" type="controlfunction">
          <title>Repeat writing and reading of the NV block with the values 0, 15 and 30</title>
          <teststep timestamp="  61.025252" level="0" type="auto" ident="" result="na">Set specified value.\nVariable &apos;valueToWrite&apos;: 0</teststep>
          <teststep timestamp="  61.025252" level="0" type="auto" ident="" result="na">Check variable &apos;valueToWrite&apos; value passed: = 0, condition: &lt; 31</teststep>
          <teststep timestamp="  61.025252" level="0" type="auto" ident="0" result="na">For-loop condition is fulfilled, executing loop body.</teststep>
          <testpattern timestamp="  61.025252" name="set" type="testprimitive">
            <title>Set the new value to write</title>
            <details timestamp="  61.025252">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvStoreValue&apos;</name>
                <description>0</description>
              </info>
            </details>
            <teststep timestamp="  61.025252" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  61.025252" result="pass"/>
          </testpattern>
          <testpattern timestamp="  61.025252" name="statechange" type="testpattern">
            <title>Trigger writing of the value. After the write is complete the read operation is started automatically</title>
            <teststep timestamp="  61.025252" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvStore&apos;: 1</teststep>
            <teststep timestamp="  61.025252" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  61.125252" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
            <teststep timestamp="  61.125252" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
            <teststep timestamp="  61.125252" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::MemNvStore&apos; value passed: = 1, condition: == 1</teststep>
            <teststep timestamp="  61.125252" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <teststep timestamp="  61.125252" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvStore&apos;: 0</teststep>
            <teststep timestamp="  61.125252" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
            <teststep timestamp="  61.225252" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
            <teststep timestamp="  61.225252" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
            <result timestamp="  61.225252" result="pass"/>
          </testpattern>
          <testpattern timestamp="  61.225252" name="wait" type="controlfunction">
            <title>Wait to allow for transmission of control signals</title>
            <teststep timestamp="  62.225252" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
            <teststep timestamp="  62.225252" level="1" type="auto" ident="1" result="pass">Waited for 1000 ms.</teststep>
            <result timestamp="  62.225252" result="pass"/>
          </testpattern>
          <testpattern timestamp="  62.225252" name="awaitvaluematch" type="testprimitive">
            <title>Wait for the read operation to complete and verify that the received value is the one which was previously set to be written</title>
            <teststep timestamp="  62.225252" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  62.225252" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 2 value condition(s).</teststep>
            <teststep timestamp="  62.225252" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvReadCurrValue&apos;: = 0, condition: == 0</teststep>
            <teststep timestamp="  62.225252" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvPendingExtended&apos;: = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</teststep>
            <result timestamp="  62.225252" result="pass"/>
          </testpattern>
          <teststep timestamp="  62.225252" level="0" type="auto" ident="" result="na">Check variable &apos;valueToWrite&apos; value passed: = 15, condition: &lt; 31</teststep>
          <teststep timestamp="  62.225252" level="0" type="auto" ident="15" result="na">For-loop condition is fulfilled, executing loop body.</teststep>
          <testpattern timestamp="  62.225252" name="set" type="testprimitive">
            <title>Set the new value to write</title>
            <details timestamp="  62.225252">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvStoreValue&apos;</name>
                <description>15</description>
              </info>
            </details>
            <teststep timestamp="  62.225252" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  62.225252" result="pass"/>
          </testpattern>
          <testpattern timestamp="  62.225252" name="statechange" type="testpattern">
            <title>Trigger writing of the value. After the write is complete the read operation is started automatically</title>
            <teststep timestamp="  62.225252" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvStore&apos;: 1</teststep>
            <teststep timestamp="  62.225252" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  62.325252" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
            <teststep timestamp="  62.325252" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
            <teststep timestamp="  62.325252" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::MemNvStore&apos; value passed: = 1, condition: == 1</teststep>
            <teststep timestamp="  62.325252" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <teststep timestamp="  62.325252" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvStore&apos;: 0</teststep>
            <teststep timestamp="  62.325252" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
            <teststep timestamp="  62.425252" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
            <teststep timestamp="  62.425252" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
            <result timestamp="  62.425252" result="pass"/>
          </testpattern>
          <testpattern timestamp="  62.425252" name="wait" type="controlfunction">
            <title>Wait to allow for transmission of control signals</title>
            <teststep timestamp="  63.425252" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
            <teststep timestamp="  63.425252" level="1" type="auto" ident="1" result="pass">Waited for 1000 ms.</teststep>
            <result timestamp="  63.425252" result="pass"/>
          </testpattern>
          <testpattern timestamp="  63.425252" name="awaitvaluematch" type="testprimitive">
            <title>Wait for the read operation to complete and verify that the received value is the one which was previously set to be written</title>
            <teststep timestamp="  63.425252" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  63.425252" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 2 value condition(s).</teststep>
            <teststep timestamp="  63.425252" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvReadCurrValue&apos;: = 15, condition: == 15</teststep>
            <teststep timestamp="  63.425252" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvPendingExtended&apos;: = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</teststep>
            <result timestamp="  63.425252" result="pass"/>
          </testpattern>
          <teststep timestamp="  63.425252" level="0" type="auto" ident="" result="na">Check variable &apos;valueToWrite&apos; value passed: = 30, condition: &lt; 31</teststep>
          <teststep timestamp="  63.425252" level="0" type="auto" ident="30" result="na">For-loop condition is fulfilled, executing loop body.</teststep>
          <testpattern timestamp="  63.425252" name="set" type="testprimitive">
            <title>Set the new value to write</title>
            <details timestamp="  63.425252">
              <title>Set specified value.</title>
              <info>
                <name>System variable &apos;StartApplication::MemNvStoreValue&apos;</name>
                <description>30</description>
              </info>
            </details>
            <teststep timestamp="  63.425252" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <result timestamp="  63.425252" result="pass"/>
          </testpattern>
          <testpattern timestamp="  63.425252" name="statechange" type="testpattern">
            <title>Trigger writing of the value. After the write is complete the read operation is started automatically</title>
            <teststep timestamp="  63.425252" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvStore&apos;: 1</teststep>
            <teststep timestamp="  63.425252" level="2" type="auto" ident="1" result="pass">Stimulation of the input parameters</teststep>
            <teststep timestamp="  63.525252" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
            <teststep timestamp="  63.525252" level="2" type="auto" ident="2" result="pass">Waited for 100 ms.</teststep>
            <teststep timestamp="  63.525252" level="0" type="auto" ident="" result="na">Check system variable &apos;StartApplication::MemNvStore&apos; value passed: = 1, condition: == 1</teststep>
            <teststep timestamp="  63.525252" level="2" type="auto" ident="3" result="pass">Validation of the expected parameters</teststep>
            <teststep timestamp="  63.525252" level="0" type="auto" ident="" result="na">Successfully set system variable &apos;StartApplication::MemNvStore&apos;: 0</teststep>
            <teststep timestamp="  63.525252" level="2" type="auto" ident="4" result="pass">Reset of the input parameters</teststep>
            <teststep timestamp="  63.625252" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=100ms (max=100ms)</teststep>
            <teststep timestamp="  63.625252" level="2" type="auto" ident="5" result="pass">Waited for 100 ms.</teststep>
            <result timestamp="  63.625252" result="pass"/>
          </testpattern>
          <testpattern timestamp="  63.625252" name="wait" type="controlfunction">
            <title>Wait to allow for transmission of control signals</title>
            <teststep timestamp="  64.625252" level="1" type="auto" ident="Resume reason" result="na"> Elapsed time=1000ms (max=1000ms)</teststep>
            <teststep timestamp="  64.625252" level="1" type="auto" ident="1" result="pass">Waited for 1000 ms.</teststep>
            <result timestamp="  64.625252" result="pass"/>
          </testpattern>
          <testpattern timestamp="  64.625252" name="awaitvaluematch" type="testprimitive">
            <title>Wait for the read operation to complete and verify that the received value is the one which was previously set to be written</title>
            <teststep timestamp="  64.625252" level="1" type="auto" ident="Resume reason" result="na">Immediately resumed on setup of wait condition.</teststep>
            <teststep timestamp="  64.625252" level="2" type="auto" ident="1" result="pass">Waited for occurrence of 2 value condition(s).</teststep>
            <teststep timestamp="  64.625252" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvReadCurrValue&apos;: = 30, condition: == 30</teststep>
            <teststep timestamp="  64.625252" level="0" type="auto" ident="" result="na">System variable &apos;StartApplication::MemNvPendingExtended&apos;: = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</teststep>
            <result timestamp="  64.625252" result="pass"/>
          </testpattern>
          <teststep timestamp="  64.625252" level="0" type="auto" ident="" result="na">Check variable &apos;valueToWrite&apos; value failed: = 45, condition: &lt; 31</teststep>
          <teststep timestamp="  64.625252" level="0" type="auto" ident="" result="na">For-loop condition is not fulfilled, leaving loop.</teststep>
          <result timestamp="  64.625252" result="pass"/>
        </testpattern>
        <completion starttime="2020-09-01 10:03:15" timestamp="  64.625252">
          <comment timestamp="  64.625252">
            <text>Value of system variable &apos;StartApplication::ErrorLog&apos;  is </text>
          </comment>
          <compend endtime="2020-09-01 10:03:15" endtimestamp="  64.625252" />
        </completion>
        <verdict time="2020-09-01 10:03:15" timestamp="  64.625252" endtime="2020-09-01 10:03:15" endtimestamp="  64.625252" result="pass" />
        <title>Write and Read NV block (Block 2, FEE)</title>
        <ident>TCASE-379342</ident>
        <description>Verify that the application can write and read non-volatile data.</description>
      </testcase>
      <title>Write and Read Nonvolatile Memory</title>
    </testgroup>
    <title>Usecase MEM</title>
  </testgroup>
  <verdict time="2020-09-01 10:03:15" timestamp="  64.625252" endtime="2020-09-01 10:03:15" endtimestamp="  64.625252" result="pass" />
  <title>StartApplication</title>
  <testlogfiles>
    <testlogfile file="" />
  </testlogfiles>
  <engineer>
    <xinfo>
      <name>Windows Login Name</name>
      <description>visvga</description>
    </xinfo>
  </engineer>
  <testsetup>
    <xinfo>
      <name>Version</name>
      <description>CANoe.ISO11783.J1939.CANaero.CANopen.LIN.MOST.FlexRay.J1587.Ethernet.Car2x.AFDX.A429.XCP.AMD.Scope.Sensor 10.0.63</description>
    </xinfo>
    <xinfo>
      <name>Configuration</name>
      <description>D:\usr\usage\Delivery\CBD20x\CBD2000456\D00\internal\StartApplication\Canoe\StartApplication\StartApplication.cfg</description>
    </xinfo>
    <xinfo>
      <name>Configuration Comment</name>
      <description></description>
    </xinfo>
    <xinfo>
      <name>Database CAN on Vita_CAN0</name>
      <description>D:\usr\usage\Delivery\CBD20x\CBD2000456\D00\internal\StartApplication\Config\System\SystemExtract.arxml (2020-08-24 13:28:40)</description>
    </xinfo>
    <xinfo>
      <name>Database LIN_MyECU on Vita_LIN0</name>
      <description>D:\usr\usage\Delivery\CBD20x\CBD2000456\D00\internal\StartApplication\input_files\LIN_MyECU.ldf (2017-11-15 13:11:18)</description>
    </xinfo>
    <xinfo>
      <name>Database FlexRay on Vita_FR0</name>
      <description>D:\usr\usage\Delivery\CBD20x\CBD2000456\D00\internal\StartApplication\Config\System\SystemExtract.arxml (2020-08-24 13:28:40)</description>
    </xinfo>
    <xinfo>
      <name>Test Module Name</name>
      <description>TestScript</description>
    </xinfo>
    <xinfo>
      <name>Test Module File</name>
      <description>D:\usr\usage\Delivery\CBD20x\CBD2000456\D00\internal\StartApplication\Appl\Source\Capl\StartApplicationTest.xml</description>
    </xinfo>
    <xinfo>
      <name>Last modification of Test Module File</name>
      <description>2020-08-07, 11:09:56</description>
    </xinfo>
    <xinfo>
      <name>Windows Computer Name</name>
      <description>VISVGA13778NBJ</description>
    </xinfo>
    <xinfo>
      <name>Nodelayer Module osek_tp</name>
      <description>OSEK_TP (VC10, Version 5.27.54, Build 54), D:\usr\usage\Delivery\CBD20x\CBD2000456\D00\internal\StartApplication\Canoe\Exec32\osek_tp.dll</description>
    </xinfo>
    <xinfo>
      <name>Nodelayer Module J1939TestServiceLibraryNL</name>
      <description>J1939 Test Service Library for CANoe, Version *********, C:\Program Files\Vector CANwin 10.0\Exec32\J1939TestServiceLibraryNL.dll</description>
    </xinfo>
  </testsetup>
  <hardware name="CAN Channel Interfaces" category="CategoryCANIF">
    <xinfoset type="device">
      <xinfoobject type="device">
      <xinfo key="channel">
        <name>Channel</name>
        <description>1</description>
      </xinfo>
      <xinfo key="device">
        <name>Device</name>
        <description>VN1630</description>
      </xinfo>
      <xinfo key="serialnumber">
        <name>Serial number</name>
        <description>504323</description>
      </xinfo>
      <xinfo key="driverdllversion">
        <name>Driver DLL version</name>
        <description>11.4.16</description>
      </xinfo>
      <xinfo key="driverversion">
        <name>Driver version</name>
        <description>10.9.12</description>
      </xinfo>
      </xinfoobject>
    </xinfoset>
  </hardware>
  <hardware name="LIN Channel Interfaces" category="CategoryLINIF">
    <xinfoset type="device">
      <xinfoobject type="device">
      <xinfo key="channel">
        <name>Channel</name>
        <description>1</description>
      </xinfo>
      <xinfo key="device">
        <name>Device</name>
        <description>VN1630</description>
      </xinfo>
      <xinfo key="serialnumber">
        <name>Serial number</name>
        <description>504323</description>
      </xinfo>
      <xinfo key="driverdllversion">
        <name>Driver DLL version</name>
        <description>11.4.16</description>
      </xinfo>
      <xinfo key="driverversion">
        <name>Driver version</name>
        <description>10.9.12</description>
      </xinfo>
      <xinfo key="firmwareversion">
        <name>LIN Firmware version</name>
        <description>1.77</description>
      </xinfo>
      </xinfoobject>
    </xinfoset>
  </hardware>
  <hardware name="FlexRay Channel Interfaces" category="CategoryFLEXRAYIF">
    <xinfoset type="device">
      <xinfoobject type="device">
      <xinfo key="channel">
        <name>Channel</name>
        <description>1</description>
      </xinfo>
      <xinfo key="device">
        <name>Device</name>
        <description>VN3600</description>
      </xinfo>
      <xinfo key="serialnumber">
        <name>Serial number</name>
        <description>2579</description>
      </xinfo>
      <xinfo key="driverdllversion">
        <name>Driver DLL version</name>
        <description>11.4.16</description>
      </xinfo>
      <xinfo key="driverversion">
        <name>Driver version</name>
        <description>8.2.26</description>
      </xinfo>
      </xinfoobject>
    </xinfoset>
  </hardware>
  <miscinfo>
    <title>Please note</title>
    <info>
      <name></name>
      <description>EXAMPLE CODE ONLY\n\n                This Example Code is only intended for illustrating an example of a possible BSW integration and BSW\n                configuration. The Example Code has not passed any quality control measures and may be incomplete. The\n                Example Code is neither intended nor qualified for use in series production. The Example Code as well\n                as any of its modifications and/or implementations must be tested with diligent care and must comply\n                with all quality requirements which are necessary according to the state of the art before their use.\n            </description>
    </info>
  </miscinfo>
</testmodule>
<!--footprint="20fd6855f887ab57764e278bf442066e" version="1"-->
