<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <ELEMENTS>
        <BSW-IMPLEMENTATION UUID="f08dba86-dea1-4578-b3c5-7afff8e5298c">
          <SHORT-NAME>CanNm_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>10.00.00</SW-VERSION>
          <USED-CODE-GENERATOR>DaV<PERSON>ci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
        </BSW-IMPLEMENTATION>
      </ELEMENTS>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="439d8ae6-8014-4334-8b66-cc96dd7e7091">
          <SHORT-NAME>CanNm_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="067a6a8d-69f3-4c66-9bdf-f4da046e27e3">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="f60e66e8-7943-477e-ba79-6cc2087c5934">
                  <SHORT-NAME>CanNm</SHORT-NAME>
                  <PROVIDED-ENTRYS>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/CanNm_ib_bswmd/BswModuleDescriptions/CanNm_MainFunction</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  </PROVIDED-ENTRYS>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="49381ed4-1cb0-4cf6-bd90-fa1978aa41e7">
                      <SHORT-NAME>CanNmBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="f6fc5974-65f3-4af8-9821-f12b8734e37c">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_0</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption by any interrupt allowed (i.e. global interrupt locks).</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="c61f92b4-a29d-4937-be1e-1dbf4c7c3d82">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_1</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption of CanNm_MainFunction or CanNm_RequestBusSynchronization by CanNm_SetUserData or CanNm_SetSleepReadyBit allowed.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="859bea19-82d0-4403-96cf-4997fa85bedd">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_2</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption of CanNm_SetUserData by CanNm_MainFunction or CanNm_RequestBusSynchronization allowed.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="6ecea4ef-62fc-4d5c-b23e-d638d9019c3c">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_3</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption of CanNm_SetSleepReadyBit by CanNm_MainFunction or CanNm_RequestBusSynchronization allowed.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="0eaf8534-978d-49f0-8baa-b74a39418a2b">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_4</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption of CanNm_RxIndication by CanNm_GetUserData or CanNm_GetPduData allowed.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="ade60f58-5f31-4e0d-93f7-ff7db25782ab">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_5</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption of CanNm_GetUserData or CanNm_GetPduData by CanNm_RxIndication allowed.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="1ec85938-7089-4b9f-b769-fd19fe673224">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_6</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption of CanNm_MainFunction by CanNm_RequestBusSynchronization allowed. Note: This critical section can be left empty when CanNm_MainFunction and Nm_MainFunction have the same task.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="eac9cde3-4648-4d4a-b334-8df1146eae70">
                          <SHORT-NAME>CanNm_MainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/CanNm_ib_bswmd/BswModuleDescriptions/CanNm_MainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="152703de-013b-4eda-9e6c-bbdc0c4467bb">
                          <SHORT-NAME>CanNm_MainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/CanNm_ib_bswmd/BswModuleDescriptions/CanNm/CanNmBehavior/CanNm_MainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="5caffa8d-434e-4ddf-8b75-ae285c919ecf">
                  <SHORT-NAME>CanNm_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="804e7dba-6bff-4674-8bdc-059d759df5d8">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
