/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Fr
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: Fr_Cfg.h
 *   Generation Time: 2025-08-05 10:37:17
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/



#ifndef FR_CFG_H
#define FR_CFG_H

#ifndef FR_USE_DUMMY_STATEMENT
#define FR_USE_DUMMY_STATEMENT STD_OFF /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef FR_DUMMY_STATEMENT
#define FR_DUMMY_STATEMENT(v)  /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */  /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef FR_DUMMY_STATEMENT_CONST
#define FR_DUMMY_STATEMENT_CONST(v)  /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */  /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef FR_ATOMIC_BIT_ACCESS_IN_BITFIELD
#define FR_ATOMIC_BIT_ACCESS_IN_BITFIELD STD_OFF /* /MICROSAR/EcuC/EcucGeneral/AtomicBitAccessInBitfield */
#endif
#ifndef FR_ATOMIC_VARIABLE_ACCESS
#define FR_ATOMIC_VARIABLE_ACCESS 32u /* /MICROSAR/EcuC/EcucGeneral/AtomicVariableAccess */
#endif
#ifndef FR_PROCESSOR_TC377T
#define FR_PROCESSOR_TC377T
#endif
#ifndef FR_COMP_TASKING
#define FR_COMP_TASKING
#endif
#ifndef FR_GEN_GENERATOR_MSR
#define FR_GEN_GENERATOR_MSR
#endif
#ifndef FR_CPUTYPE_BITORDER_LSB2MSB
#define FR_CPUTYPE_BITORDER_LSB2MSB /* /MICROSAR/vSet/vSetPlatform/vSetBitOrder */
#endif
#ifndef FR_CONFIGURATION_VARIANT_PRECOMPILE
#define FR_CONFIGURATION_VARIANT_PRECOMPILE 1
#endif
#ifndef FR_CONFIGURATION_VARIANT_LINKTIME
#define FR_CONFIGURATION_VARIANT_LINKTIME 2
#endif
#ifndef FR_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE
#define FR_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE 3
#endif
#ifndef FR_CONFIGURATION_VARIANT
#define FR_CONFIGURATION_VARIANT FR_CONFIGURATION_VARIANT_PRECOMPILE
#endif
#ifndef FR_POSTBUILD_VARIANT_SUPPORT
#define FR_POSTBUILD_VARIANT_SUPPORT STD_OFF
#endif

#define FR_DEV_ERROR_DETECT       STD_ON
#define FR_DEV_ERROR_REPORT       STD_ON

#define FR_AUTOSARVERSION4 
#define FR_FrCtrlIdx                                   0u 
#define FR_FrAbsTimerIdx                               0u 
#define FR_FrRelTimerIdx                               0u 
#define FR_CFG_ISR_TYPE                                0u 
#define FR_CFG_APPL_CALLBACK_CYCLE_START               STD_ON 
#define FR_CFG_APPL_CALLBACK_TIMER0                    STD_ON 
#define FR_PROD_ERROR_DETECT                           STD_OFF 
#define FR_VEXTENDED_CC_STATUS                         STD_OFF 
#define FR_ABORT_COMMUNICATION_DISABLE                 STD_OFF 
#define FR_SET_EXT_SYNC_DISABLE                        STD_ON 
#define FR_SET_WAKEUP_CHANNEL_DISABLE                  STD_OFF 
#define FR_ABSOLUTE_TIMER_DISABLE                      STD_OFF 
#define FR_VENABLE_MID_FILTERING                       STD_OFF 
#define FR_VCHANNEL_STATUS_EXT                         STD_OFF 
#define FR_GET_CLOCK_CORRECTION_EXT                    STD_OFF 
#define FR_RELATIVE_TIMER_ENABLE                       STD_OFF 
#define FR_VERSION_INFO_API                            STD_OFF 
#define FR_NMVECTOR_ENABLE                             STD_OFF 
#define FR_GETSYNCFRAMELIST_API                        STD_OFF 
#define FR_RECONFIGLPDUSUPPORT                         STD_OFF 
#define FR_DISABLELPDUSUPPORT                          STD_OFF 
#define FR_CANCELTRANSMITSUPPORT                       STD_OFF 
#define FR_RX_STRINGENT_CHECK                          STD_OFF 
#define FR_RX_STRINGENT_LENGTH_CHECK                   STD_OFF 
#define FR_BUFFER_RECONFIG                             STD_OFF 
#define FR_FIFO_SUPPORT                                STD_OFF 
#define FR_INDEX                                       0u 
#define FR_NUM_CTRL_SUPPORTED                          1u 
#define FR_NUM_CTRL_USED                               1u 
#define FR_CTRL_ENABLE_API_OPTIMIZATION                STD_OFF 
#define FR_CTRL_ENABLE_ASSEMBLER_FUNCTION_OPTIMIZATION STD_OFF 
#define FR_ENABLE_BIDIRECTIONALROUTING                 STD_OFF 
#define FR_VHARDWARE_CANCELLATION                      STD_OFF 
#define FR_DFI_MULTI_USER_EXT                          STD_OFF 
#define FR_READ_CC_CONFIGURATION                       STD_OFF 
#define FR_CC_READ_BACK_SUPPORT                        STD_OFF 
#define FR_AMD_RUNTIME_MEASUREMENT                     STD_OFF 
#define FR_MULTI_ID_CONFIG                             STD_OFF 
#define FR_MIRROR_EXTENSION                            STD_OFF 
#define FR_STATUS_COUNTER_EXT                          STD_OFF 
#define FrMultipleConfiguration                        Fr_Config 
#define FR_VCC_REG_START_ADDR                          0xF001C000uL 
#define FR_HWFIFO_SUPPORT                              STD_OFF 


/* ---------------------------------------------------------------
   &&&~ Frame Triggerings - 
 --------------------------------------------------------------- */
#define FR_Trig_Tx_frame_TxDyn_95_1_8_55698a72_Tx_95_1_8_0         0x0000u 
#define FR_Trig_Tx_nm_MyECU_Fr_75d76485_Tx_90_0_64_0               0x0001u 
#define FR_Trig_Tx_frame_TxStat_16_1_4_b0a77485_Tx_16_1_4_0        0x0002u 
#define FR_Trig_Tx_frame_StartAppl_MyECU_TX_3f5a6647_Tx_8_0_1_0    0x0003u 
#define FR_Trig_Tx_Transmit_MyECU_c81f6313_Tx_2_0_1_0              0x0004u 
#define FR_Trig_Rx_frame_RxDyn_c28a5b15_Rx_103_3_4_0               0x0000u 
#define FR_Trig_Rx_nm_OtherECU_Fr_f8d387fc_Rx_90_1_64_0            0x0001u 
#define FR_Trig_Rx_nm_RearECU_Fr_bb401b65_Rx_90_2_64_0             0x0002u 
#define FR_Trig_Rx_frame_RxStat_15_0_2_c02bde7a_Rx_15_0_2_0        0x0003u 
#define FR_Trig_Rx_frame_StartAppl_BothECU_RX_852952d7_Rx_10_0_1_0 0x0004u 
#define FR_Trig_Rx_Dummy_RearECU_ad0fba75_Rx_3_0_1_0               0x0005u 


 

/* ---------------------------------------------------------------
   &&&~ Controller0 dependent defines - 
 --------------------------------------------------------------- */
#define FR_SIZE_RXVIRTBUF2PHAYBUFMAP 6uL 
#define FR_SIZE_TXVIRTBUF2PHAYBUFMAP 5uL 
#define FR_CFG_STATUS_ISR_LINE       0uL 
#define FR_SIZE_MB_ARRAY             10uL 


 
 

/* ---------------------------------------------------------------
   &&&~ Independent defines
 --------------------------------------------------------------- */
#define FR_CTRL_TEST_COUNT             1uL 
#define FR_SIZE_CONTROLREGCONFARRAY    36uL 
#define FR_DIRECT_BUFFER_ACCESS_ENABLE STD_OFF 


    





#define FR_RMC_VALUE 256

#define FR_TRICORE_AURIX
#define FR_AURIX_IRQ_CFG_SIZE                            0x0AU
#define FR_AURIX_IR_BASE                                 0xF0038000UL



#endif /* FR_CFG_H */

