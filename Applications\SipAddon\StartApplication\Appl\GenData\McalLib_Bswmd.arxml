<?xml version="1.0" encoding="UTF-8"?>

<!--
/*******************************************************************************
**                                                                            **
** Copyright (C) Infineon Technologies (2020)                                 **
**                                                                            **
** All rights reserved.                                                       **
**                                                                            **
** This document contains proprietary information belonging to Infineon       **
** Technologies. Passing on and copying of this document, and communication   **
** of its contents is not permitted without prior written authorization.      **
**                                                                            **
********************************************************************************
**                                                                            **
**  FILENAME  : McalLib_Bswmd.arxml                                           **
**                                                                            **
**  VERSION   : 1.40.0_18.0.0                                                 **
**                                                                            **
**  DATE, TIME: 2025-08-05, 10:50:57        !!!IGNORE-LINE!!!                 **
**                                                                            **
**  GENERATOR : Build b191017-0938            !!!IGNORE-LINE!!!               **
**                                                                            **
**  VARIANT   : Variant PC                                                    **
**                                                                            **
**  PLATFORM  : Infineon AURIX2G                                              **
**                                                                            **
**  AUTHOR    : DL-AUTOSAR-Engineering                                        **
**                                                                            **
**  VENDOR    : Infineon Technologies                                         **
**                                                                            **
**  DESCRIPTION  : Basic Software Module Description for MCAL Library         **
**                                                                            **
**  [/cover]                                                                  **
**                                                                            **
**  SPECIFICATION(S) : NA                                                     **
**                                                                            **
**  MAY BE CHANGED BY USER : no                                               **
**                                                                            **
*******************************************************************************/
-->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-2-2.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>AURIX2G_McalLib</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE>
          <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
          <CATEGORY>STANDARD</CATEGORY>
          <ELEMENTS>
            <BSW-MODULE-DESCRIPTION>
              <SHORT-NAME>McalLib</SHORT-NAME>
              <LONG-NAME>
                <L-4 L="EN">MCAL Library</L-4>
              </LONG-NAME>
              <CATEGORY>BSW_MODULE</CATEGORY>
              <MODULE-ID>255</MODULE-ID>
              <PROVIDED-ENTRYS>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_SetBitAtomic</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetBitAtomic</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetCpuWdgPassword</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_SetCpuWdgPassword</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WriteCpuEndInitProtReg</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetSafetyEndInitPassword</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_SetSafetyEndInitPassword</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WriteSafetyEndInitProtReg</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WriteSafetyEndInitProtReg16</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WriteSafetyEndInitProtRegMask</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetPeripheralEndInitPassword</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_SetPeripheralEndInitPassword</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WritePeripEndInitProtReg</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetCpuPhysicalId</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetGlobalDsprAddress</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetLocalDsprAddress</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetGlobalPsprAddress</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetLocalPsprAddress</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_DelayTickResolution</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_DelayResetTickCalibration</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_DelayGetTick</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetCpuIndex</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetSpinlock</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_ReleaseSpinlock</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/McalLib_GetVersionInfo</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              </PROVIDED-ENTRYS>
              <BSW-MODULE-DEPENDENCYS>
                <BSW-MODULE-DEPENDENCY>
                  <SHORT-NAME>McalSafetyErrorDependency</SHORT-NAME>
                  <TARGET-MODULE-ID>255</TARGET-MODULE-ID>
                  <REQUIRED-ENTRYS>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Stub/BswModuleEntrys/Mcal_ReportSafetyError</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  </REQUIRED-ENTRYS>
                </BSW-MODULE-DEPENDENCY>
              </BSW-MODULE-DEPENDENCYS>
              <INTERNAL-BEHAVIORS>
                <BSW-INTERNAL-BEHAVIOR>
                  <SHORT-NAME>McalLibBehavior</SHORT-NAME>
                  <EXCLUSIVE-AREAS>
                    <EXCLUSIVE-AREA>
                      <SHORT-NAME>PeripheralEndInit</SHORT-NAME>
                      <INTRODUCTION>
                        <NOTE SI="User of McalLib shall ensure that the critical section protection provided by McalLib for peripheral ENDINIT protected register is implemented to suspend or resume interrupts" NOTE-TYPE="HINT"/>
                      </INTRODUCTION>
                    </EXCLUSIVE-AREA>
                    <EXCLUSIVE-AREA>
                      <SHORT-NAME>SafetyEndInit</SHORT-NAME>
                      <INTRODUCTION>
                        <NOTE SI="User of McalLib shall ensure that the critical section protection provided by McalLib for safety ENDINIT protected register is implemented to suspend or resume interrupts" NOTE-TYPE="HINT"/>
                      </INTRODUCTION>
                    </EXCLUSIVE-AREA>
                    <EXCLUSIVE-AREA>
                      <SHORT-NAME>CpuEndInit</SHORT-NAME>
                      <INTRODUCTION>
                        <NOTE SI="User of McalLib shall ensure that the critical section protection provided by McalLib for CPU ENDINIT protected register is implemented to suspend or resume interrupts" NOTE-TYPE="HINT"/>
                      </INTRODUCTION>
                    </EXCLUSIVE-AREA>
                    <EXCLUSIVE-AREA>
                      <SHORT-NAME>StmTimerResolution</SHORT-NAME>
                      <INTRODUCTION>
                        <NOTE SI="User of McalLib shall ensure that the critical section protection provided by McalLib for StmTimerResolution is implemented to suspend or resume interrupts" NOTE-TYPE="HINT"/>
                      </INTRODUCTION>
                    </EXCLUSIVE-AREA>
                  </EXCLUSIVE-AREAS>
                  <ENTITYS>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_SetBitAtomic</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_SetBitAtomic</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_GetBitAtomic</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetBitAtomic</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_SetCpuWdgPassword</SHORT-NAME>
                      <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                        <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AURIX2G_McalLib/BswModuleDescriptions/McalLib/McalLibBehavior/CpuEndInit</CAN-ENTER-EXCLUSIVE-AREA-REF>
                      </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_SetCpuWdgPassword</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_WriteCpuEndInitProtReg</SHORT-NAME>
                      <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                        <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AURIX2G_McalLib/BswModuleDescriptions/McalLib/McalLibBehavior/CpuEndInit</CAN-ENTER-EXCLUSIVE-AREA-REF>
                      </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WriteCpuEndInitProtReg</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_SetSafetyEndInitPassword</SHORT-NAME>
                      <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                        <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AURIX2G_McalLib/BswModuleDescriptions/McalLib/McalLibBehavior/SafetyEndInit</CAN-ENTER-EXCLUSIVE-AREA-REF>
                      </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_SetSafetyEndInitPassword</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_WriteSafetyEndInitProtReg16</SHORT-NAME>
                      <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                        <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AURIX2G_McalLib/BswModuleDescriptions/McalLib/McalLibBehavior/SafetyEndInit</CAN-ENTER-EXCLUSIVE-AREA-REF>
                      </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WriteSafetyEndInitProtReg16</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_WriteSafetyEndInitProtReg</SHORT-NAME>
                      <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                        <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AURIX2G_McalLib/BswModuleDescriptions/McalLib/McalLibBehavior/SafetyEndInit</CAN-ENTER-EXCLUSIVE-AREA-REF>
                      </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WriteSafetyEndInitProtReg</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_WriteSafetyEndInitProtRegMask</SHORT-NAME>
                      <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                        <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AURIX2G_McalLib/BswModuleDescriptions/McalLib/McalLibBehavior/SafetyEndInit</CAN-ENTER-EXCLUSIVE-AREA-REF>
                      </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WriteSafetyEndInitProtRegMask</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_SetPeripheralEndInitPassword</SHORT-NAME>
                      <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                        <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AURIX2G_McalLib/BswModuleDescriptions/McalLib/McalLibBehavior/PeripheralEndInit</CAN-ENTER-EXCLUSIVE-AREA-REF>
                      </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_SetPeripheralEndInitPassword</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_WritePeripEndInitProtReg</SHORT-NAME>
                      <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                        <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AURIX2G_McalLib/BswModuleDescriptions/McalLib/McalLibBehavior/PeripheralEndInit</CAN-ENTER-EXCLUSIVE-AREA-REF>
                      </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WritePeripEndInitProtReg</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_GetCpuWdgPassword</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetCpuWdgPassword</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_GetSafetyEndInitPassword</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetSafetyEndInitPassword</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_GetPeripheralEndInitPassword</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetPeripheralEndInitPassword</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_GetCpuPhysicalId</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetCpuPhysicalId</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_DelayTickResolution</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_DelayTickResolution</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>McalLib_GetVersionInfo</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/McalLib_GetVersionInfo</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_GetGlobalDsprAddress</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetGlobalDsprAddress</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_GetLocalDsprAddress</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetLocalDsprAddress</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_GetGlobalPsprAddress</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetGlobalPsprAddress</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_GetLocalPsprAddress</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetLocalPsprAddress</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_DelayGetTick</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_DelayGetTick</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_GetCpuIndex</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetCpuIndex</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_GetSpinlock</SHORT-NAME>
                      <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_GetSpinlock</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_ReleaseSpinlock</SHORT-NAME>
                      <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_ReleaseSpinlock</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Mcal_DelayResetTickCalibration</SHORT-NAME>
                      <CAN-ENTER-EXCLUSIVE-AREA-REFS>
                        <CAN-ENTER-EXCLUSIVE-AREA-REF DEST="EXCLUSIVE-AREA">/AURIX2G_McalLib/BswModuleDescriptions/McalLib/McalLibBehavior/StmTimerResolution</CAN-ENTER-EXCLUSIVE-AREA-REF>
                      </CAN-ENTER-EXCLUSIVE-AREA-REFS>
                      <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_DelayResetTickCalibration</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                  </ENTITYS>
                </BSW-INTERNAL-BEHAVIOR>
              </INTERNAL-BEHAVIORS>
            </BSW-MODULE-DESCRIPTION>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>BswModuleEntrys</SHORT-NAME>
          <CATEGORY>STANDARD</CATEGORY>
          <ELEMENTS>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_GetCpuWdgPassword</SHORT-NAME>
              <SERVICE-ID>136</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_SetCpuWdgPassword</SHORT-NAME>
              <INTRODUCTION>
                <NOTE SI="Mcal_SetCpuWdgPassword is reentrant on other CPUs." NOTE-TYPE="HINT" />
              </INTRODUCTION>
              <SERVICE-ID>133</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>Password</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_WriteCpuEndInitProtReg</SHORT-NAME>
              <SERVICE-ID>126</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>DataValue</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>RegAddress</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/BaseTypes/void</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_GetSafetyEndInitPassword</SHORT-NAME>
              <SERVICE-ID>135</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_SetSafetyEndInitPassword</SHORT-NAME>
              <SERVICE-ID>128</SERVICE-ID>
              <IS-REENTRANT>false</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>Password</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_WriteSafetyEndInitProtReg</SHORT-NAME>
              <SERVICE-ID>127</SERVICE-ID>
              <IS-REENTRANT>false</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>DataValue</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>RegAddress</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/BaseTypes/void</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_WriteSafetyEndInitProtReg16</SHORT-NAME>
              <SERVICE-ID>129</SERVICE-ID>
              <IS-REENTRANT>false</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>DataValue</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>RegAddress</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/BaseTypes/void</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>            
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_WriteSafetyEndInitProtRegMask</SHORT-NAME>
              <SERVICE-ID>143</SERVICE-ID>
              <IS-REENTRANT>false</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>DataValue</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>Mask</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>RegAddress</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/BaseTypes/void</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_GetPeripheralEndInitPassword</SHORT-NAME>
              <SERVICE-ID>130</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_SetPeripheralEndInitPassword</SHORT-NAME>
              <SERVICE-ID>124</SERVICE-ID>
              <IS-REENTRANT>false</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>Password</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_WritePeripEndInitProtReg</SHORT-NAME>
              <SERVICE-ID>122</SERVICE-ID>
              <IS-REENTRANT>false</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>DataValue</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>RegAddress</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/BaseTypes/void</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_GetCpuPhysicalId</SHORT-NAME>
              <SERVICE-ID>139</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_GetGlobalDsprAddress</SHORT-NAME>
              <SERVICE-ID>123</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>CpuId</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>LocalDsprAddress</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_GetLocalDsprAddress</SHORT-NAME>
              <SERVICE-ID>131</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>GlobalDsprAddress</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_GetGlobalPsprAddress</SHORT-NAME>
              <SERVICE-ID>125</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>CpuId</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>LocalPsprAddress</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_GetLocalPsprAddress</SHORT-NAME>
              <SERVICE-ID>132</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>GlobalPsprAddress</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_DelayTickResolution</SHORT-NAME>
              <SERVICE-ID>140</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_DelayResetTickCalibration</SHORT-NAME>
              <SERVICE-ID>134</SERVICE-ID>
              <IS-REENTRANT>false</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_DelayGetTick</SHORT-NAME>
              <SERVICE-ID>138</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_GetCpuIndex</SHORT-NAME>
              <SERVICE-ID>137</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <RETURN-TYPE>
                <SHORT-NAME>Return</SHORT-NAME>
                <CATEGORY>TYPE_REFERENCE</CATEGORY>
                <SW-DATA-DEF-PROPS>
                  <SW-DATA-DEF-PROPS-VARIANTS>
                    <SW-DATA-DEF-PROPS-CONDITIONAL>
                      <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                    </SW-DATA-DEF-PROPS-CONDITIONAL>
                  </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
              </RETURN-TYPE>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>CpuId</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>McalLib_GetVersionInfo</SHORT-NAME>
              <SERVICE-ID>121</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>versioninfo</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Std/ImplementationDataTypes/Std_VersionInfoType</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_GetSpinlock</SHORT-NAME>
              <SERVICE-ID>141</SERVICE-ID>
              <IS-REENTRANT>false</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>Mcal_LockAddress</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_ReleaseSpinlock</SHORT-NAME>
              <SERVICE-ID>142</SERVICE-ID>
              <IS-REENTRANT>false</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>Mcal_LockAddress</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_SetBitAtomic</SHORT-NAME>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>MACRO</SW-SERVICE-IMPL-POLICY>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>DataPtr</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>BitPos</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>BitLen</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>Data</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Mcal_GetBitAtomic</SHORT-NAME>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>MACRO</SW-SERVICE-IMPL-POLICY>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>DataValue</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>BitPos</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>BitLen</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>Implementations</SHORT-NAME>
          <ELEMENTS>
            <BSW-IMPLEMENTATION>
              <SHORT-NAME>McalLib</SHORT-NAME>
              <CODE-DESCRIPTORS>
                <CODE>
                  <SHORT-NAME>Files</SHORT-NAME>
                  <ARTIFACT-DESCRIPTORS>
                    <AUTOSAR-ENGINEERING-OBJECT>
                      <SHORT-LABEL>Tricore::ssc::src::McalLib.c</SHORT-LABEL>
                      <CATEGORY>SWSRC</CATEGORY>
                    </AUTOSAR-ENGINEERING-OBJECT>
                    <AUTOSAR-ENGINEERING-OBJECT>
                      <SHORT-LABEL>Tricore::ssc::inc::McalLib.h</SHORT-LABEL>
                      <CATEGORY>SWHDR</CATEGORY>
                    </AUTOSAR-ENGINEERING-OBJECT>
                  </ARTIFACT-DESCRIPTORS>
                </CODE>
              </CODE-DESCRIPTORS>
              <GENERATED-ARTIFACTS>
                <DEPENDENCY-ON-ARTIFACT>
                  <SHORT-NAME>McalLib_Cfg_h</SHORT-NAME>
                  <ARTIFACT-DESCRIPTOR>
                    <SHORT-LABEL>generate::template::inc::McalLib_Cfg.h</SHORT-LABEL>
                    <CATEGORY>SWTEMPLATE</CATEGORY>
                  </ARTIFACT-DESCRIPTOR>
                  <USAGES>
                    <USAGE>COMPILE</USAGE>
                  </USAGES>
                </DEPENDENCY-ON-ARTIFACT>
                <DEPENDENCY-ON-ARTIFACT>
                  <SHORT-NAME>McalLib_xdm</SHORT-NAME>
                  <ARTIFACT-DESCRIPTOR>
                    <SHORT-LABEL>config::McalLib.xdm</SHORT-LABEL>
                    <CATEGORY>SWTOOL</CATEGORY>
                  </ARTIFACT-DESCRIPTOR>
                  <USAGES>
                    <USAGE>CODEGENERATION</USAGE>
                  </USAGES>
                </DEPENDENCY-ON-ARTIFACT>
                <DEPENDENCY-ON-ARTIFACT>
                  <SHORT-NAME>McalLib_bmd</SHORT-NAME>
                  <ARTIFACT-DESCRIPTOR>
                    <SHORT-LABEL>autosar::TC37x::TC377x_ED_EX::McalLib.bmd</SHORT-LABEL>
                    <CATEGORY>SWTOOL</CATEGORY>
                  </ARTIFACT-DESCRIPTOR>
                  <USAGES>
                    <USAGE>CODEGENERATION</USAGE>
                  </USAGES>
                </DEPENDENCY-ON-ARTIFACT>
              </GENERATED-ARTIFACTS>
              <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
              <RESOURCE-CONSUMPTION>
                <SHORT-NAME>ResourceConsumption</SHORT-NAME>
                <MEMORY-SECTIONS>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CONST_ASIL_B_GLOBAL_8</SHORT-NAME>
                    <ALIGNMENT>8</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONST</SW-ADDRMETHOD-REF>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>VAR_CLEARED_ASIL_B_GLOBAL_32</SHORT-NAME>
                    <ALIGNMENT>32</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/VAR_CLEARED</SW-ADDRMETHOD-REF>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CONST_ASIL_B_GLOBAL_32</SHORT-NAME>
                    <ALIGNMENT>32</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONST</SW-ADDRMETHOD-REF>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CODE_ASIL_B_GLOBAL</SHORT-NAME>
                    <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CODE</SW-ADDRMETHOD-REF>
                  </MEMORY-SECTION>
                </MEMORY-SECTIONS>
                <SECTION-NAME-PREFIXS>
                  <SECTION-NAME-PREFIX>
                    <SHORT-NAME>MCALLIB</SHORT-NAME>
                    <SYMBOL>MCALLIB</SYMBOL>
                  </SECTION-NAME-PREFIX>
                </SECTION-NAME-PREFIXS>
              </RESOURCE-CONSUMPTION>
              <SW-VERSION>10.40.1</SW-VERSION>
              <VENDOR-ID>17</VENDOR-ID>
              <AR-RELEASE-VERSION>4.2.2</AR-RELEASE-VERSION>
              <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/AURIX2G_McalLib/BswModuleDescriptions/McalLib/McalLibBehavior</BEHAVIOR-REF>
              <VENDOR-SPECIFIC-MODULE-DEF-REFS>
                <VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/AURIX2G/EcucDefs/McalLib</VENDOR-SPECIFIC-MODULE-DEF-REF>
              </VENDOR-SPECIFIC-MODULE-DEF-REFS>
            </BSW-IMPLEMENTATION>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>ImplementationDataTypes</SHORT-NAME>
          <CATEGORY>STANDARD</CATEGORY>
          <ELEMENTS>
            <IMPLEMENTATION-DATA-TYPE>
              <SHORT-NAME>unsigned_int</SHORT-NAME>
              <CATEGORY>VALUE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <BASE-TYPE-REF DEST="SW-BASE-TYPE">/AURIX2G_McalLib/ImplementationDataTypes/BaseTypes/UnsignedInt</BASE-TYPE-REF>
                    <DATA-CONSTR-REF DEST="DATA-CONSTR">/AURIX2G_McalLib/ImplementationDataTypes/DataConstrs_compu/unsignedInt_constr</DATA-CONSTR-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
              <TYPE-EMITTER>BSW</TYPE-EMITTER>
            </IMPLEMENTATION-DATA-TYPE>
          </ELEMENTS>
          <AR-PACKAGES>
            <AR-PACKAGE>
              <SHORT-NAME>BaseTypes</SHORT-NAME>
              <ELEMENTS>
                <SW-BASE-TYPE>
                  <SHORT-NAME>UnsignedInt</SHORT-NAME>
                  <CATEGORY>FIXED_LENGTH</CATEGORY>
                  <BASE-TYPE-SIZE>32</BASE-TYPE-SIZE>
                  <BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
                  <BYTE-ORDER>MOST-SIGNIFICANT-BYTE-LAST</BYTE-ORDER>
                  <NATIVE-DECLARATION>unsigned int</NATIVE-DECLARATION>
                </SW-BASE-TYPE>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE>
              <SHORT-NAME>DataConstrs_compu</SHORT-NAME>
              <CATEGORY>STANDARD</CATEGORY>
              <ELEMENTS>
                <DATA-CONSTR>
                  <SHORT-NAME>unsignedInt_constr</SHORT-NAME>
                  <DATA-CONSTR-RULES>
                    <DATA-CONSTR-RULE>
                      <INTERNAL-CONSTRS>
                        <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                        <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4294967295</UPPER-LIMIT>
                      </INTERNAL-CONSTRS>
                    </DATA-CONSTR-RULE>
                  </DATA-CONSTR-RULES>
                </DATA-CONSTR>
              </ELEMENTS>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
