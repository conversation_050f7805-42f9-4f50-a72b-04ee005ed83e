/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: FrSM
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: FrSM_Lcfg.c
 *   Generation Time: 2025-08-05 10:37:18
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/






#define FRSM_LCFG_C

/* -----------------------------------------------------------------------------
    Includes
 ----------------------------------------------------------------------------- */

#include "FrSM.h"

/* -----------------------------------------------------------------------------
    Function Prototypes
 ----------------------------------------------------------------------------- */
#define FRSM_START_SEC_CODE
#include "MemMap.h"  /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern FUNC( void, FRSM_APPL_CODE ) ApplFr_OutOfSync( CONST (NetworkHandleType, AUTOMATIC) NetworkHandle );

#define FRSM_STOP_SEC_CODE
#include "MemMap.h"  /* PRQA S 5087 */ /* MD_MSR_MemMap */




/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL DATA
**********************************************************************************************************************/
/**********************************************************************************************************************
  FrSM_ClusterConfig
**********************************************************************************************************************/
/** 
  \var    FrSM_ClusterConfig
  \brief  root config struct
  \details
  Element                         Description
  ColdstartEcu                
  WakeupEcu                   
  ControllerIndex             
  DurationT1                  
  DurationT2                  
  DurationT3                  
  DurationT4                  
  FrIfClusterIndex            
  NetworkHandle               
  NumWakeupPatterns           
  StartupRepetitions          
  StartupRepetitionsWithWakeup
  TrcvStbyDelay               
  FrChannel                   
*/ 
#define FRSM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(FrSM_ClusterConfigType, FRSM_CONST) FrSM_ClusterConfig[1] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    ColdstartEcu  WakeupEcu  ControllerIndex  DurationT1  DurationT2  DurationT3  DurationT4  FrIfClusterIndex  NetworkHandle  NumWakeupPatterns  StartupRepetitions  StartupRepetitionsWithWakeup  TrcvStbyDelay  FrChannel    */
  { /*     0 */         TRUE,      TRUE,              0u,         1u,        10u,       100u,         0u,               0u,            2u,                1u,                 0u,                           0u,            0u, FR_CHANNEL_A }
};
#define FRSM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrSM_VarStruct
**********************************************************************************************************************/
/** 
  \var    FrSM_VarStruct
  \details
  Element                    Description
  InternalState          
  Startup_Counter        
  Timer_ColdstartDelay   
  Timer_RetryStartUp     
  Timer_StartUpMonitoring
  WakeUpPatternCounter   
  WakeUpType             
  RequestedComMode       
*/ 
#define FRSM_START_SEC_VAR_NOINIT_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrSM_VarStructType, FRSM_VAR_NOINIT) FrSM_VarStruct[1];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define FRSM_STOP_SEC_VAR_NOINIT_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */



/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: LOCAL DATA
**********************************************************************************************************************/


/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL FUNCTIONS
**********************************************************************************************************************/




/* START of Checksum include for - Ccl_AsrSmFrLinktimeCRC */

#define FRSM_START_SEC_CONST_UNSPECIFIED
#include "MemMap.h"  /* PRQA S 5087 */ /* MD_MSR_MemMap */

CONST(FrSMSyncLossErrorIndicationFctPtrType, FRSM_CONST) FrSMSyncLossErrorIndicationFctPtr = ApplFr_OutOfSync;  /* PRQA S 1533 */ /* MD_FRSM_Rule8.9 */





#define FRSM_STOP_SEC_CONST_UNSPECIFIED
#include "MemMap.h"  /* PRQA S 5087 */ /* MD_MSR_MemMap */


/* -----------------------------------------------------------------------------
    FrSM Functions
 ----------------------------------------------------------------------------- */






#define FRSM_START_SEC_CODE
#include "MemMap.h"  /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*!
 * \internal
 * - #10 Call the internal main function with the internal cluster ID which belonges to the FrIf cluster ID of the indexed main function
 * \endinternal
 */
FUNC(void, FRSM_CODE) FrSM_MainFunction_0(void)
{
  FrSM_MainFunction( 0 );
}



#define FRSM_STOP_SEC_CODE
#include "MemMap.h"  /* PRQA S 5087 */ /* MD_MSR_MemMap */


/* END of Checksum include for - Ccl_AsrSmFrLinktimeCRC */




