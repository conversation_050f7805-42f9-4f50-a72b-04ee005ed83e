<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <ELEMENTS>
        <BSW-IMPLEMENTATION UUID="489c368f-1652-4e5d-8e61-f065cddaadaa">
          <SHORT-NAME>LinSM_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>7.00.02</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
        </BSW-IMPLEMENTATION>
      </ELEMENTS>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="3a4b8e7a-4766-4472-bb06-0e357bd8270c">
          <SHORT-NAME>LinSM_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="663e8737-c7f4-4bcd-90b3-5d8387838f0b">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="d703947c-db16-43b9-8a0d-416a84e627a2">
                  <SHORT-NAME>LinSM</SHORT-NAME>
                  <PROVIDED-ENTRYS>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/LinSM_ib_bswmd/BswModuleDescriptions/LinSM_MainFunction</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  </PROVIDED-ENTRYS>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="7442df7e-9c75-4cec-a181-02c21b9aa0a2">
                      <SHORT-NAME>LinSMBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="d6863578-b646-4d0a-933b-fa369d9a903a">
                          <SHORT-NAME>LINSM_EXCLUSIVE_AREA_0</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Must lock task interrupts, if LinIf_MainFunction(), LinSM_MainFunction() or BswM action BswMLinScheduleSwitch priority level is higher than the level of ComM_MainFunction().</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="262b922a-b1af-419b-8b61-2f9d6a00dc9e">
                          <SHORT-NAME>LINSM_EXCLUSIVE_AREA_1</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Must lock task interrupts, if ComM_MainFunction(), LinSM_MainFunction() or BswM action BswMLinScheduleSwitch priority level is higher than the level of LinIf_MainFunction().</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="90e02364-1b6d-43b1-ad19-d35fdeb5967d">
                          <SHORT-NAME>LINSM_EXCLUSIVE_AREA_2</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Must lock task interrupts, if ComM_MainFunction(), LinIf_MainFunction() or BswM action BswMLinScheduleSwitch priority level is higher than the level of LinSM_MainFunction().</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="06ef6e49-115b-4a93-b45e-1cbf06b57d76">
                          <SHORT-NAME>LINSM_EXCLUSIVE_AREA_3</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Must lock task interrupts, if ComM_MainFunction(), LinIf_MainFunction() or LinSM_MainFunction() interrupt priority level is higher than the level of the BswM action BswMLinScheduleSwitch.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="3ede8bf4-b575-4751-99c1-46c5ea32da88">
                          <SHORT-NAME>LINSM_EXCLUSIVE_AREA_4</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Must lock task interrupts if the function LinSM_GotoSleepIndication() may be interrupted by any of the functions LinSM_MainFunction(), LinSM_RequestComMode(), LinSM_WakeupConfirmation() or LinSM_GotoSleepConfirmation().</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="*************-4c5a-827e-8898532eab6a">
                          <SHORT-NAME>LinSM_MainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/LinSM_ib_bswmd/BswModuleDescriptions/LinSM_MainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="4bac8bd4-0a63-4fd3-9a87-f92acc6a399d">
                          <SHORT-NAME>LinSM_MainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/LinSM_ib_bswmd/BswModuleDescriptions/LinSM/LinSMBehavior/LinSM_MainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="77e9e1db-7095-4900-abcf-4528eb19e9fa">
                  <SHORT-NAME>LinSM_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="3117ef14-d0c6-4b99-89dd-8f47590255d2">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
