<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <ELEMENTS>
        <BSW-IMPLEMENTATION UUID="3caf2919-5d52-423f-9faf-7b78a8fcdb88">
          <SHORT-NAME>CanIf_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>6.23.00</SW-VERSION>
          <USED-CODE-GENERATOR>cMSR</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
        </BSW-IMPLEMENTATION>
      </ELEMENTS>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="ca4fabd2-6a3a-4516-893e-1e5eee1008e2">
          <SHORT-NAME>CanIf_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="7eeb0321-1b82-4a62-9c69-f310b2002820">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="d878d989-e864-4ee2-99d7-03e42476a454">
                  <SHORT-NAME>CanIf</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="c9236a53-3e15-4bfb-a96c-d39702ade1f4">
                      <SHORT-NAME>CanIf_Behavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="bf895ee1-7ec4-42fe-9c58-2766a5bc310c">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="a19ed7ae-122f-4d80-aa90-ade63842949e">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_1</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="0b0d6626-584d-43c1-a8e7-5a3c56299f13">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_2</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="580fd4d9-aae5-477e-908f-68c25c479e07">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_3</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="2cf62713-23f4-4f16-a40a-a4ce5a52114a">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_4</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="e1c79637-c5d7-4d51-9600-5a53ed7fd0b2">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_5</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="a1359e97-3a5a-43c7-8718-638259ca7828">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_6</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="c27eb86d-f7be-4172-be5a-9aa563ef680b">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_7</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="13e96995-d4a5-4286-accc-c5a1ae3dc96c">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
