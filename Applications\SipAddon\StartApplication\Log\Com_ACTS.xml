<?xml version="1.0" encoding="UTF-8" standalone="no"?><System name="Com"><Parameters><Parameter id="1" name="COM_CONFIGURATION_VARIANT" type="1"><values><value>COM_CONFIGURATION_VARIANT_PRECOMPILE</value><value>COM_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE</value></values></Parameter><Parameter id="2" name="COM_USE_INIT_POINTER" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="3" name="COM_CHECK_INIT_POINTER" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="4" name="COM_FINAL_MAGIC_NUMBER" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="5" name="COM_USE_ECUM_BSW_ERROR_HOOK" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="6" name="COM_ACTIVATABLERXCOMIPDUS" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="7" name="COM_RXPDUINFOIDXOFACTIVATABLERXCOMIPDUS" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="8" name="COM_ACTIVATABLETXCOMIPDUS" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="9" name="COM_TXPDUINFOIDXOFACTIVATABLETXCOMIPDUS" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="10" name="COM_ALWAYSACTIVERXCOMIPDUS" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="11" name="COM_RXPDUINFOIDXOFALWAYSACTIVERXCOMIPDUS" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="12" name="COM_ALWAYSACTIVETXCOMIPDUS" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="13" name="COM_TXPDUINFOIDXOFALWAYSACTIVETXCOMIPDUS" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="14" name="COM_DEFERREDGWMAPPINGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="15" name="COM_RXPDUINFOIDXOFDEFERREDGWMAPPINGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="16" name="COM_FILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="17" name="COM_FILTERALGOOFFILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="18" name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="19" name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="20" name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="21" name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="22" name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="23" name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="24" name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="25" name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="26" name="COM_FILTERMASKOFFILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="27" name="COM_FILTERMAXOFFILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="28" name="COM_FILTERMINOFFILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="29" name="COM_FILTERXOFFILTERINFO_FLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="30" name="COM_FILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="31" name="COM_FILTERALGOOFFILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="32" name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="33" name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="34" name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="35" name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="36" name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="37" name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="38" name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="39" name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="40" name="COM_FILTERMASKOFFILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="41" name="COM_FILTERMAXOFFILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="42" name="COM_FILTERMINOFFILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="43" name="COM_FILTERXOFFILTERINFO_FLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="44" name="COM_FILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="45" name="COM_FILTERALGOOFFILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="46" name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="47" name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="48" name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="49" name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="50" name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="51" name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="52" name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="53" name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="54" name="COM_FILTERMASKOFFILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="55" name="COM_FILTERMAXOFFILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="56" name="COM_FILTERMINOFFILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="57" name="COM_FILTERXOFFILTERINFO_SINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="58" name="COM_FILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="59" name="COM_FILTERALGOOFFILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="60" name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="61" name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="62" name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="63" name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="64" name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="65" name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="66" name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="67" name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="68" name="COM_FILTERMASKOFFILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="69" name="COM_FILTERMAXOFFILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="70" name="COM_FILTERMINOFFILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="71" name="COM_FILTERXOFFILTERINFO_SINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="72" name="COM_FILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="73" name="COM_FILTERALGOOFFILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="74" name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="75" name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="76" name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="77" name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="78" name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="79" name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="80" name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="81" name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="82" name="COM_FILTERMASKOFFILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="83" name="COM_FILTERMAXOFFILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="84" name="COM_FILTERMINOFFILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="85" name="COM_FILTERXOFFILTERINFO_SINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="86" name="COM_FILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="87" name="COM_FILTERALGOOFFILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="88" name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="89" name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="90" name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="91" name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="92" name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="93" name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="94" name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="95" name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="96" name="COM_FILTERMASKOFFILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="97" name="COM_FILTERMAXOFFILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="98" name="COM_FILTERMINOFFILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="99" name="COM_FILTERXOFFILTERINFO_SINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="100" name="COM_FILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="101" name="COM_FILTERALGOOFFILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="102" name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="103" name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="104" name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="105" name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="106" name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="107" name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="108" name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="109" name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="110" name="COM_FILTERMASKOFFILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="111" name="COM_FILTERMAXOFFILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="112" name="COM_FILTERMINOFFILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="113" name="COM_FILTERXOFFILTERINFO_UINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="114" name="COM_FILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="115" name="COM_FILTERALGOOFFILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="116" name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="117" name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="118" name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="119" name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="120" name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="121" name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="122" name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="123" name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="124" name="COM_FILTERMASKOFFILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="125" name="COM_FILTERMAXOFFILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="126" name="COM_FILTERMINOFFILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="127" name="COM_FILTERXOFFILTERINFO_UINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="128" name="COM_FILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="129" name="COM_FILTERALGOOFFILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="130" name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="131" name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="132" name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="133" name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="134" name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="135" name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="136" name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="137" name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="138" name="COM_FILTERMASKOFFILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="139" name="COM_FILTERMAXOFFILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="140" name="COM_FILTERMINOFFILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="141" name="COM_FILTERXOFFILTERINFO_UINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="142" name="COM_FILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="143" name="COM_FILTERALGOOFFILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="144" name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="145" name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="146" name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="147" name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="148" name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="149" name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="150" name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="151" name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="152" name="COM_FILTERMASKOFFILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="153" name="COM_FILTERMAXOFFILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="154" name="COM_FILTERMINOFFILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="155" name="COM_FILTERXOFFILTERINFO_UINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="156" name="COM_FILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="157" name="COM_FILTERALGOOFFILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="158" name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="159" name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="160" name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="161" name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="162" name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="163" name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="164" name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="165" name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="166" name="COM_FILTERMASKOFFILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="167" name="COM_FILTERMAXOFFILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="168" name="COM_FILTERMINOFFILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="169" name="COM_FILTERXOFFILTERINFO_UINT8_N" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="170" name="COM_GWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="171" name="COM_BITOFFSETOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="172" name="COM_BITSIZEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="173" name="COM_COPYTYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="174" name="COM_EXISTS_DIRECT_COPYTYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="175" name="COM_EXISTS_RIGHTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="176" name="COM_EXISTS_LEFTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="177" name="COM_ENDIANNESSOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="178" name="COM_EXISTS_BIG_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="179" name="COM_EXISTS_LITTLE_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="180" name="COM_GWSOURCESTARTBITINDEXOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="181" name="COM_RXUBIDXOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="182" name="COM_RXUBMASKIDXOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="183" name="COM_RXUBMASKUSEDOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="184" name="COM_SOURCEDESCRIPTIONMASKENDMASKIDXOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="185" name="COM_SOURCEDESCRIPTIONMASKENDMASKUSEDOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="186" name="COM_SOURCEDESCRIPTIONMASKIDXOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="187" name="COM_SOURCESTARTBYTEPOSITIONOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="188" name="COM_TXBUFFERENDIDXOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="189" name="COM_TXBUFFERLENGTHOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="190" name="COM_TXBUFFERSTARTIDXOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="191" name="COM_TXBUFFERUBIDXOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="192" name="COM_TXBUFFERUBUSEDOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="193" name="COM_TXPDUINFOIDXOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="194" name="COM_TXUBMASKIDXOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="195" name="COM_TXUBMASKUSEDOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="196" name="COM_TYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="197" name="COM_EXISTS_IMMEDIATE_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="198" name="COM_EXISTS_IMMEDIATE_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="199" name="COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="200" name="COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="201" name="COM_EXISTS_IMMEDIATE_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="202" name="COM_EXISTS_DEFERRED_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="203" name="COM_EXISTS_DEFERRED_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="204" name="COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="205" name="COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="206" name="COM_EXISTS_DEFERRED_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="207" name="COM_VALIDDLCOFGWDESCRIPTIONACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="208" name="COM_GWGRPSIGMAPPING" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="209" name="COM_RXACCESSINFOIDXOFGWGRPSIGMAPPING" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="210" name="COM_TXSIGIDOFGWGRPSIGMAPPING" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="211" name="COM_GWINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="212" name="COM_GWSIGGRPMAPPINGENDIDXOFGWINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="213" name="COM_GWSIGGRPMAPPINGSTARTIDXOFGWINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="214" name="COM_GWSIGGRPMAPPINGUSEDOFGWINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="215" name="COM_GWSIGMAPPINGENDIDXOFGWINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="216" name="COM_GWSIGMAPPINGSTARTIDXOFGWINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="217" name="COM_GWSIGMAPPINGUSEDOFGWINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="218" name="COM_GWSIGGRPMAPPING" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="219" name="COM_GWGRPSIGMAPPINGENDIDXOFGWSIGGRPMAPPING" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="220" name="COM_GWGRPSIGMAPPINGSTARTIDXOFGWSIGGRPMAPPING" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="221" name="COM_TXSIGGRPIDOFGWSIGGRPMAPPING" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="222" name="COM_GWSIGMAPPING" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="223" name="COM_RXACCESSINFOIDXOFGWSIGMAPPING" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="224" name="COM_TXSIGIDOFGWSIGMAPPING" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="225" name="COM_GWTIMEOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="226" name="COM_GWROUTINGTIMEOUTFACTOROFGWTIMEOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="227" name="COM_TXPDUINFOIDXOFGWTIMEOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="228" name="COM_GWTXPDUDESCRIPTIONINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="229" name="COM_GWDESCRIPTIONACCESSINFOENDIDXOFGWTXPDUDESCRIPTIONINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="230" name="COM_GWDESCRIPTIONACCESSINFOSTARTIDXOFGWTXPDUDESCRIPTIONINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="231" name="COM_IPDUGROUPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="232" name="COM_IPDUGROUPINFOTOSUBIPDUGROUPSINDENDIDXOFIPDUGROUPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="233" name="COM_IPDUGROUPINFOTOSUBIPDUGROUPSINDSTARTIDXOFIPDUGROUPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="234" name="COM_IPDUGROUPINFOTOSUBIPDUGROUPSINDUSEDOFIPDUGROUPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="235" name="COM_INVALIDHNDOFIPDUGROUPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="236" name="COM_RXPDUINFOINDENDIDXOFIPDUGROUPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="237" name="COM_RXPDUINFOINDSTARTIDXOFIPDUGROUPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="238" name="COM_RXPDUINFOINDUSEDOFIPDUGROUPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="239" name="COM_TXPDUINFOINDENDIDXOFIPDUGROUPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="240" name="COM_TXPDUINFOINDSTARTIDXOFIPDUGROUPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="241" name="COM_TXPDUINFOINDUSEDOFIPDUGROUPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="242" name="COM_RXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="243" name="COM_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="244" name="COM_EXISTS_UINT8_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="245" name="COM_EXISTS_SINT8_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="246" name="COM_EXISTS_UINT16_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="247" name="COM_EXISTS_SINT16_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="248" name="COM_EXISTS_UINT32_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="249" name="COM_EXISTS_SINT32_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="250" name="COM_EXISTS_UINT64_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="251" name="COM_EXISTS_SINT64_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="252" name="COM_EXISTS_UINT8_N_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="253" name="COM_EXISTS_UINT8_DYN_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="254" name="COM_EXISTS_ZEROBIT_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="255" name="COM_EXISTS_FLOAT32_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="256" name="COM_EXISTS_FLOAT64_APPLTYPEOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="257" name="COM_BITLENGTHOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="258" name="COM_BITPOSITIONOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="259" name="COM_BUFFERIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="260" name="COM_BUFFERUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="261" name="COM_BUSACCOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="262" name="COM_EXISTS_NBIT_BUSACCOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="263" name="COM_EXISTS_BYTE_BUSACCOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="264" name="COM_EXISTS_NBYTE_BUSACCOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="265" name="COM_EXISTS_NBYTE_SW_BUSACCOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="266" name="COM_EXISTS_NBITNBYTE_BUSACCOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="267" name="COM_EXISTS_NBITNBYTE_SW_BUSACCOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="268" name="COM_EXISTS_ARRAY_BASED_BUSACCOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="269" name="COM_BYTELENGTHOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="270" name="COM_BYTEPOSITIONOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="271" name="COM_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="272" name="COM_CONSTVALUEARRAYBASEDINITVALUELENGTHOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="273" name="COM_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="274" name="COM_CONSTVALUEARRAYBASEDINITVALUEUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="275" name="COM_CONSTVALUEARRAYBASEDINVVALUEENDIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="276" name="COM_CONSTVALUEARRAYBASEDINVVALUELENGTHOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="277" name="COM_CONSTVALUEARRAYBASEDINVVALUESTARTIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="278" name="COM_CONSTVALUEARRAYBASEDINVVALUEUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="279" name="COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUEENDIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="280" name="COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUELENGTHOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="281" name="COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUESTARTIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="282" name="COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUEUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="283" name="COM_FILTERINFOIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="284" name="COM_FILTERINFOUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="285" name="COM_GWINFOIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="286" name="COM_GWINFOUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="287" name="COM_INITVALUEIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="288" name="COM_INITVALUEUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="289" name="COM_INVVALUEIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="290" name="COM_INVVALUEUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="291" name="COM_INVALIDHNDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="292" name="COM_ISGROUPSIGNALOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="293" name="COM_ROUTINGBUFFERIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="294" name="COM_ROUTINGBUFFERUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="295" name="COM_RXDATATIMEOUTSUBSTITUTIONVALUEIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="296" name="COM_RXDATATIMEOUTSUBSTITUTIONVALUEUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="297" name="COM_RXPDUINFOIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="298" name="COM_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="299" name="COM_RXSIGBUFFERARRAYBASEDBUFFERLENGTHOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="300" name="COM_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="301" name="COM_RXSIGBUFFERARRAYBASEDBUFFERUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="302" name="COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERENDIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="303" name="COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERLENGTHOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="304" name="COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERSTARTIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="305" name="COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="306" name="COM_RXSIGBUFFERARRAYBASEDSHDBUFFERENDIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="307" name="COM_RXSIGBUFFERARRAYBASEDSHDBUFFERLENGTHOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="308" name="COM_RXSIGBUFFERARRAYBASEDSHDBUFFERSTARTIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="309" name="COM_RXSIGBUFFERARRAYBASEDSHDBUFFERUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="310" name="COM_RXTOUTINFOIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="311" name="COM_RXTOUTINFOUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="312" name="COM_SHDBUFFERIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="313" name="COM_SHDBUFFERUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="314" name="COM_SIGNEXTREQUIREDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="315" name="COM_STARTBYTEINPDUPOSITIONOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="316" name="COM_TMPBUFFERIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="317" name="COM_TMPBUFFERUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="318" name="COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERENDIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="319" name="COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERLENGTHOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="320" name="COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERSTARTIDXOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="321" name="COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERUSEDOFRXACCESSINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="322" name="COM_RXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="323" name="COM_DEFERREDGWMAPPINGINFOIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="324" name="COM_DEFERREDGWMAPPINGINFOUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="325" name="COM_GWINFOENDIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="326" name="COM_GWINFOSTARTIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="327" name="COM_GWINFOUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="328" name="COM_GWTXPDUDESCRIPTIONINFOENDIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="329" name="COM_GWTXPDUDESCRIPTIONINFOSTARTIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="330" name="COM_GWTXPDUDESCRIPTIONINFOUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="331" name="COM_HANDLERXDEFERREDGWDESCRIPTIONIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="332" name="COM_HANDLERXDEFERREDGWDESCRIPTIONUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="333" name="COM_HANDLERXPDUDEFERREDIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="334" name="COM_HANDLERXPDUDEFERREDUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="335" name="COM_IPDUGROUPINFOOFRXPDUINFOINDENDIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="336" name="COM_IPDUGROUPINFOOFRXPDUINFOINDSTARTIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="337" name="COM_IPDUGROUPINFOOFRXPDUINFOINDUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="338" name="COM_INVALIDHNDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="339" name="COM_METADATALENGTHOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="340" name="COM_PDUGRPVECTORENDIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="341" name="COM_PDUGRPVECTORSTARTIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="342" name="COM_PDUGRPVECTORUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="343" name="COM_RXACCESSINFOINDENDIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="344" name="COM_RXACCESSINFOINDSTARTIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="345" name="COM_RXACCESSINFOINDUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="346" name="COM_RXDEFPDUBUFFERENDIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="347" name="COM_RXDEFPDUBUFFERLENGTHOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="348" name="COM_RXDEFPDUBUFFERSTARTIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="349" name="COM_RXDEFPDUBUFFERUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="350" name="COM_RXPDUCALLOUTFUNCPTRIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="351" name="COM_RXPDUCALLOUTFUNCPTRUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="352" name="COM_RXSIGGRPINFOINDENDIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="353" name="COM_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="354" name="COM_RXSIGGRPINFOINDUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="355" name="COM_RXSIGINFOENDIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="356" name="COM_RXSIGINFOSTARTIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="357" name="COM_RXSIGINFOUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="358" name="COM_RXTOUTINFOIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="359" name="COM_RXTOUTINFOINDENDIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="360" name="COM_RXTOUTINFOINDSTARTIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="361" name="COM_RXTOUTINFOINDUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="362" name="COM_RXTOUTINFOUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="363" name="COM_RXTPINFOIDXOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="364" name="COM_RXTPINFOUSEDOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="365" name="COM_SIGNALPROCESSINGOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="366" name="COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="367" name="COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="368" name="COM_TYPEOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="369" name="COM_EXISTS_NORMAL_TYPEOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="370" name="COM_EXISTS_TP_TYPEOFRXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="371" name="COM_RXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="372" name="COM_ARRAYACCESSUSEDOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="373" name="COM_CONSTVALUESIGGRPARRAYACCESSENDIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="374" name="COM_CONSTVALUESIGGRPARRAYACCESSLENGTHOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="375" name="COM_CONSTVALUESIGGRPARRAYACCESSSTARTIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="376" name="COM_CONSTVALUESIGGRPARRAYACCESSUSEDOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="377" name="COM_CONSTVALUEUINT8ENDIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="378" name="COM_CONSTVALUEUINT8LENGTHOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="379" name="COM_CONSTVALUEUINT8STARTIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="380" name="COM_CONSTVALUEUINT8USEDOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="381" name="COM_FILTEREVENTOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="382" name="COM_GWINFOIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="383" name="COM_GWINFOUSEDOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="384" name="COM_INVEVENTOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="385" name="COM_INVALIDHNDOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="386" name="COM_RXACCESSINFOGRPSIGINDENDIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="387" name="COM_RXACCESSINFOGRPSIGINDSTARTIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="388" name="COM_RXACCESSINFOGRPSIGINDUSEDOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="389" name="COM_RXCBKFUNCPTRACKIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="390" name="COM_RXCBKFUNCPTRACKUSEDOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="391" name="COM_RXCBKFUNCPTRINVACKIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="392" name="COM_RXCBKFUNCPTRINVACKUSEDOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="393" name="COM_RXPDUINFOIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="394" name="COM_RXSIGARRAYACCESSSIGGRPBUFFERENDIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="395" name="COM_RXSIGARRAYACCESSSIGGRPBUFFERLENGTHOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="396" name="COM_RXSIGARRAYACCESSSIGGRPBUFFERSTARTIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="397" name="COM_RXSIGARRAYACCESSSIGGRPBUFFERUSEDOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="398" name="COM_RXSIGBUFFERUINT8ENDIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="399" name="COM_RXSIGBUFFERUINT8LENGTHOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="400" name="COM_RXSIGBUFFERUINT8STARTIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="401" name="COM_RXSIGBUFFERUINT8USEDOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="402" name="COM_RXTOUTINFOIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="403" name="COM_RXTOUTINFOUSEDOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="404" name="COM_SHDBUFFERREQUIREDOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="405" name="COM_SIGNALPROCESSINGOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="406" name="COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="407" name="COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="408" name="COM_STARTBYTEPOSITIONOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="409" name="COM_UBIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="410" name="COM_UBMASKIDXOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="411" name="COM_UBMASKUSEDOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="412" name="COM_VALIDDLCOFRXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="413" name="COM_RXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="414" name="COM_GWINFOIDXOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="415" name="COM_GWINFOUSEDOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="416" name="COM_RXACCESSINFOIDXOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="417" name="COM_RXCBKFUNCPTRACKIDXOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="418" name="COM_RXCBKFUNCPTRACKUSEDOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="419" name="COM_RXCBKFUNCPTRINVACKIDXOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="420" name="COM_RXCBKFUNCPTRINVACKUSEDOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="421" name="COM_RXTOUTINFOIDXOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="422" name="COM_RXTOUTINFOUSEDOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="423" name="COM_SIGNALPROCESSINGOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="424" name="COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="425" name="COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="426" name="COM_UBIDXOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="427" name="COM_UBMASKIDXOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="428" name="COM_UBMASKUSEDOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="429" name="COM_VALIDDLCOFRXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="430" name="COM_RXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="431" name="COM_CBKRXTOUTFUNCPTRINDENDIDXOFRXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="432" name="COM_CBKRXTOUTFUNCPTRINDSTARTIDXOFRXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="433" name="COM_CBKRXTOUTFUNCPTRINDUSEDOFRXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="434" name="COM_FACTOROFRXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="435" name="COM_FIRSTFACTOROFRXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="436" name="COM_RXACCESSINFOREPLACEGRPSIGINDENDIDXOFRXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="437" name="COM_RXACCESSINFOREPLACEGRPSIGINDSTARTIDXOFRXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="438" name="COM_RXACCESSINFOREPLACEGRPSIGINDUSEDOFRXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="439" name="COM_RXACCESSINFOREPLACESIGINDENDIDXOFRXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="440" name="COM_RXACCESSINFOREPLACESIGINDSTARTIDXOFRXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="441" name="COM_RXACCESSINFOREPLACESIGINDUSEDOFRXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="442" name="COM_RXPDUINFOIDXOFRXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="443" name="COM_RXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="444" name="COM_BUFFERSIZEOFRXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="445" name="COM_DYNAMICINITIALLENGTHOFRXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="446" name="COM_RXACCESSINFODYNSIGIDXOFRXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="447" name="COM_RXACCESSINFODYNSIGUSEDOFRXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="448" name="COM_RXTPBUFFERENDIDXOFRXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="449" name="COM_RXTPBUFFERMETADATAENDIDXOFRXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="450" name="COM_RXTPBUFFERMETADATALENGTHOFRXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="451" name="COM_RXTPBUFFERMETADATASTARTIDXOFRXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="452" name="COM_RXTPBUFFERMETADATAUSEDOFRXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="453" name="COM_RXTPBUFFERSTARTIDXOFRXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="454" name="COM_RXTPBUFFERUSEDOFRXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="455" name="COM_SIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="456" name="COM_CONSTVALUEUINT8FILTERMASKENDIDXOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="457" name="COM_CONSTVALUEUINT8FILTERMASKLENGTHOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="458" name="COM_CONSTVALUEUINT8FILTERMASKSTARTIDXOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="459" name="COM_CONSTVALUEUINT8FILTERMASKUSEDOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="460" name="COM_CONSTVALUEUINT8FILTERVALUEXENDIDXOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="461" name="COM_CONSTVALUEUINT8FILTERVALUEXLENGTHOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="462" name="COM_CONSTVALUEUINT8FILTERVALUEXSTARTIDXOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="463" name="COM_CONSTVALUEUINT8FILTERVALUEXUSEDOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="464" name="COM_FILTERALGOOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="465" name="COM_EXISTS_ALWAYS_FILTERALGOOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="466" name="COM_EXISTS_NEVER_FILTERALGOOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="467" name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="468" name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="469" name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="470" name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="471" name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="472" name="COM_EXISTS_NONE_FILTERALGOOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="473" name="COM_OFFSETINSIGNALGROUPOFSIGGRPARRAYFILTERINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="474" name="COM_TXCYCLICPDU" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="475" name="COM_TXPDUINFOIDXOFTXCYCLICPDU" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="476" name="COM_TXMODEFALSE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="477" name="COM_DIRECTOFTXMODEFALSE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="478" name="COM_PERIODICOFTXMODEFALSE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="479" name="COM_REPCNTOFTXMODEFALSE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="480" name="COM_REPPERIODOFTXMODEFALSE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="481" name="COM_TIMEOFFSETOFTXMODEFALSE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="482" name="COM_TIMEPERIODOFTXMODEFALSE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="483" name="COM_TXMODEINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="484" name="COM_INITMODEOFTXMODEINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="485" name="COM_INVALIDHNDOFTXMODEINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="486" name="COM_MINIMUMDELAYOFTXMODEINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="487" name="COM_TXFILTERINITSTATEENDIDXOFTXMODEINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="488" name="COM_TXFILTERINITSTATESTARTIDXOFTXMODEINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="489" name="COM_TXFILTERINITSTATEUSEDOFTXMODEINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="490" name="COM_TXMODEFALSEIDXOFTXMODEINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="491" name="COM_TXMODETRUEIDXOFTXMODEINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="492" name="COM_TXSIGINFOFILTERINITVALUEINDENDIDXOFTXMODEINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="493" name="COM_TXSIGINFOFILTERINITVALUEINDSTARTIDXOFTXMODEINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="494" name="COM_TXSIGINFOFILTERINITVALUEINDUSEDOFTXMODEINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="495" name="COM_TXMODETRUE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="496" name="COM_DIRECTOFTXMODETRUE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="497" name="COM_PERIODICOFTXMODETRUE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="498" name="COM_REPCNTOFTXMODETRUE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="499" name="COM_REPPERIODOFTXMODETRUE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="500" name="COM_TIMEOFFSETOFTXMODETRUE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="501" name="COM_TIMEPERIODOFTXMODETRUE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="502" name="COM_TXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="503" name="COM_CANCELLATIONSUPPORTOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="504" name="COM_CBKTXACKDEFFUNCPTRINDENDIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="505" name="COM_CBKTXACKDEFFUNCPTRINDSTARTIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="506" name="COM_CBKTXACKDEFFUNCPTRINDUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="507" name="COM_CBKTXACKIMFUNCPTRINDENDIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="508" name="COM_CBKTXACKIMFUNCPTRINDSTARTIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="509" name="COM_CBKTXACKIMFUNCPTRINDUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="510" name="COM_CBKTXERRFUNCPTRINDENDIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="511" name="COM_CBKTXERRFUNCPTRINDSTARTIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="512" name="COM_CBKTXERRFUNCPTRINDUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="513" name="COM_CLRUBOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="514" name="COM_EXISTS_TRANSMIT_CLRUBOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="515" name="COM_EXISTS_TRIGGER_TRANSMIT_CLRUBOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="516" name="COM_EXISTS_NOT_USED_CLRUBOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="517" name="COM_EXISTS_CONFIRMATION_CLRUBOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="518" name="COM_CONSTVALUEUINT8UBCLEARMASKENDIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="519" name="COM_CONSTVALUEUINT8UBCLEARMASKSTARTIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="520" name="COM_CONSTVALUEUINT8UBCLEARMASKUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="521" name="COM_EXTERNALIDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="522" name="COM_GWTIMEOUTINFOIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="523" name="COM_GWTIMEOUTINFOUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="524" name="COM_IPDUGROUPINFOOFTXPDUINFOINDENDIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="525" name="COM_IPDUGROUPINFOOFTXPDUINFOINDSTARTIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="526" name="COM_IPDUGROUPINFOOFTXPDUINFOINDUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="527" name="COM_INVALIDHNDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="528" name="COM_METADATALENGTHOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="529" name="COM_PDUGRPVECTORENDIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="530" name="COM_PDUGRPVECTORSTARTIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="531" name="COM_PDUGRPVECTORUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="532" name="COM_PDUWITHMETADATALENGTHOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="533" name="COM_TXBUFFERENDIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="534" name="COM_TXBUFFERLENGTHOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="535" name="COM_TXBUFFERMETADATAENDIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="536" name="COM_TXBUFFERMETADATALENGTHOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="537" name="COM_TXBUFFERMETADATASTARTIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="538" name="COM_TXBUFFERMETADATAUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="539" name="COM_TXBUFFERSTARTIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="540" name="COM_TXBUFFERUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="541" name="COM_TXPDUCALLOUTFUNCPTRIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="542" name="COM_TXPDUCALLOUTFUNCPTRUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="543" name="COM_TXPDUINITVALUEENDIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="544" name="COM_TXPDUINITVALUEMETADATAENDIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="545" name="COM_TXPDUINITVALUEMETADATASTARTIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="546" name="COM_TXPDUINITVALUEMETADATAUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="547" name="COM_TXPDUINITVALUESTARTIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="548" name="COM_TXPDUINITVALUEUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="549" name="COM_TXPDUTTCALLOUTFUNCPTRIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="550" name="COM_TXPDUTTCALLOUTFUNCPTRUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="551" name="COM_TXSIGGRPINFOINDENDIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="552" name="COM_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="553" name="COM_TXSIGGRPINFOINDUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="554" name="COM_TXTOUTINFOIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="555" name="COM_TXTOUTINFOUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="556" name="COM_TXTPINFOIDXOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="557" name="COM_TXTPINFOUSEDOFTXPDUINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="558" name="COM_TXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="559" name="COM_ARRAYACCESSUSEDOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="560" name="COM_INVALIDHNDOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="561" name="COM_PDUOFFSETOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="562" name="COM_SIGGROUPONCHANGEOFFSETOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="563" name="COM_SIGGROUPONCHANGESTARTPOSITIONOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="564" name="COM_SIGGROUPONCHANGEWITHOUTREPOFFSETOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="565" name="COM_SIGGROUPONCHANGEWITHOUTREPSTARTPOSITIONOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="566" name="COM_SIGGRPARRAYFILTERINFOENDIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="567" name="COM_SIGGRPARRAYFILTERINFOSTARTIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="568" name="COM_SIGGRPARRAYFILTERINFOUSEDOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="569" name="COM_TRANSFERPROPERTYOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="570" name="COM_EXISTS_PENDING_TRANSFERPROPERTYOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="571" name="COM_EXISTS_TRIGGERED_TRANSFERPROPERTYOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="572" name="COM_EXISTS_TRIGGERED_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="573" name="COM_EXISTS_TRIGGERED_ON_CHANGE_TRANSFERPROPERTYOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="574" name="COM_EXISTS_TRIGGERED_ON_CHANGE_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="575" name="COM_TXBUFFERENDIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="576" name="COM_TXBUFFERLENGTHOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="577" name="COM_TXBUFFERSIGGRPINTXIPDUENDIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="578" name="COM_TXBUFFERSIGGRPINTXIPDULENGTHOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="579" name="COM_TXBUFFERSIGGRPINTXIPDUSTARTIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="580" name="COM_TXBUFFERSTARTIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="581" name="COM_TXBUFFERUBIDXINTXBUFFERIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="582" name="COM_TXBUFFERUBIDXINTXBUFFERUSEDOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="583" name="COM_TXBUFFERUSEDOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="584" name="COM_TXFILTERINITSTATEENDIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="585" name="COM_TXFILTERINITSTATESTARTIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="586" name="COM_TXFILTERINITSTATEUSEDOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="587" name="COM_TXPDUINFOIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="588" name="COM_TXSIGGRPMASKENDIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="589" name="COM_TXSIGGRPMASKLENGTHOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="590" name="COM_TXSIGGRPMASKSTARTIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="591" name="COM_TXSIGGRPMASKUSEDOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="592" name="COM_TXSIGGRPONCHANGEMASKONCHANGEENDIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="593" name="COM_TXSIGGRPONCHANGEMASKONCHANGELENGTHOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="594" name="COM_TXSIGGRPONCHANGEMASKONCHANGESTARTIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="595" name="COM_TXSIGGRPONCHANGEMASKONCHANGEUSEDOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="596" name="COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPENDIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="597" name="COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPLENGTHOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="598" name="COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPSTARTIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="599" name="COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPUSEDOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="600" name="COM_TXSIGINFOINVVALUEINDENDIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="601" name="COM_TXSIGINFOINVVALUEINDSTARTIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="602" name="COM_TXSIGINFOINVVALUEINDUSEDOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="603" name="COM_UBMASKIDXOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="604" name="COM_UBMASKUSEDOFTXSIGGRPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="605" name="COM_TXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="606" name="COM_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="607" name="COM_EXISTS_UINT8_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="608" name="COM_EXISTS_SINT8_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="609" name="COM_EXISTS_UINT16_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="610" name="COM_EXISTS_SINT16_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="611" name="COM_EXISTS_UINT32_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="612" name="COM_EXISTS_SINT32_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="613" name="COM_EXISTS_UINT64_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="614" name="COM_EXISTS_SINT64_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="615" name="COM_EXISTS_UINT8_N_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="616" name="COM_EXISTS_UINT8_DYN_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="617" name="COM_EXISTS_ZEROBIT_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="618" name="COM_EXISTS_FLOAT32_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="619" name="COM_EXISTS_FLOAT64_APPLTYPEOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="620" name="COM_BITLENGTHOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="621" name="COM_BITPOSITIONOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="622" name="COM_BUSACCOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="623" name="COM_EXISTS_NBIT_BUSACCOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="624" name="COM_EXISTS_BYTE_BUSACCOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="625" name="COM_EXISTS_NBYTE_BUSACCOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="626" name="COM_EXISTS_NBYTE_SW_BUSACCOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="627" name="COM_EXISTS_NBITNBYTE_BUSACCOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="628" name="COM_EXISTS_NBITNBYTE_SW_BUSACCOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="629" name="COM_EXISTS_ARRAY_BASED_BUSACCOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="630" name="COM_BYTELENGTHOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="631" name="COM_BYTEPOSITIONOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="632" name="COM_CONSTVALUEARRAYBASEDINVVALUEENDIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="633" name="COM_CONSTVALUEARRAYBASEDINVVALUELENGTHOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="634" name="COM_CONSTVALUEARRAYBASEDINVVALUESTARTIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="635" name="COM_CONSTVALUEARRAYBASEDINVVALUEUSEDOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="636" name="COM_FILTERINFOIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="637" name="COM_FILTERINFOUSEDOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="638" name="COM_FILTERINITVALUEIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="639" name="COM_FILTERINITVALUEUSEDOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="640" name="COM_INVVALUEIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="641" name="COM_INVVALUEUSEDOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="642" name="COM_INVALIDHNDOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="643" name="COM_ONCHANGEIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="644" name="COM_ONCHANGEUSEDOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="645" name="COM_STARTBYTEINPDUPOSITIONOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="646" name="COM_TRIGGEREDOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="647" name="COM_TXBUFFERENDIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="648" name="COM_TXBUFFERLENGTHOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="649" name="COM_TXBUFFERSTARTIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="650" name="COM_TXBUFFERUBIDXINTXBUFFERIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="651" name="COM_TXBUFFERUBIDXINTXBUFFERUSEDOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="652" name="COM_TXBUFFERUSEDOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="653" name="COM_TXFILTERINITSTATEIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="654" name="COM_TXFILTERINITSTATEUSEDOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="655" name="COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUEENDIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="656" name="COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUELENGTHOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="657" name="COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUESTARTIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="658" name="COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUEUSEDOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="659" name="COM_TXPDUINFOIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="660" name="COM_TXSIGGRPINFOIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="661" name="COM_TXSIGGRPINFOUSEDOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="662" name="COM_UBMASKIDXOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="663" name="COM_UBMASKUSEDOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="664" name="COM_WITHOUTREPOFTXSIGINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="665" name="COM_TXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="666" name="COM_CBKTXTOUTFUNCPTRINDENDIDXOFTXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="667" name="COM_CBKTXTOUTFUNCPTRINDSTARTIDXOFTXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="668" name="COM_CBKTXTOUTFUNCPTRINDUSEDOFTXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="669" name="COM_FACTOROFTXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="670" name="COM_MODEOFTXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="671" name="COM_EXISTS_NORMAL_MODEOFTXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="672" name="COM_EXISTS_NONE_MODEOFTXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="673" name="COM_TXPDUINFOIDXOFTXTOUTINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="674" name="COM_TXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="675" name="COM_BUFFERSIZEOFTXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="676" name="COM_DYNAMICINITIALLENGTHOFTXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="677" name="COM_TXBUFFERENDIDXOFTXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="678" name="COM_TXBUFFERLENGTHOFTXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="679" name="COM_TXBUFFERSTARTIDXOFTXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="680" name="COM_TXBUFFERUSEDOFTXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="681" name="COM_TXSIGINFODYNSIGIDXOFTXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="682" name="COM_TXSIGINFODYNSIGUSEDOFTXTPINFO" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="683" name="COM_CBKRXTOUTFUNCPTR" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="684" name="COM_CBKRXTOUTFUNCPTRIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="685" name="COM_CBKTXACKDEFFUNCPTR" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="686" name="COM_CBKTXACKDEFFUNCPTRIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="687" name="COM_CBKTXACKIMFUNCPTR" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="688" name="COM_CBKTXACKIMFUNCPTRIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="689" name="COM_CBKTXERRFUNCPTR" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="690" name="COM_CBKTXERRFUNCPTRIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="691" name="COM_CBKTXTOUTFUNCPTR" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="692" name="COM_CBKTXTOUTFUNCPTRIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="693" name="COM_CONSTVALUEARRAYBASED" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="694" name="COM_CONSTVALUEFLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="695" name="COM_CONSTVALUEFLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="696" name="COM_CONSTVALUESINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="697" name="COM_CONSTVALUESINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="698" name="COM_CONSTVALUESINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="699" name="COM_CONSTVALUESINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="700" name="COM_CONSTVALUESIGGRPARRAYACCESS" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="701" name="COM_CONSTVALUEUINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="702" name="COM_CONSTVALUEUINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="703" name="COM_CONSTVALUEUINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="704" name="COM_CONSTVALUEUINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="705" name="COM_IPDUGROUPINFOOFRXPDUINFOIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="706" name="COM_IPDUGROUPINFOOFTXPDUINFOIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="707" name="COM_IPDUGROUPINFOTOSUBIPDUGROUPSIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="708" name="COM_PDUGRPVECTOR" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="709" name="COM_RXACCESSINFOGRPSIGIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="710" name="COM_RXACCESSINFOIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="711" name="COM_RXACCESSINFOREPLACEGRPSIGIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="712" name="COM_RXACCESSINFOREPLACESIGIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="713" name="COM_RXCBKFUNCPTR" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="714" name="COM_RXPDUCALLOUTFUNCPTR" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="715" name="COM_RXPDUINFOIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="716" name="COM_RXSIGGRPINFOIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="717" name="COM_RXTOUTINFOIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="718" name="COM_SOURCEDESCRIPTIONMASK" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="719" name="COM_TXFILTERINITSTATE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="720" name="COM_TXFILTERINITVALUEARRAYBASED" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="721" name="COM_TXFILTERINITVALUEFLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="722" name="COM_TXFILTERINITVALUEFLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="723" name="COM_TXFILTERINITVALUESINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="724" name="COM_TXFILTERINITVALUESINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="725" name="COM_TXFILTERINITVALUESINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="726" name="COM_TXFILTERINITVALUESINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="727" name="COM_TXFILTERINITVALUESIGGRPARRAYACCESS" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="728" name="COM_TXFILTERINITVALUEUINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="729" name="COM_TXFILTERINITVALUEUINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="730" name="COM_TXFILTERINITVALUEUINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="731" name="COM_TXFILTERINITVALUEUINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="732" name="COM_TXPDUCALLOUTFUNCPTR" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="733" name="COM_TXPDUINFOIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="734" name="COM_TXPDUINITVALUE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="735" name="COM_TXPDUTTCALLOUTFUNCPTR" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="736" name="COM_TXSIGGRPINFOIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="737" name="COM_TXSIGGRPMASK" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="738" name="COM_TXSIGGRPONCHANGEMASK" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="739" name="COM_TXSIGINFOFILTERINITVALUEIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="740" name="COM_TXSIGINFOINVVALUEIND" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="741" name="COM_CURRENTTXMODE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="742" name="COM_CYCLETIMECNT" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="743" name="COM_CYCLICSENDREQUEST" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="744" name="COM_DEFERREDGWMAPPINGEVENT" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="745" name="COM_DELAYTIMECNT" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="746" name="COM_DIRECTTRIGGER" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="747" name="COM_GWEVENT" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="748" name="COM_GWEVENTCACHE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="749" name="COM_GWROUTINGTIMEOUTCOUNTER" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="750" name="COM_HANDLERXDEFERREDGWDESCRIPTION" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="751" name="COM_HANDLERXPDUDEFERRED" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="752" name="COM_HANDLETXPDUDEFERRED" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="753" name="COM_IPDUGROUPSTATE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="754" name="COM_REPCNT" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="755" name="COM_REPCYCLECNT" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="756" name="COM_RXDEFPDUBUFFER" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="757" name="COM_RXDEFERREDEVENTCACHE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="758" name="COM_RXDEFERREDFCTPTRCACHE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="759" name="COM_RXDYNSIGNALLENGTH" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="760" name="COM_RXDYNSIGNALTMPLENGTHFORSIGNALGROUPS" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="761" name="COM_RXPDUDMSTATE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="762" name="COM_RXPDUGRPACTIVE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="763" name="COM_RXSIGARRAYACCESSSIGGRPBUFFER" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="764" name="COM_RXSIGBUFFERARRAYBASED" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="765" name="COM_RXSIGBUFFERFLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="766" name="COM_RXSIGBUFFERFLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="767" name="COM_RXSIGBUFFERSINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="768" name="COM_RXSIGBUFFERSINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="769" name="COM_RXSIGBUFFERSINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="770" name="COM_RXSIGBUFFERSINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="771" name="COM_RXSIGBUFFERUINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="772" name="COM_RXSIGBUFFERUINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="773" name="COM_RXSIGBUFFERUINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="774" name="COM_RXSIGBUFFERUINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="775" name="COM_RXSIGBUFFERZEROBIT" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="776" name="COM_RXTOUTCNT" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="777" name="COM_RXTPBUFFER" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="778" name="COM_RXTPCONNECTIONSTATE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="779" name="COM_RXTPSDULENGTH" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="780" name="COM_RXTPWRITTENBYTESCOUNTER" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="781" name="COM_SIGGRPEVENTFLAG" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="782" name="COM_TMPRXBUFFER" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="783" name="COM_TMPRXSHDBUFFERARRAYBASED" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="784" name="COM_TMPRXSHDBUFFERFLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="785" name="COM_TMPRXSHDBUFFERFLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="786" name="COM_TMPRXSHDBUFFERSINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="787" name="COM_TMPRXSHDBUFFERSINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="788" name="COM_TMPRXSHDBUFFERSINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="789" name="COM_TMPRXSHDBUFFERSINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="790" name="COM_TMPRXSHDBUFFERUINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="791" name="COM_TMPRXSHDBUFFERUINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="792" name="COM_TMPRXSHDBUFFERUINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="793" name="COM_TMPRXSHDBUFFERUINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="794" name="COM_TRANSMITREQUEST" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="795" name="COM_TXBUFFER" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="796" name="COM_TXDYNSIGNALLENGTH" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="797" name="COM_TXFILTEROLDVALUEARRAYBASED" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="798" name="COM_TXFILTEROLDVALUEFLOAT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="799" name="COM_TXFILTEROLDVALUEFLOAT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="800" name="COM_TXFILTEROLDVALUESINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="801" name="COM_TXFILTEROLDVALUESINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="802" name="COM_TXFILTEROLDVALUESINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="803" name="COM_TXFILTEROLDVALUESINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="804" name="COM_TXFILTEROLDVALUEUINT16" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="805" name="COM_TXFILTEROLDVALUEUINT32" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="806" name="COM_TXFILTEROLDVALUEUINT64" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="807" name="COM_TXFILTEROLDVALUEUINT8" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="808" name="COM_TXFILTERSTATE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="809" name="COM_TXPDUGRPACTIVE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="810" name="COM_TXSDULENGTH" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="811" name="COM_TXTOUTCNT" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="812" name="COM_TXTMPTPPDULENGTH" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="813" name="COM_TXTPCONNECTIONSTATE" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="814" name="COM_TXTPSDULENGTH" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="815" name="COM_TXTPWRITTENBYTESCOUNTER" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter><Parameter id="816" name="COM_WAITINGFORCONFIRMATION" type="1"><values><value>STD_ON</value><value>STD_OFF</value></values></Parameter></Parameters><Constraints><Constraint text="(COM_CONFIGURATION_VARIANT = &quot;COM_CONFIGURATION_VARIANT_PRECOMPILE&quot;) =&gt; (COM_USE_INIT_POINTER = &quot;STD_OFF&quot;)"><!--COM_USE_INIT_POINTER is always STD_OFF in the configuration variant precompile if selectable is not supported, else STD_ON or STD_OFF.--><Parameters><Parameter name="COM_CONFIGURATION_VARIANT"/><Parameter name="COM_USE_INIT_POINTER"/></Parameters></Constraint><Constraint text="(COM_CONFIGURATION_VARIANT = &quot;COM_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE&quot;) =&gt; (COM_USE_INIT_POINTER = &quot;STD_ON&quot;)"><!--COM_USE_INIT_POINTER is always STD_ON in the configuration variant postbuild loadable.--><Parameters><Parameter name="COM_CONFIGURATION_VARIANT"/><Parameter name="COM_USE_INIT_POINTER"/></Parameters></Constraint><Constraint text="(COM_CONFIGURATION_VARIANT = &quot;COM_CONFIGURATION_VARIANT_PRECOMPILE&quot;) =&gt; (COM_CHECK_INIT_POINTER = &quot;STD_OFF&quot;)"><!--COM_CHECK_INIT_POINTER is always STD_OFF in the configuration variant precompile.--><Parameters><Parameter name="COM_CONFIGURATION_VARIANT"/><Parameter name="COM_CHECK_INIT_POINTER"/></Parameters></Constraint><Constraint text="(COM_CONFIGURATION_VARIANT = &quot;COM_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE&quot;) =&gt; (COM_CHECK_INIT_POINTER = &quot;STD_ON&quot;)"><!--COM_CHECK_INIT_POINTER is always STD_ON in the configuration variant postbuild loadable.--><Parameters><Parameter name="COM_CONFIGURATION_VARIANT"/><Parameter name="COM_CHECK_INIT_POINTER"/></Parameters></Constraint><Constraint text="(COM_CONFIGURATION_VARIANT = &quot;COM_CONFIGURATION_VARIANT_PRECOMPILE&quot;) =&gt; (COM_FINAL_MAGIC_NUMBER = &quot;STD_OFF&quot;)"><!--COM_FINAL_MAGIC_NUMBER is always STD_OFF in the configuration variant precompile.--><Parameters><Parameter name="COM_CONFIGURATION_VARIANT"/><Parameter name="COM_FINAL_MAGIC_NUMBER"/></Parameters></Constraint><Constraint text="(COM_CONFIGURATION_VARIANT = &quot;COM_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE&quot;) =&gt; (COM_FINAL_MAGIC_NUMBER = &quot;STD_ON&quot;)"><!--COM_FINAL_MAGIC_NUMBER is always STD_ON in the configuration variant postbuild loadable.--><Parameters><Parameter name="COM_CONFIGURATION_VARIANT"/><Parameter name="COM_FINAL_MAGIC_NUMBER"/></Parameters></Constraint><Constraint text="(COM_CONFIGURATION_VARIANT = &quot;COM_CONFIGURATION_VARIANT_PRECOMPILE&quot;) =&gt; (COM_USE_ECUM_BSW_ERROR_HOOK = &quot;STD_OFF&quot;)"><!--COM_USE_ECUM_BSW_ERROR_HOOK is always STD_OFF in the configuration variant precompile.--><Parameters><Parameter name="COM_CONFIGURATION_VARIANT"/><Parameter name="COM_USE_ECUM_BSW_ERROR_HOOK"/></Parameters></Constraint><Constraint text="(COM_CONFIGURATION_VARIANT = &quot;COM_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE&quot;) =&gt; (COM_USE_ECUM_BSW_ERROR_HOOK = &quot;STD_ON&quot;)"><!--COM_USE_ECUM_BSW_ERROR_HOOK is always STD_ON in the configuration variant postbuild loadable.--><Parameters><Parameter name="COM_CONFIGURATION_VARIANT"/><Parameter name="COM_USE_ECUM_BSW_ERROR_HOOK"/></Parameters></Constraint><Constraint text="(COM_ACTIVATABLERXCOMIPDUS = &quot;STD_OFF&quot;) =&gt; (COM_RXPDUINFOIDXOFACTIVATABLERXCOMIPDUS = &quot;STD_OFF&quot;)"><!--If the structure COM_ACTIVATABLERXCOMIPDUS is deactivated, COM_RXPDUINFOIDXOFACTIVATABLERXCOMIPDUS is deactivated.--><Parameters><Parameter name="COM_ACTIVATABLERXCOMIPDUS"/><Parameter name="COM_RXPDUINFOIDXOFACTIVATABLERXCOMIPDUS"/></Parameters></Constraint><Constraint text="(COM_ACTIVATABLETXCOMIPDUS = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINFOIDXOFACTIVATABLETXCOMIPDUS = &quot;STD_OFF&quot;)"><!--If the structure COM_ACTIVATABLETXCOMIPDUS is deactivated, COM_TXPDUINFOIDXOFACTIVATABLETXCOMIPDUS is deactivated.--><Parameters><Parameter name="COM_ACTIVATABLETXCOMIPDUS"/><Parameter name="COM_TXPDUINFOIDXOFACTIVATABLETXCOMIPDUS"/></Parameters></Constraint><Constraint text="(COM_ALWAYSACTIVERXCOMIPDUS = &quot;STD_OFF&quot;) =&gt; (COM_RXPDUINFOIDXOFALWAYSACTIVERXCOMIPDUS = &quot;STD_OFF&quot;)"><!--If the structure COM_ALWAYSACTIVERXCOMIPDUS is deactivated, COM_RXPDUINFOIDXOFALWAYSACTIVERXCOMIPDUS is deactivated.--><Parameters><Parameter name="COM_ALWAYSACTIVERXCOMIPDUS"/><Parameter name="COM_RXPDUINFOIDXOFALWAYSACTIVERXCOMIPDUS"/></Parameters></Constraint><Constraint text="(COM_ALWAYSACTIVETXCOMIPDUS = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINFOIDXOFALWAYSACTIVETXCOMIPDUS = &quot;STD_OFF&quot;)"><!--If the structure COM_ALWAYSACTIVETXCOMIPDUS is deactivated, COM_TXPDUINFOIDXOFALWAYSACTIVETXCOMIPDUS is deactivated.--><Parameters><Parameter name="COM_ALWAYSACTIVETXCOMIPDUS"/><Parameter name="COM_TXPDUINFOIDXOFALWAYSACTIVETXCOMIPDUS"/></Parameters></Constraint><Constraint text="(COM_DEFERREDGWMAPPINGINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXPDUINFOIDXOFDEFERREDGWMAPPINGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_DEFERREDGWMAPPINGINFO is deactivated, COM_RXPDUINFOIDXOFDEFERREDGWMAPPINGINFO is deactivated.--><Parameters><Parameter name="COM_DEFERREDGWMAPPINGINFO"/><Parameter name="COM_RXPDUINFOIDXOFDEFERREDGWMAPPINGINFO"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_FLOAT32 is deactivated, COM_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT32"/><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT32 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT32 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT32 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT32 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT32 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT32 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT32 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT32 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT32"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMASKOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_FLOAT32 is deactivated, COM_FILTERMASKOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT32"/><Parameter name="COM_FILTERMASKOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMAXOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_FLOAT32 is deactivated, COM_FILTERMAXOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT32"/><Parameter name="COM_FILTERMAXOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMINOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_FLOAT32 is deactivated, COM_FILTERMINOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT32"/><Parameter name="COM_FILTERMINOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERXOFFILTERINFO_FLOAT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_FLOAT32 is deactivated, COM_FILTERXOFFILTERINFO_FLOAT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT32"/><Parameter name="COM_FILTERXOFFILTERINFO_FLOAT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_FLOAT64 is deactivated, COM_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT64"/><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT64 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT64 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT64 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT64 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT64 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT64 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT64 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_FLOAT64 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT64"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMASKOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_FLOAT64 is deactivated, COM_FILTERMASKOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT64"/><Parameter name="COM_FILTERMASKOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMAXOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_FLOAT64 is deactivated, COM_FILTERMAXOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT64"/><Parameter name="COM_FILTERMAXOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMINOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_FLOAT64 is deactivated, COM_FILTERMINOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT64"/><Parameter name="COM_FILTERMINOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_FLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERXOFFILTERINFO_FLOAT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_FLOAT64 is deactivated, COM_FILTERXOFFILTERINFO_FLOAT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_FLOAT64"/><Parameter name="COM_FILTERXOFFILTERINFO_FLOAT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT16 is deactivated, COM_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT16"/><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT16 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT16"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT16 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT16"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT16 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT16"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT16 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT16"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT16 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT16"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT16 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT16"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT16 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT16"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT16 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT16"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT16 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT16"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT16 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT16"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT16 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT16"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT16 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT16"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT16 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT16"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT16 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT16"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT16 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT16"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT16 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT16"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMASKOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT16 is deactivated, COM_FILTERMASKOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT16"/><Parameter name="COM_FILTERMASKOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMAXOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT16 is deactivated, COM_FILTERMAXOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT16"/><Parameter name="COM_FILTERMAXOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMINOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT16 is deactivated, COM_FILTERMINOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT16"/><Parameter name="COM_FILTERMINOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT16 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERXOFFILTERINFO_SINT16 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT16 is deactivated, COM_FILTERXOFFILTERINFO_SINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT16"/><Parameter name="COM_FILTERXOFFILTERINFO_SINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT32 is deactivated, COM_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT32"/><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT32 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT32"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT32 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT32"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT32 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT32"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT32 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT32"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT32 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT32"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT32 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT32"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT32 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT32"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT32 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT32"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT32 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT32"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT32 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT32"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT32 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT32"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT32 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT32"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT32 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT32"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT32 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT32"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT32 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT32"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT32 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT32"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMASKOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT32 is deactivated, COM_FILTERMASKOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT32"/><Parameter name="COM_FILTERMASKOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMAXOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT32 is deactivated, COM_FILTERMAXOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT32"/><Parameter name="COM_FILTERMAXOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMINOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT32 is deactivated, COM_FILTERMINOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT32"/><Parameter name="COM_FILTERMINOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERXOFFILTERINFO_SINT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT32 is deactivated, COM_FILTERXOFFILTERINFO_SINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT32"/><Parameter name="COM_FILTERXOFFILTERINFO_SINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT64 is deactivated, COM_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT64"/><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT64 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT64"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT64 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT64"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT64 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT64"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT64 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT64"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT64 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT64"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT64 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT64"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT64 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT64"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT64 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT64"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT64 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT64"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT64 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT64"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT64 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT64"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT64 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT64"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT64 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT64"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT64 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT64"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT64 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT64"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT64 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT64"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMASKOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT64 is deactivated, COM_FILTERMASKOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT64"/><Parameter name="COM_FILTERMASKOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMAXOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT64 is deactivated, COM_FILTERMAXOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT64"/><Parameter name="COM_FILTERMAXOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMINOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT64 is deactivated, COM_FILTERMINOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT64"/><Parameter name="COM_FILTERMINOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERXOFFILTERINFO_SINT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT64 is deactivated, COM_FILTERXOFFILTERINFO_SINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT64"/><Parameter name="COM_FILTERXOFFILTERINFO_SINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT8 is deactivated, COM_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT8"/><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT8 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT8"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT8 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT8"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT8 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT8"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT8 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT8"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT8 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT8"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT8 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT8"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT8 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT8"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT8 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT8"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT8 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT8"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT8 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT8"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT8 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT8"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT8 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT8"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT8 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT8"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT8 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT8"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_SINT8 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_SINT8"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_SINT8 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT8"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMASKOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT8 is deactivated, COM_FILTERMASKOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT8"/><Parameter name="COM_FILTERMASKOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMAXOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT8 is deactivated, COM_FILTERMAXOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT8"/><Parameter name="COM_FILTERMAXOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMINOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT8 is deactivated, COM_FILTERMINOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT8"/><Parameter name="COM_FILTERMINOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_SINT8 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERXOFFILTERINFO_SINT8 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_SINT8 is deactivated, COM_FILTERXOFFILTERINFO_SINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_SINT8"/><Parameter name="COM_FILTERXOFFILTERINFO_SINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT16 is deactivated, COM_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT16"/><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT16 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT16"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT16 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT16"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT16 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT16"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT16 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT16"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT16 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT16"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT16 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT16"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT16 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT16"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT16 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT16"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT16 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT16"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT16 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT16"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT16 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT16"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT16 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT16"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT16 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT16"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT16 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT16"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT16 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT16"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT16 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT16"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMASKOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT16 is deactivated, COM_FILTERMASKOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT16"/><Parameter name="COM_FILTERMASKOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMAXOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT16 is deactivated, COM_FILTERMAXOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT16"/><Parameter name="COM_FILTERMAXOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMINOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT16 is deactivated, COM_FILTERMINOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT16"/><Parameter name="COM_FILTERMINOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT16 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERXOFFILTERINFO_UINT16 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT16 is deactivated, COM_FILTERXOFFILTERINFO_UINT16 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT16"/><Parameter name="COM_FILTERXOFFILTERINFO_UINT16"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT32 is deactivated, COM_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT32"/><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT32 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT32"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT32 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT32"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT32 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT32"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT32 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT32"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT32 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT32"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT32 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT32"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT32 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT32"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT32 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT32"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT32 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT32"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT32 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT32"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT32 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT32"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT32 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT32"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT32 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT32"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT32 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT32"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT32 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT32"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT32 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT32"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMASKOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT32 is deactivated, COM_FILTERMASKOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT32"/><Parameter name="COM_FILTERMASKOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMAXOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT32 is deactivated, COM_FILTERMAXOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT32"/><Parameter name="COM_FILTERMAXOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMINOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT32 is deactivated, COM_FILTERMINOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT32"/><Parameter name="COM_FILTERMINOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT32 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERXOFFILTERINFO_UINT32 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT32 is deactivated, COM_FILTERXOFFILTERINFO_UINT32 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT32"/><Parameter name="COM_FILTERXOFFILTERINFO_UINT32"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT64 is deactivated, COM_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT64"/><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT64 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT64"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT64 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT64"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT64 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT64"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT64 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT64"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT64 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT64"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT64 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT64"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT64 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT64"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT64 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT64"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT64 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT64"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT64 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT64"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT64 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT64"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT64 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT64"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT64 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT64"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT64 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT64"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT64 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT64"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT64 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT64"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMASKOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT64 is deactivated, COM_FILTERMASKOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT64"/><Parameter name="COM_FILTERMASKOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMAXOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT64 is deactivated, COM_FILTERMAXOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT64"/><Parameter name="COM_FILTERMAXOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMINOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT64 is deactivated, COM_FILTERMINOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT64"/><Parameter name="COM_FILTERMINOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT64 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERXOFFILTERINFO_UINT64 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT64 is deactivated, COM_FILTERXOFFILTERINFO_UINT64 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT64"/><Parameter name="COM_FILTERXOFFILTERINFO_UINT64"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT8 is deactivated, COM_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8"/><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8 is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8 is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8 is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8 is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8 is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8 is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8 is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMASKOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT8 is deactivated, COM_FILTERMASKOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8"/><Parameter name="COM_FILTERMASKOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMAXOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT8 is deactivated, COM_FILTERMAXOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8"/><Parameter name="COM_FILTERMAXOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMINOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT8 is deactivated, COM_FILTERMINOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8"/><Parameter name="COM_FILTERMINOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8 = &quot;STD_OFF&quot;) =&gt; (COM_FILTERXOFFILTERINFO_UINT8 = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT8 is deactivated, COM_FILTERXOFFILTERINFO_UINT8 is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8"/><Parameter name="COM_FILTERXOFFILTERINFO_UINT8"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT8_N is deactivated, COM_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8_N"/><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8_N is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8_N is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8_N is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8_N is deactivated, COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8_N is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8_N is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8_N is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8_N is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8_N is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8_N is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8_N is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8_N is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8_N is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8_N is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFFILTERINFO_UINT8_N is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFFILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERINFO_UINT8_N is deactivated, COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8_N"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMASKOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT8_N is deactivated, COM_FILTERMASKOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8_N"/><Parameter name="COM_FILTERMASKOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMAXOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT8_N is deactivated, COM_FILTERMAXOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8_N"/><Parameter name="COM_FILTERMAXOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_FILTERMINOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT8_N is deactivated, COM_FILTERMINOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8_N"/><Parameter name="COM_FILTERMINOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_FILTERINFO_UINT8_N = &quot;STD_OFF&quot;) =&gt; (COM_FILTERXOFFILTERINFO_UINT8_N = &quot;STD_OFF&quot;)"><!--If the structure COM_FILTERINFO_UINT8_N is deactivated, COM_FILTERXOFFILTERINFO_UINT8_N is deactivated.--><Parameters><Parameter name="COM_FILTERINFO_UINT8_N"/><Parameter name="COM_FILTERXOFFILTERINFO_UINT8_N"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_BITOFFSETOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_BITOFFSETOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_BITOFFSETOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_BITSIZEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_BITSIZEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_BITSIZEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_COPYTYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_COPYTYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_COPYTYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_COPYTYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DIRECT_COPYTYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_COPYTYPEOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_DIRECT_COPYTYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_COPYTYPEOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_DIRECT_COPYTYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DIRECT_COPYTYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_DIRECT_COPYTYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_DIRECT_COPYTYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_COPYTYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_RIGHTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_COPYTYPEOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_RIGHTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_COPYTYPEOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_RIGHTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_RIGHTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_RIGHTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_RIGHTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_COPYTYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_LEFTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_COPYTYPEOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_LEFTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_COPYTYPEOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_LEFTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_LEFTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_LEFTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_LEFTSHIFT_COPYTYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_ENDIANNESSOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_ENDIANNESSOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_ENDIANNESSOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_ENDIANNESSOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_BIG_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_ENDIANNESSOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_BIG_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_ENDIANNESSOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_BIG_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_BIG_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_BIG_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_BIG_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_ENDIANNESSOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_LITTLE_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_ENDIANNESSOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_LITTLE_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_ENDIANNESSOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_LITTLE_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_LITTLE_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_LITTLE_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_LITTLE_ENDIAN_ENDIANNESSOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWSOURCESTARTBITINDEXOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_GWSOURCESTARTBITINDEXOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_GWSOURCESTARTBITINDEXOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXUBIDXOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_RXUBIDXOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_RXUBIDXOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXUBMASKIDXOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_RXUBMASKIDXOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_RXUBMASKIDXOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXUBMASKUSEDOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_RXUBMASKUSEDOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_RXUBMASKUSEDOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_SOURCEDESCRIPTIONMASKENDMASKIDXOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_SOURCEDESCRIPTIONMASKENDMASKIDXOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_SOURCEDESCRIPTIONMASKENDMASKIDXOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_SOURCEDESCRIPTIONMASKENDMASKUSEDOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_SOURCEDESCRIPTIONMASKENDMASKUSEDOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_SOURCEDESCRIPTIONMASKENDMASKUSEDOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_SOURCEDESCRIPTIONMASKIDXOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_SOURCEDESCRIPTIONMASKIDXOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_SOURCEDESCRIPTIONMASKIDXOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_SOURCESTARTBYTEPOSITIONOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_SOURCESTARTBYTEPOSITIONOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_SOURCESTARTBYTEPOSITIONOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERENDIDXOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_TXBUFFERENDIDXOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_TXBUFFERENDIDXOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERLENGTHOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_TXBUFFERLENGTHOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_TXBUFFERLENGTHOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERSTARTIDXOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_TXBUFFERSTARTIDXOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_TXBUFFERSTARTIDXOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERUBIDXOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_TXBUFFERUBIDXOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_TXBUFFERUBIDXOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERUBUSEDOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_TXBUFFERUBUSEDOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_TXBUFFERUBUSEDOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINFOIDXOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_TXPDUINFOIDXOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_TXPDUINFOIDXOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXUBMASKIDXOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_TXUBMASKIDXOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_TXUBMASKIDXOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXUBMASKUSEDOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_TXUBMASKUSEDOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_TXUBMASKUSEDOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_IMMEDIATE_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_TYPEOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_IMMEDIATE_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_IMMEDIATE_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_TYPEOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_IMMEDIATE_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_TYPEOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_TYPEOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_IMMEDIATE_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_TYPEOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_IMMEDIATE_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_DEFERRED_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_TYPEOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_DEFERRED_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_DEFERRED_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_DEFERRED_PENDING_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_DEFERRED_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_TYPEOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_DEFERRED_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_DEFERRED_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_DEFERRED_TRIGGERED_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_TYPEOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_TYPEOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_DEFERRED_TRIGGERED_ONCHANGE_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_DEFERRED_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_TYPEOFGWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_DEFERRED_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_EXISTS_DEFERRED_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_EXISTS_DEFERRED_TRIGGERED_WITHOUTREP_TYPEOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_VALIDDLCOFGWDESCRIPTIONACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWDESCRIPTIONACCESSINFO is deactivated, COM_VALIDDLCOFGWDESCRIPTIONACCESSINFO is deactivated.--><Parameters><Parameter name="COM_GWDESCRIPTIONACCESSINFO"/><Parameter name="COM_VALIDDLCOFGWDESCRIPTIONACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_GWGRPSIGMAPPING = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOIDXOFGWGRPSIGMAPPING = &quot;STD_OFF&quot;)"><!--If the structure COM_GWGRPSIGMAPPING is deactivated, COM_RXACCESSINFOIDXOFGWGRPSIGMAPPING is deactivated.--><Parameters><Parameter name="COM_GWGRPSIGMAPPING"/><Parameter name="COM_RXACCESSINFOIDXOFGWGRPSIGMAPPING"/></Parameters></Constraint><Constraint text="(COM_GWGRPSIGMAPPING = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGIDOFGWGRPSIGMAPPING = &quot;STD_OFF&quot;)"><!--If the structure COM_GWGRPSIGMAPPING is deactivated, COM_TXSIGIDOFGWGRPSIGMAPPING is deactivated.--><Parameters><Parameter name="COM_GWGRPSIGMAPPING"/><Parameter name="COM_TXSIGIDOFGWGRPSIGMAPPING"/></Parameters></Constraint><Constraint text="(COM_GWINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWSIGGRPMAPPINGENDIDXOFGWINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWINFO is deactivated, COM_GWSIGGRPMAPPINGENDIDXOFGWINFO is deactivated.--><Parameters><Parameter name="COM_GWINFO"/><Parameter name="COM_GWSIGGRPMAPPINGENDIDXOFGWINFO"/></Parameters></Constraint><Constraint text="(COM_GWINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWSIGGRPMAPPINGSTARTIDXOFGWINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWINFO is deactivated, COM_GWSIGGRPMAPPINGSTARTIDXOFGWINFO is deactivated.--><Parameters><Parameter name="COM_GWINFO"/><Parameter name="COM_GWSIGGRPMAPPINGSTARTIDXOFGWINFO"/></Parameters></Constraint><Constraint text="(COM_GWINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWSIGGRPMAPPINGUSEDOFGWINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWINFO is deactivated, COM_GWSIGGRPMAPPINGUSEDOFGWINFO is deactivated.--><Parameters><Parameter name="COM_GWINFO"/><Parameter name="COM_GWSIGGRPMAPPINGUSEDOFGWINFO"/></Parameters></Constraint><Constraint text="(COM_GWINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWSIGMAPPINGENDIDXOFGWINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWINFO is deactivated, COM_GWSIGMAPPINGENDIDXOFGWINFO is deactivated.--><Parameters><Parameter name="COM_GWINFO"/><Parameter name="COM_GWSIGMAPPINGENDIDXOFGWINFO"/></Parameters></Constraint><Constraint text="(COM_GWINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWSIGMAPPINGSTARTIDXOFGWINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWINFO is deactivated, COM_GWSIGMAPPINGSTARTIDXOFGWINFO is deactivated.--><Parameters><Parameter name="COM_GWINFO"/><Parameter name="COM_GWSIGMAPPINGSTARTIDXOFGWINFO"/></Parameters></Constraint><Constraint text="(COM_GWINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWSIGMAPPINGUSEDOFGWINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWINFO is deactivated, COM_GWSIGMAPPINGUSEDOFGWINFO is deactivated.--><Parameters><Parameter name="COM_GWINFO"/><Parameter name="COM_GWSIGMAPPINGUSEDOFGWINFO"/></Parameters></Constraint><Constraint text="(COM_GWSIGGRPMAPPING = &quot;STD_OFF&quot;) =&gt; (COM_GWGRPSIGMAPPINGENDIDXOFGWSIGGRPMAPPING = &quot;STD_OFF&quot;)"><!--If the structure COM_GWSIGGRPMAPPING is deactivated, COM_GWGRPSIGMAPPINGENDIDXOFGWSIGGRPMAPPING is deactivated.--><Parameters><Parameter name="COM_GWSIGGRPMAPPING"/><Parameter name="COM_GWGRPSIGMAPPINGENDIDXOFGWSIGGRPMAPPING"/></Parameters></Constraint><Constraint text="(COM_GWSIGGRPMAPPING = &quot;STD_OFF&quot;) =&gt; (COM_GWGRPSIGMAPPINGSTARTIDXOFGWSIGGRPMAPPING = &quot;STD_OFF&quot;)"><!--If the structure COM_GWSIGGRPMAPPING is deactivated, COM_GWGRPSIGMAPPINGSTARTIDXOFGWSIGGRPMAPPING is deactivated.--><Parameters><Parameter name="COM_GWSIGGRPMAPPING"/><Parameter name="COM_GWGRPSIGMAPPINGSTARTIDXOFGWSIGGRPMAPPING"/></Parameters></Constraint><Constraint text="(COM_GWSIGGRPMAPPING = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPIDOFGWSIGGRPMAPPING = &quot;STD_OFF&quot;)"><!--If the structure COM_GWSIGGRPMAPPING is deactivated, COM_TXSIGGRPIDOFGWSIGGRPMAPPING is deactivated.--><Parameters><Parameter name="COM_GWSIGGRPMAPPING"/><Parameter name="COM_TXSIGGRPIDOFGWSIGGRPMAPPING"/></Parameters></Constraint><Constraint text="(COM_GWSIGMAPPING = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOIDXOFGWSIGMAPPING = &quot;STD_OFF&quot;)"><!--If the structure COM_GWSIGMAPPING is deactivated, COM_RXACCESSINFOIDXOFGWSIGMAPPING is deactivated.--><Parameters><Parameter name="COM_GWSIGMAPPING"/><Parameter name="COM_RXACCESSINFOIDXOFGWSIGMAPPING"/></Parameters></Constraint><Constraint text="(COM_GWSIGMAPPING = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGIDOFGWSIGMAPPING = &quot;STD_OFF&quot;)"><!--If the structure COM_GWSIGMAPPING is deactivated, COM_TXSIGIDOFGWSIGMAPPING is deactivated.--><Parameters><Parameter name="COM_GWSIGMAPPING"/><Parameter name="COM_TXSIGIDOFGWSIGMAPPING"/></Parameters></Constraint><Constraint text="(COM_GWTIMEOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWROUTINGTIMEOUTFACTOROFGWTIMEOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWTIMEOUTINFO is deactivated, COM_GWROUTINGTIMEOUTFACTOROFGWTIMEOUTINFO is deactivated.--><Parameters><Parameter name="COM_GWTIMEOUTINFO"/><Parameter name="COM_GWROUTINGTIMEOUTFACTOROFGWTIMEOUTINFO"/></Parameters></Constraint><Constraint text="(COM_GWTIMEOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINFOIDXOFGWTIMEOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWTIMEOUTINFO is deactivated, COM_TXPDUINFOIDXOFGWTIMEOUTINFO is deactivated.--><Parameters><Parameter name="COM_GWTIMEOUTINFO"/><Parameter name="COM_TXPDUINFOIDXOFGWTIMEOUTINFO"/></Parameters></Constraint><Constraint text="(COM_GWTXPDUDESCRIPTIONINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWDESCRIPTIONACCESSINFOENDIDXOFGWTXPDUDESCRIPTIONINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWTXPDUDESCRIPTIONINFO is deactivated, COM_GWDESCRIPTIONACCESSINFOENDIDXOFGWTXPDUDESCRIPTIONINFO is deactivated.--><Parameters><Parameter name="COM_GWTXPDUDESCRIPTIONINFO"/><Parameter name="COM_GWDESCRIPTIONACCESSINFOENDIDXOFGWTXPDUDESCRIPTIONINFO"/></Parameters></Constraint><Constraint text="(COM_GWTXPDUDESCRIPTIONINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWDESCRIPTIONACCESSINFOSTARTIDXOFGWTXPDUDESCRIPTIONINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_GWTXPDUDESCRIPTIONINFO is deactivated, COM_GWDESCRIPTIONACCESSINFOSTARTIDXOFGWTXPDUDESCRIPTIONINFO is deactivated.--><Parameters><Parameter name="COM_GWTXPDUDESCRIPTIONINFO"/><Parameter name="COM_GWDESCRIPTIONACCESSINFOSTARTIDXOFGWTXPDUDESCRIPTIONINFO"/></Parameters></Constraint><Constraint text="(COM_IPDUGROUPINFO = &quot;STD_OFF&quot;) =&gt; (COM_IPDUGROUPINFOTOSUBIPDUGROUPSINDENDIDXOFIPDUGROUPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_IPDUGROUPINFO is deactivated, COM_IPDUGROUPINFOTOSUBIPDUGROUPSINDENDIDXOFIPDUGROUPINFO is deactivated.--><Parameters><Parameter name="COM_IPDUGROUPINFO"/><Parameter name="COM_IPDUGROUPINFOTOSUBIPDUGROUPSINDENDIDXOFIPDUGROUPINFO"/></Parameters></Constraint><Constraint text="(COM_IPDUGROUPINFO = &quot;STD_OFF&quot;) =&gt; (COM_IPDUGROUPINFOTOSUBIPDUGROUPSINDSTARTIDXOFIPDUGROUPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_IPDUGROUPINFO is deactivated, COM_IPDUGROUPINFOTOSUBIPDUGROUPSINDSTARTIDXOFIPDUGROUPINFO is deactivated.--><Parameters><Parameter name="COM_IPDUGROUPINFO"/><Parameter name="COM_IPDUGROUPINFOTOSUBIPDUGROUPSINDSTARTIDXOFIPDUGROUPINFO"/></Parameters></Constraint><Constraint text="(COM_IPDUGROUPINFO = &quot;STD_OFF&quot;) =&gt; (COM_IPDUGROUPINFOTOSUBIPDUGROUPSINDUSEDOFIPDUGROUPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_IPDUGROUPINFO is deactivated, COM_IPDUGROUPINFOTOSUBIPDUGROUPSINDUSEDOFIPDUGROUPINFO is deactivated.--><Parameters><Parameter name="COM_IPDUGROUPINFO"/><Parameter name="COM_IPDUGROUPINFOTOSUBIPDUGROUPSINDUSEDOFIPDUGROUPINFO"/></Parameters></Constraint><Constraint text="(COM_IPDUGROUPINFO = &quot;STD_OFF&quot;) =&gt; (COM_INVALIDHNDOFIPDUGROUPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_IPDUGROUPINFO is deactivated, COM_INVALIDHNDOFIPDUGROUPINFO is deactivated.--><Parameters><Parameter name="COM_IPDUGROUPINFO"/><Parameter name="COM_INVALIDHNDOFIPDUGROUPINFO"/></Parameters></Constraint><Constraint text="(COM_IPDUGROUPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXPDUINFOINDENDIDXOFIPDUGROUPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_IPDUGROUPINFO is deactivated, COM_RXPDUINFOINDENDIDXOFIPDUGROUPINFO is deactivated.--><Parameters><Parameter name="COM_IPDUGROUPINFO"/><Parameter name="COM_RXPDUINFOINDENDIDXOFIPDUGROUPINFO"/></Parameters></Constraint><Constraint text="(COM_IPDUGROUPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXPDUINFOINDSTARTIDXOFIPDUGROUPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_IPDUGROUPINFO is deactivated, COM_RXPDUINFOINDSTARTIDXOFIPDUGROUPINFO is deactivated.--><Parameters><Parameter name="COM_IPDUGROUPINFO"/><Parameter name="COM_RXPDUINFOINDSTARTIDXOFIPDUGROUPINFO"/></Parameters></Constraint><Constraint text="(COM_IPDUGROUPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXPDUINFOINDUSEDOFIPDUGROUPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_IPDUGROUPINFO is deactivated, COM_RXPDUINFOINDUSEDOFIPDUGROUPINFO is deactivated.--><Parameters><Parameter name="COM_IPDUGROUPINFO"/><Parameter name="COM_RXPDUINFOINDUSEDOFIPDUGROUPINFO"/></Parameters></Constraint><Constraint text="(COM_IPDUGROUPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINFOINDENDIDXOFIPDUGROUPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_IPDUGROUPINFO is deactivated, COM_TXPDUINFOINDENDIDXOFIPDUGROUPINFO is deactivated.--><Parameters><Parameter name="COM_IPDUGROUPINFO"/><Parameter name="COM_TXPDUINFOINDENDIDXOFIPDUGROUPINFO"/></Parameters></Constraint><Constraint text="(COM_IPDUGROUPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINFOINDSTARTIDXOFIPDUGROUPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_IPDUGROUPINFO is deactivated, COM_TXPDUINFOINDSTARTIDXOFIPDUGROUPINFO is deactivated.--><Parameters><Parameter name="COM_IPDUGROUPINFO"/><Parameter name="COM_TXPDUINFOINDSTARTIDXOFIPDUGROUPINFO"/></Parameters></Constraint><Constraint text="(COM_IPDUGROUPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINFOINDUSEDOFIPDUGROUPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_IPDUGROUPINFO is deactivated, COM_TXPDUINFOINDUSEDOFIPDUGROUPINFO is deactivated.--><Parameters><Parameter name="COM_IPDUGROUPINFO"/><Parameter name="COM_TXPDUINFOINDUSEDOFIPDUGROUPINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT8_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFRXACCESSINFO is deactivated, COM_EXISTS_UINT8_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/><Parameter name="COM_EXISTS_UINT8_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT8_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_UINT8_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_UINT8_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT8_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFRXACCESSINFO is deactivated, COM_EXISTS_SINT8_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/><Parameter name="COM_EXISTS_SINT8_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT8_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_SINT8_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_SINT8_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT16_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFRXACCESSINFO is deactivated, COM_EXISTS_UINT16_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/><Parameter name="COM_EXISTS_UINT16_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT16_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_UINT16_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_UINT16_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT16_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFRXACCESSINFO is deactivated, COM_EXISTS_SINT16_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/><Parameter name="COM_EXISTS_SINT16_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT16_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_SINT16_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_SINT16_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT32_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFRXACCESSINFO is deactivated, COM_EXISTS_UINT32_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/><Parameter name="COM_EXISTS_UINT32_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT32_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_UINT32_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_UINT32_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT32_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFRXACCESSINFO is deactivated, COM_EXISTS_SINT32_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/><Parameter name="COM_EXISTS_SINT32_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT32_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_SINT32_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_SINT32_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT64_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFRXACCESSINFO is deactivated, COM_EXISTS_UINT64_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/><Parameter name="COM_EXISTS_UINT64_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT64_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_UINT64_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_UINT64_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT64_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFRXACCESSINFO is deactivated, COM_EXISTS_SINT64_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/><Parameter name="COM_EXISTS_SINT64_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT64_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_SINT64_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_SINT64_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT8_N_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFRXACCESSINFO is deactivated, COM_EXISTS_UINT8_N_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/><Parameter name="COM_EXISTS_UINT8_N_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT8_N_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_UINT8_N_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_UINT8_N_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT8_DYN_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFRXACCESSINFO is deactivated, COM_EXISTS_UINT8_DYN_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/><Parameter name="COM_EXISTS_UINT8_DYN_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT8_DYN_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_UINT8_DYN_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_UINT8_DYN_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ZEROBIT_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFRXACCESSINFO is deactivated, COM_EXISTS_ZEROBIT_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/><Parameter name="COM_EXISTS_ZEROBIT_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ZEROBIT_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_ZEROBIT_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_ZEROBIT_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_FLOAT32_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFRXACCESSINFO is deactivated, COM_EXISTS_FLOAT32_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/><Parameter name="COM_EXISTS_FLOAT32_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_FLOAT32_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_FLOAT32_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_FLOAT32_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_FLOAT64_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFRXACCESSINFO is deactivated, COM_EXISTS_FLOAT64_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFRXACCESSINFO"/><Parameter name="COM_EXISTS_FLOAT64_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_FLOAT64_APPLTYPEOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_FLOAT64_APPLTYPEOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_FLOAT64_APPLTYPEOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_BITLENGTHOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_BITLENGTHOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_BITLENGTHOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_BITPOSITIONOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_BITPOSITIONOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_BITPOSITIONOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_BUFFERIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_BUFFERIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_BUFFERIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_BUFFERUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_BUFFERUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_BUFFERUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBIT_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFRXACCESSINFO is deactivated, COM_EXISTS_NBIT_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFRXACCESSINFO"/><Parameter name="COM_EXISTS_NBIT_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBIT_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_NBIT_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_NBIT_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_BYTE_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFRXACCESSINFO is deactivated, COM_EXISTS_BYTE_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFRXACCESSINFO"/><Parameter name="COM_EXISTS_BYTE_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_BYTE_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_BYTE_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_BYTE_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBYTE_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFRXACCESSINFO is deactivated, COM_EXISTS_NBYTE_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFRXACCESSINFO"/><Parameter name="COM_EXISTS_NBYTE_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBYTE_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_NBYTE_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_NBYTE_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBYTE_SW_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFRXACCESSINFO is deactivated, COM_EXISTS_NBYTE_SW_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFRXACCESSINFO"/><Parameter name="COM_EXISTS_NBYTE_SW_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBYTE_SW_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_NBYTE_SW_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_NBYTE_SW_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBITNBYTE_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFRXACCESSINFO is deactivated, COM_EXISTS_NBITNBYTE_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFRXACCESSINFO"/><Parameter name="COM_EXISTS_NBITNBYTE_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBITNBYTE_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_NBITNBYTE_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_NBITNBYTE_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBITNBYTE_SW_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFRXACCESSINFO is deactivated, COM_EXISTS_NBITNBYTE_SW_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFRXACCESSINFO"/><Parameter name="COM_EXISTS_NBITNBYTE_SW_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBITNBYTE_SW_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_NBITNBYTE_SW_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_NBITNBYTE_SW_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ARRAY_BASED_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFRXACCESSINFO is deactivated, COM_EXISTS_ARRAY_BASED_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFRXACCESSINFO"/><Parameter name="COM_EXISTS_ARRAY_BASED_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ARRAY_BASED_BUSACCOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXACCESSINFO is deactivated, COM_EXISTS_ARRAY_BASED_BUSACCOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_EXISTS_ARRAY_BASED_BUSACCOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_BYTELENGTHOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_BYTELENGTHOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_BYTELENGTHOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_BYTEPOSITIONOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_BYTEPOSITIONOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_BYTEPOSITIONOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDINITVALUELENGTHOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_CONSTVALUEARRAYBASEDINITVALUELENGTHOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDINITVALUELENGTHOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDINITVALUEUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_CONSTVALUEARRAYBASEDINITVALUEUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDINITVALUEUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDINVVALUEENDIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_CONSTVALUEARRAYBASEDINVVALUEENDIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDINVVALUEENDIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDINVVALUELENGTHOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_CONSTVALUEARRAYBASEDINVVALUELENGTHOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDINVVALUELENGTHOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDINVVALUESTARTIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_CONSTVALUEARRAYBASEDINVVALUESTARTIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDINVVALUESTARTIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDINVVALUEUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_CONSTVALUEARRAYBASEDINVVALUEUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDINVVALUEUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUEENDIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUEENDIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUEENDIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUELENGTHOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUELENGTHOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUELENGTHOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUESTARTIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUESTARTIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUESTARTIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUEUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUEUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDRXDATATIMEOUTSUBSTITUTIONVALUEUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_FILTERINFOIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_FILTERINFOIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_FILTERINFOIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_FILTERINFOUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_FILTERINFOUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_FILTERINFOUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWINFOIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_GWINFOIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_GWINFOIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWINFOUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_GWINFOUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_GWINFOUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_INITVALUEIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_INITVALUEIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_INITVALUEIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_INITVALUEUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_INITVALUEUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_INITVALUEUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_INVVALUEIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_INVVALUEIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_INVVALUEIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_INVVALUEUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_INVVALUEUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_INVVALUEUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_INVALIDHNDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_INVALIDHNDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_INVALIDHNDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_ISGROUPSIGNALOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_ISGROUPSIGNALOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_ISGROUPSIGNALOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_ROUTINGBUFFERIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_ROUTINGBUFFERIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_ROUTINGBUFFERIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_ROUTINGBUFFERUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_ROUTINGBUFFERUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_ROUTINGBUFFERUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXDATATIMEOUTSUBSTITUTIONVALUEIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXDATATIMEOUTSUBSTITUTIONVALUEIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXDATATIMEOUTSUBSTITUTIONVALUEIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXDATATIMEOUTSUBSTITUTIONVALUEUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXDATATIMEOUTSUBSTITUTIONVALUEUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXDATATIMEOUTSUBSTITUTIONVALUEUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXPDUINFOIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXPDUINFOIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXPDUINFOIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERARRAYBASEDBUFFERLENGTHOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXSIGBUFFERARRAYBASEDBUFFERLENGTHOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXSIGBUFFERARRAYBASEDBUFFERLENGTHOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERARRAYBASEDBUFFERUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXSIGBUFFERARRAYBASEDBUFFERUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXSIGBUFFERARRAYBASEDBUFFERUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERENDIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERENDIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERENDIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERLENGTHOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERLENGTHOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERLENGTHOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERSTARTIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERSTARTIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERSTARTIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXSIGBUFFERARRAYBASEDROUTINGBUFFERUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERARRAYBASEDSHDBUFFERENDIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXSIGBUFFERARRAYBASEDSHDBUFFERENDIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXSIGBUFFERARRAYBASEDSHDBUFFERENDIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERARRAYBASEDSHDBUFFERLENGTHOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXSIGBUFFERARRAYBASEDSHDBUFFERLENGTHOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXSIGBUFFERARRAYBASEDSHDBUFFERLENGTHOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERARRAYBASEDSHDBUFFERSTARTIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXSIGBUFFERARRAYBASEDSHDBUFFERSTARTIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXSIGBUFFERARRAYBASEDSHDBUFFERSTARTIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERARRAYBASEDSHDBUFFERUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXSIGBUFFERARRAYBASEDSHDBUFFERUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXSIGBUFFERARRAYBASEDSHDBUFFERUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTOUTINFOIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXTOUTINFOIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXTOUTINFOIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTOUTINFOUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_RXTOUTINFOUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_RXTOUTINFOUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_SHDBUFFERIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_SHDBUFFERIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_SHDBUFFERIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_SHDBUFFERUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_SHDBUFFERUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_SHDBUFFERUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_SIGNEXTREQUIREDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_SIGNEXTREQUIREDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_SIGNEXTREQUIREDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_STARTBYTEINPDUPOSITIONOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_STARTBYTEINPDUPOSITIONOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_STARTBYTEINPDUPOSITIONOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TMPBUFFERIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_TMPBUFFERIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_TMPBUFFERIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TMPBUFFERUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_TMPBUFFERUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_TMPBUFFERUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERENDIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERENDIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERENDIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERLENGTHOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERLENGTHOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERLENGTHOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERSTARTIDXOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERSTARTIDXOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERSTARTIDXOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXACCESSINFO = &quot;STD_OFF&quot;) =&gt; (COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERUSEDOFRXACCESSINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXACCESSINFO is deactivated, COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERUSEDOFRXACCESSINFO is deactivated.--><Parameters><Parameter name="COM_RXACCESSINFO"/><Parameter name="COM_TMPRXSHDBUFFERARRAYBASEDTMPBUFFERUSEDOFRXACCESSINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_DEFERREDGWMAPPINGINFOIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_DEFERREDGWMAPPINGINFOIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_DEFERREDGWMAPPINGINFOIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_DEFERREDGWMAPPINGINFOUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_DEFERREDGWMAPPINGINFOUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_DEFERREDGWMAPPINGINFOUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWINFOENDIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_GWINFOENDIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_GWINFOENDIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWINFOSTARTIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_GWINFOSTARTIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_GWINFOSTARTIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWINFOUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_GWINFOUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_GWINFOUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWTXPDUDESCRIPTIONINFOENDIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_GWTXPDUDESCRIPTIONINFOENDIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_GWTXPDUDESCRIPTIONINFOENDIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWTXPDUDESCRIPTIONINFOSTARTIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_GWTXPDUDESCRIPTIONINFOSTARTIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_GWTXPDUDESCRIPTIONINFOSTARTIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWTXPDUDESCRIPTIONINFOUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_GWTXPDUDESCRIPTIONINFOUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_GWTXPDUDESCRIPTIONINFOUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_HANDLERXDEFERREDGWDESCRIPTIONIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_HANDLERXDEFERREDGWDESCRIPTIONIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_HANDLERXDEFERREDGWDESCRIPTIONIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_HANDLERXDEFERREDGWDESCRIPTIONUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_HANDLERXDEFERREDGWDESCRIPTIONUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_HANDLERXDEFERREDGWDESCRIPTIONUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_HANDLERXPDUDEFERREDIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_HANDLERXPDUDEFERREDIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_HANDLERXPDUDEFERREDIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_HANDLERXPDUDEFERREDUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_HANDLERXPDUDEFERREDUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_HANDLERXPDUDEFERREDUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_IPDUGROUPINFOOFRXPDUINFOINDENDIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_IPDUGROUPINFOOFRXPDUINFOINDENDIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_IPDUGROUPINFOOFRXPDUINFOINDENDIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_IPDUGROUPINFOOFRXPDUINFOINDSTARTIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_IPDUGROUPINFOOFRXPDUINFOINDSTARTIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_IPDUGROUPINFOOFRXPDUINFOINDSTARTIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_IPDUGROUPINFOOFRXPDUINFOINDUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_IPDUGROUPINFOOFRXPDUINFOINDUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_IPDUGROUPINFOOFRXPDUINFOINDUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_INVALIDHNDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_INVALIDHNDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_INVALIDHNDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_METADATALENGTHOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_METADATALENGTHOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_METADATALENGTHOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_PDUGRPVECTORENDIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_PDUGRPVECTORENDIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_PDUGRPVECTORENDIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_PDUGRPVECTORSTARTIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_PDUGRPVECTORSTARTIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_PDUGRPVECTORSTARTIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_PDUGRPVECTORUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_PDUGRPVECTORUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_PDUGRPVECTORUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOINDENDIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXACCESSINFOINDENDIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXACCESSINFOINDENDIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOINDSTARTIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXACCESSINFOINDSTARTIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXACCESSINFOINDSTARTIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOINDUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXACCESSINFOINDUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXACCESSINFOINDUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXDEFPDUBUFFERENDIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXDEFPDUBUFFERENDIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXDEFPDUBUFFERENDIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXDEFPDUBUFFERLENGTHOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXDEFPDUBUFFERLENGTHOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXDEFPDUBUFFERLENGTHOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXDEFPDUBUFFERSTARTIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXDEFPDUBUFFERSTARTIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXDEFPDUBUFFERSTARTIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXDEFPDUBUFFERUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXDEFPDUBUFFERUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXDEFPDUBUFFERUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXPDUCALLOUTFUNCPTRIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXPDUCALLOUTFUNCPTRIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXPDUCALLOUTFUNCPTRIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXPDUCALLOUTFUNCPTRUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXPDUCALLOUTFUNCPTRUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXPDUCALLOUTFUNCPTRUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGGRPINFOINDENDIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXSIGGRPINFOINDENDIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXSIGGRPINFOINDENDIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGGRPINFOINDUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXSIGGRPINFOINDUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXSIGGRPINFOINDUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGINFOENDIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXSIGINFOENDIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXSIGINFOENDIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGINFOSTARTIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXSIGINFOSTARTIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXSIGINFOSTARTIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGINFOUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXSIGINFOUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXSIGINFOUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTOUTINFOIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXTOUTINFOIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXTOUTINFOIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTOUTINFOINDENDIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXTOUTINFOINDENDIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXTOUTINFOINDENDIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTOUTINFOINDSTARTIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXTOUTINFOINDSTARTIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXTOUTINFOINDSTARTIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTOUTINFOINDUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXTOUTINFOINDUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXTOUTINFOINDUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTOUTINFOUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXTOUTINFOUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXTOUTINFOUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTPINFOIDXOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXTPINFOIDXOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXTPINFOIDXOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTPINFOUSEDOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_RXTPINFOUSEDOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXTPINFOUSEDOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_SIGNALPROCESSINGOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_SIGNALPROCESSINGOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_SIGNALPROCESSINGOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_SIGNALPROCESSINGOFRXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGNALPROCESSINGOFRXPDUINFO is deactivated, COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_SIGNALPROCESSINGOFRXPDUINFO"/><Parameter name="COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXPDUINFO is deactivated, COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_SIGNALPROCESSINGOFRXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGNALPROCESSINGOFRXPDUINFO is deactivated, COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_SIGNALPROCESSINGOFRXPDUINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXPDUINFO is deactivated, COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TYPEOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXPDUINFO is deactivated, COM_TYPEOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_TYPEOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TYPEOFRXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NORMAL_TYPEOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TYPEOFRXPDUINFO is deactivated, COM_EXISTS_NORMAL_TYPEOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TYPEOFRXPDUINFO"/><Parameter name="COM_EXISTS_NORMAL_TYPEOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NORMAL_TYPEOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXPDUINFO is deactivated, COM_EXISTS_NORMAL_TYPEOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_EXISTS_NORMAL_TYPEOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TYPEOFRXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TP_TYPEOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TYPEOFRXPDUINFO is deactivated, COM_EXISTS_TP_TYPEOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TYPEOFRXPDUINFO"/><Parameter name="COM_EXISTS_TP_TYPEOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TP_TYPEOFRXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXPDUINFO is deactivated, COM_EXISTS_TP_TYPEOFRXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_EXISTS_TP_TYPEOFRXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_ARRAYACCESSUSEDOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_ARRAYACCESSUSEDOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_ARRAYACCESSUSEDOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUESIGGRPARRAYACCESSENDIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_CONSTVALUESIGGRPARRAYACCESSENDIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_CONSTVALUESIGGRPARRAYACCESSENDIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUESIGGRPARRAYACCESSLENGTHOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_CONSTVALUESIGGRPARRAYACCESSLENGTHOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_CONSTVALUESIGGRPARRAYACCESSLENGTHOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUESIGGRPARRAYACCESSSTARTIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_CONSTVALUESIGGRPARRAYACCESSSTARTIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_CONSTVALUESIGGRPARRAYACCESSSTARTIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUESIGGRPARRAYACCESSUSEDOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_CONSTVALUESIGGRPARRAYACCESSUSEDOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_CONSTVALUESIGGRPARRAYACCESSUSEDOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8ENDIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_CONSTVALUEUINT8ENDIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_CONSTVALUEUINT8ENDIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8LENGTHOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_CONSTVALUEUINT8LENGTHOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_CONSTVALUEUINT8LENGTHOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8STARTIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_CONSTVALUEUINT8STARTIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_CONSTVALUEUINT8STARTIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8USEDOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_CONSTVALUEUINT8USEDOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_CONSTVALUEUINT8USEDOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_FILTEREVENTOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_FILTEREVENTOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_FILTEREVENTOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWINFOIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_GWINFOIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_GWINFOIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWINFOUSEDOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_GWINFOUSEDOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_GWINFOUSEDOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_INVEVENTOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_INVEVENTOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_INVEVENTOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_INVALIDHNDOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_INVALIDHNDOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_INVALIDHNDOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOGRPSIGINDENDIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXACCESSINFOGRPSIGINDENDIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXACCESSINFOGRPSIGINDENDIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOGRPSIGINDSTARTIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXACCESSINFOGRPSIGINDSTARTIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXACCESSINFOGRPSIGINDSTARTIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOGRPSIGINDUSEDOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXACCESSINFOGRPSIGINDUSEDOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXACCESSINFOGRPSIGINDUSEDOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXCBKFUNCPTRACKIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXCBKFUNCPTRACKIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXCBKFUNCPTRACKIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXCBKFUNCPTRACKUSEDOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXCBKFUNCPTRACKUSEDOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXCBKFUNCPTRACKUSEDOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXCBKFUNCPTRINVACKIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXCBKFUNCPTRINVACKIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXCBKFUNCPTRINVACKIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXCBKFUNCPTRINVACKUSEDOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXCBKFUNCPTRINVACKUSEDOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXCBKFUNCPTRINVACKUSEDOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXPDUINFOIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXPDUINFOIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXPDUINFOIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGARRAYACCESSSIGGRPBUFFERENDIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXSIGARRAYACCESSSIGGRPBUFFERENDIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXSIGARRAYACCESSSIGGRPBUFFERENDIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGARRAYACCESSSIGGRPBUFFERLENGTHOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXSIGARRAYACCESSSIGGRPBUFFERLENGTHOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXSIGARRAYACCESSSIGGRPBUFFERLENGTHOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGARRAYACCESSSIGGRPBUFFERSTARTIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXSIGARRAYACCESSSIGGRPBUFFERSTARTIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXSIGARRAYACCESSSIGGRPBUFFERSTARTIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGARRAYACCESSSIGGRPBUFFERUSEDOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXSIGARRAYACCESSSIGGRPBUFFERUSEDOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXSIGARRAYACCESSSIGGRPBUFFERUSEDOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERUINT8ENDIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXSIGBUFFERUINT8ENDIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXSIGBUFFERUINT8ENDIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERUINT8LENGTHOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXSIGBUFFERUINT8LENGTHOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXSIGBUFFERUINT8LENGTHOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERUINT8STARTIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXSIGBUFFERUINT8STARTIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXSIGBUFFERUINT8STARTIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXSIGBUFFERUINT8USEDOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXSIGBUFFERUINT8USEDOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXSIGBUFFERUINT8USEDOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTOUTINFOIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXTOUTINFOIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXTOUTINFOIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTOUTINFOUSEDOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_RXTOUTINFOUSEDOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_RXTOUTINFOUSEDOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_SHDBUFFERREQUIREDOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_SHDBUFFERREQUIREDOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_SHDBUFFERREQUIREDOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_SIGNALPROCESSINGOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_SIGNALPROCESSINGOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_SIGNALPROCESSINGOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_SIGNALPROCESSINGOFRXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGNALPROCESSINGOFRXSIGGRPINFO is deactivated, COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_SIGNALPROCESSINGOFRXSIGGRPINFO"/><Parameter name="COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXSIGGRPINFO is deactivated, COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_SIGNALPROCESSINGOFRXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGNALPROCESSINGOFRXSIGGRPINFO is deactivated, COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_SIGNALPROCESSINGOFRXSIGGRPINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXSIGGRPINFO is deactivated, COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_STARTBYTEPOSITIONOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_STARTBYTEPOSITIONOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_STARTBYTEPOSITIONOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_UBIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_UBIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_UBIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_UBMASKIDXOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_UBMASKIDXOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_UBMASKIDXOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_UBMASKUSEDOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_UBMASKUSEDOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_UBMASKUSEDOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_VALIDDLCOFRXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGGRPINFO is deactivated, COM_VALIDDLCOFRXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGGRPINFO"/><Parameter name="COM_VALIDDLCOFRXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWINFOIDXOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_GWINFOIDXOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_GWINFOIDXOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWINFOUSEDOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_GWINFOUSEDOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_GWINFOUSEDOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOIDXOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_RXACCESSINFOIDXOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_RXACCESSINFOIDXOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXCBKFUNCPTRACKIDXOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_RXCBKFUNCPTRACKIDXOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_RXCBKFUNCPTRACKIDXOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXCBKFUNCPTRACKUSEDOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_RXCBKFUNCPTRACKUSEDOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_RXCBKFUNCPTRACKUSEDOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXCBKFUNCPTRINVACKIDXOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_RXCBKFUNCPTRINVACKIDXOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_RXCBKFUNCPTRINVACKIDXOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXCBKFUNCPTRINVACKUSEDOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_RXCBKFUNCPTRINVACKUSEDOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_RXCBKFUNCPTRINVACKUSEDOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTOUTINFOIDXOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_RXTOUTINFOIDXOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_RXTOUTINFOIDXOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTOUTINFOUSEDOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_RXTOUTINFOUSEDOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_RXTOUTINFOUSEDOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_SIGNALPROCESSINGOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_SIGNALPROCESSINGOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_SIGNALPROCESSINGOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_SIGNALPROCESSINGOFRXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGNALPROCESSINGOFRXSIGINFO is deactivated, COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_SIGNALPROCESSINGOFRXSIGINFO"/><Parameter name="COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXSIGINFO is deactivated, COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_EXISTS_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_SIGNALPROCESSINGOFRXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGNALPROCESSINGOFRXSIGINFO is deactivated, COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_SIGNALPROCESSINGOFRXSIGINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_RXSIGINFO is deactivated, COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_EXISTS_IMMEDIATE_SIGNALPROCESSINGOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_UBIDXOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_UBIDXOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_UBIDXOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_UBMASKIDXOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_UBMASKIDXOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_UBMASKIDXOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_UBMASKUSEDOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_UBMASKUSEDOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_UBMASKUSEDOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_VALIDDLCOFRXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXSIGINFO is deactivated, COM_VALIDDLCOFRXSIGINFO is deactivated.--><Parameters><Parameter name="COM_RXSIGINFO"/><Parameter name="COM_VALIDDLCOFRXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKRXTOUTFUNCPTRINDENDIDXOFRXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTOUTINFO is deactivated, COM_CBKRXTOUTFUNCPTRINDENDIDXOFRXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_CBKRXTOUTFUNCPTRINDENDIDXOFRXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKRXTOUTFUNCPTRINDSTARTIDXOFRXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTOUTINFO is deactivated, COM_CBKRXTOUTFUNCPTRINDSTARTIDXOFRXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_CBKRXTOUTFUNCPTRINDSTARTIDXOFRXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKRXTOUTFUNCPTRINDUSEDOFRXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTOUTINFO is deactivated, COM_CBKRXTOUTFUNCPTRINDUSEDOFRXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_CBKRXTOUTFUNCPTRINDUSEDOFRXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_FACTOROFRXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTOUTINFO is deactivated, COM_FACTOROFRXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_FACTOROFRXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_FIRSTFACTOROFRXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTOUTINFO is deactivated, COM_FIRSTFACTOROFRXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_FIRSTFACTOROFRXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOREPLACEGRPSIGINDENDIDXOFRXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTOUTINFO is deactivated, COM_RXACCESSINFOREPLACEGRPSIGINDENDIDXOFRXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_RXACCESSINFOREPLACEGRPSIGINDENDIDXOFRXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOREPLACEGRPSIGINDSTARTIDXOFRXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTOUTINFO is deactivated, COM_RXACCESSINFOREPLACEGRPSIGINDSTARTIDXOFRXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_RXACCESSINFOREPLACEGRPSIGINDSTARTIDXOFRXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOREPLACEGRPSIGINDUSEDOFRXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTOUTINFO is deactivated, COM_RXACCESSINFOREPLACEGRPSIGINDUSEDOFRXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_RXACCESSINFOREPLACEGRPSIGINDUSEDOFRXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOREPLACESIGINDENDIDXOFRXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTOUTINFO is deactivated, COM_RXACCESSINFOREPLACESIGINDENDIDXOFRXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_RXACCESSINFOREPLACESIGINDENDIDXOFRXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOREPLACESIGINDSTARTIDXOFRXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTOUTINFO is deactivated, COM_RXACCESSINFOREPLACESIGINDSTARTIDXOFRXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_RXACCESSINFOREPLACESIGINDSTARTIDXOFRXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFOREPLACESIGINDUSEDOFRXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTOUTINFO is deactivated, COM_RXACCESSINFOREPLACESIGINDUSEDOFRXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_RXACCESSINFOREPLACESIGINDUSEDOFRXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXPDUINFOIDXOFRXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTOUTINFO is deactivated, COM_RXPDUINFOIDXOFRXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_RXPDUINFOIDXOFRXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_BUFFERSIZEOFRXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTPINFO is deactivated, COM_BUFFERSIZEOFRXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_BUFFERSIZEOFRXTPINFO"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_DYNAMICINITIALLENGTHOFRXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTPINFO is deactivated, COM_DYNAMICINITIALLENGTHOFRXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_DYNAMICINITIALLENGTHOFRXTPINFO"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFODYNSIGIDXOFRXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTPINFO is deactivated, COM_RXACCESSINFODYNSIGIDXOFRXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXACCESSINFODYNSIGIDXOFRXTPINFO"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXACCESSINFODYNSIGUSEDOFRXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTPINFO is deactivated, COM_RXACCESSINFODYNSIGUSEDOFRXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXACCESSINFODYNSIGUSEDOFRXTPINFO"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTPBUFFERENDIDXOFRXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTPINFO is deactivated, COM_RXTPBUFFERENDIDXOFRXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXTPBUFFERENDIDXOFRXTPINFO"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTPBUFFERMETADATAENDIDXOFRXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTPINFO is deactivated, COM_RXTPBUFFERMETADATAENDIDXOFRXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXTPBUFFERMETADATAENDIDXOFRXTPINFO"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTPBUFFERMETADATALENGTHOFRXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTPINFO is deactivated, COM_RXTPBUFFERMETADATALENGTHOFRXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXTPBUFFERMETADATALENGTHOFRXTPINFO"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTPBUFFERMETADATASTARTIDXOFRXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTPINFO is deactivated, COM_RXTPBUFFERMETADATASTARTIDXOFRXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXTPBUFFERMETADATASTARTIDXOFRXTPINFO"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTPBUFFERMETADATAUSEDOFRXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTPINFO is deactivated, COM_RXTPBUFFERMETADATAUSEDOFRXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXTPBUFFERMETADATAUSEDOFRXTPINFO"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTPBUFFERSTARTIDXOFRXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTPINFO is deactivated, COM_RXTPBUFFERSTARTIDXOFRXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXTPBUFFERSTARTIDXOFRXTPINFO"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTPBUFFERUSEDOFRXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_RXTPINFO is deactivated, COM_RXTPBUFFERUSEDOFRXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXTPBUFFERUSEDOFRXTPINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8FILTERMASKENDIDXOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_SIGGRPARRAYFILTERINFO is deactivated, COM_CONSTVALUEUINT8FILTERMASKENDIDXOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_CONSTVALUEUINT8FILTERMASKENDIDXOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8FILTERMASKLENGTHOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_SIGGRPARRAYFILTERINFO is deactivated, COM_CONSTVALUEUINT8FILTERMASKLENGTHOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_CONSTVALUEUINT8FILTERMASKLENGTHOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8FILTERMASKSTARTIDXOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_SIGGRPARRAYFILTERINFO is deactivated, COM_CONSTVALUEUINT8FILTERMASKSTARTIDXOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_CONSTVALUEUINT8FILTERMASKSTARTIDXOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8FILTERMASKUSEDOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_SIGGRPARRAYFILTERINFO is deactivated, COM_CONSTVALUEUINT8FILTERMASKUSEDOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_CONSTVALUEUINT8FILTERMASKUSEDOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8FILTERVALUEXENDIDXOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_SIGGRPARRAYFILTERINFO is deactivated, COM_CONSTVALUEUINT8FILTERVALUEXENDIDXOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_CONSTVALUEUINT8FILTERVALUEXENDIDXOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8FILTERVALUEXLENGTHOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_SIGGRPARRAYFILTERINFO is deactivated, COM_CONSTVALUEUINT8FILTERVALUEXLENGTHOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_CONSTVALUEUINT8FILTERVALUEXLENGTHOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8FILTERVALUEXSTARTIDXOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_SIGGRPARRAYFILTERINFO is deactivated, COM_CONSTVALUEUINT8FILTERVALUEXSTARTIDXOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_CONSTVALUEUINT8FILTERVALUEXSTARTIDXOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8FILTERVALUEXUSEDOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_SIGGRPARRAYFILTERINFO is deactivated, COM_CONSTVALUEUINT8FILTERVALUEXUSEDOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_CONSTVALUEUINT8FILTERVALUEXUSEDOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_SIGGRPARRAYFILTERINFO is deactivated, COM_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFSIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ALWAYS_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_ALWAYS_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_ALWAYS_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_NEVER_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFSIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEVER_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_NEVER_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_NEVER_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFSIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_MASKED_OLD_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFSIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_MASKED_NEW_DIFFERS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFSIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_MASKED_NEW_EQUALS_X_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFSIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_NEW_IS_OUTSIDE_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFSIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_NEW_IS_WITHIN_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_NONE_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_FILTERALGOOFSIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_FILTERALGOOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_SIGGRPARRAYFILTERINFO is deactivated, COM_EXISTS_NONE_FILTERALGOOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_EXISTS_NONE_FILTERALGOOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_SIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;) =&gt; (COM_OFFSETINSIGNALGROUPOFSIGGRPARRAYFILTERINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_SIGGRPARRAYFILTERINFO is deactivated, COM_OFFSETINSIGNALGROUPOFSIGGRPARRAYFILTERINFO is deactivated.--><Parameters><Parameter name="COM_SIGGRPARRAYFILTERINFO"/><Parameter name="COM_OFFSETINSIGNALGROUPOFSIGGRPARRAYFILTERINFO"/></Parameters></Constraint><Constraint text="(COM_TXCYCLICPDU = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINFOIDXOFTXCYCLICPDU = &quot;STD_OFF&quot;)"><!--If the structure COM_TXCYCLICPDU is deactivated, COM_TXPDUINFOIDXOFTXCYCLICPDU is deactivated.--><Parameters><Parameter name="COM_TXCYCLICPDU"/><Parameter name="COM_TXPDUINFOIDXOFTXCYCLICPDU"/></Parameters></Constraint><Constraint text="(COM_TXMODEFALSE = &quot;STD_OFF&quot;) =&gt; (COM_DIRECTOFTXMODEFALSE = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEFALSE is deactivated, COM_DIRECTOFTXMODEFALSE is deactivated.--><Parameters><Parameter name="COM_TXMODEFALSE"/><Parameter name="COM_DIRECTOFTXMODEFALSE"/></Parameters></Constraint><Constraint text="(COM_TXMODEFALSE = &quot;STD_OFF&quot;) =&gt; (COM_PERIODICOFTXMODEFALSE = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEFALSE is deactivated, COM_PERIODICOFTXMODEFALSE is deactivated.--><Parameters><Parameter name="COM_TXMODEFALSE"/><Parameter name="COM_PERIODICOFTXMODEFALSE"/></Parameters></Constraint><Constraint text="(COM_TXMODEFALSE = &quot;STD_OFF&quot;) =&gt; (COM_REPCNTOFTXMODEFALSE = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEFALSE is deactivated, COM_REPCNTOFTXMODEFALSE is deactivated.--><Parameters><Parameter name="COM_TXMODEFALSE"/><Parameter name="COM_REPCNTOFTXMODEFALSE"/></Parameters></Constraint><Constraint text="(COM_TXMODEFALSE = &quot;STD_OFF&quot;) =&gt; (COM_REPPERIODOFTXMODEFALSE = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEFALSE is deactivated, COM_REPPERIODOFTXMODEFALSE is deactivated.--><Parameters><Parameter name="COM_TXMODEFALSE"/><Parameter name="COM_REPPERIODOFTXMODEFALSE"/></Parameters></Constraint><Constraint text="(COM_TXMODEFALSE = &quot;STD_OFF&quot;) =&gt; (COM_TIMEOFFSETOFTXMODEFALSE = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEFALSE is deactivated, COM_TIMEOFFSETOFTXMODEFALSE is deactivated.--><Parameters><Parameter name="COM_TXMODEFALSE"/><Parameter name="COM_TIMEOFFSETOFTXMODEFALSE"/></Parameters></Constraint><Constraint text="(COM_TXMODEFALSE = &quot;STD_OFF&quot;) =&gt; (COM_TIMEPERIODOFTXMODEFALSE = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEFALSE is deactivated, COM_TIMEPERIODOFTXMODEFALSE is deactivated.--><Parameters><Parameter name="COM_TXMODEFALSE"/><Parameter name="COM_TIMEPERIODOFTXMODEFALSE"/></Parameters></Constraint><Constraint text="(COM_TXMODEINFO = &quot;STD_OFF&quot;) =&gt; (COM_INITMODEOFTXMODEINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEINFO is deactivated, COM_INITMODEOFTXMODEINFO is deactivated.--><Parameters><Parameter name="COM_TXMODEINFO"/><Parameter name="COM_INITMODEOFTXMODEINFO"/></Parameters></Constraint><Constraint text="(COM_TXMODEINFO = &quot;STD_OFF&quot;) =&gt; (COM_INVALIDHNDOFTXMODEINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEINFO is deactivated, COM_INVALIDHNDOFTXMODEINFO is deactivated.--><Parameters><Parameter name="COM_TXMODEINFO"/><Parameter name="COM_INVALIDHNDOFTXMODEINFO"/></Parameters></Constraint><Constraint text="(COM_TXMODEINFO = &quot;STD_OFF&quot;) =&gt; (COM_MINIMUMDELAYOFTXMODEINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEINFO is deactivated, COM_MINIMUMDELAYOFTXMODEINFO is deactivated.--><Parameters><Parameter name="COM_TXMODEINFO"/><Parameter name="COM_MINIMUMDELAYOFTXMODEINFO"/></Parameters></Constraint><Constraint text="(COM_TXMODEINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTERINITSTATEENDIDXOFTXMODEINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEINFO is deactivated, COM_TXFILTERINITSTATEENDIDXOFTXMODEINFO is deactivated.--><Parameters><Parameter name="COM_TXMODEINFO"/><Parameter name="COM_TXFILTERINITSTATEENDIDXOFTXMODEINFO"/></Parameters></Constraint><Constraint text="(COM_TXMODEINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTERINITSTATESTARTIDXOFTXMODEINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEINFO is deactivated, COM_TXFILTERINITSTATESTARTIDXOFTXMODEINFO is deactivated.--><Parameters><Parameter name="COM_TXMODEINFO"/><Parameter name="COM_TXFILTERINITSTATESTARTIDXOFTXMODEINFO"/></Parameters></Constraint><Constraint text="(COM_TXMODEINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTERINITSTATEUSEDOFTXMODEINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEINFO is deactivated, COM_TXFILTERINITSTATEUSEDOFTXMODEINFO is deactivated.--><Parameters><Parameter name="COM_TXMODEINFO"/><Parameter name="COM_TXFILTERINITSTATEUSEDOFTXMODEINFO"/></Parameters></Constraint><Constraint text="(COM_TXMODEINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXMODEFALSEIDXOFTXMODEINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEINFO is deactivated, COM_TXMODEFALSEIDXOFTXMODEINFO is deactivated.--><Parameters><Parameter name="COM_TXMODEINFO"/><Parameter name="COM_TXMODEFALSEIDXOFTXMODEINFO"/></Parameters></Constraint><Constraint text="(COM_TXMODEINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXMODETRUEIDXOFTXMODEINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEINFO is deactivated, COM_TXMODETRUEIDXOFTXMODEINFO is deactivated.--><Parameters><Parameter name="COM_TXMODEINFO"/><Parameter name="COM_TXMODETRUEIDXOFTXMODEINFO"/></Parameters></Constraint><Constraint text="(COM_TXMODEINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGINFOFILTERINITVALUEINDENDIDXOFTXMODEINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEINFO is deactivated, COM_TXSIGINFOFILTERINITVALUEINDENDIDXOFTXMODEINFO is deactivated.--><Parameters><Parameter name="COM_TXMODEINFO"/><Parameter name="COM_TXSIGINFOFILTERINITVALUEINDENDIDXOFTXMODEINFO"/></Parameters></Constraint><Constraint text="(COM_TXMODEINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGINFOFILTERINITVALUEINDSTARTIDXOFTXMODEINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEINFO is deactivated, COM_TXSIGINFOFILTERINITVALUEINDSTARTIDXOFTXMODEINFO is deactivated.--><Parameters><Parameter name="COM_TXMODEINFO"/><Parameter name="COM_TXSIGINFOFILTERINITVALUEINDSTARTIDXOFTXMODEINFO"/></Parameters></Constraint><Constraint text="(COM_TXMODEINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGINFOFILTERINITVALUEINDUSEDOFTXMODEINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODEINFO is deactivated, COM_TXSIGINFOFILTERINITVALUEINDUSEDOFTXMODEINFO is deactivated.--><Parameters><Parameter name="COM_TXMODEINFO"/><Parameter name="COM_TXSIGINFOFILTERINITVALUEINDUSEDOFTXMODEINFO"/></Parameters></Constraint><Constraint text="(COM_TXMODETRUE = &quot;STD_OFF&quot;) =&gt; (COM_DIRECTOFTXMODETRUE = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODETRUE is deactivated, COM_DIRECTOFTXMODETRUE is deactivated.--><Parameters><Parameter name="COM_TXMODETRUE"/><Parameter name="COM_DIRECTOFTXMODETRUE"/></Parameters></Constraint><Constraint text="(COM_TXMODETRUE = &quot;STD_OFF&quot;) =&gt; (COM_PERIODICOFTXMODETRUE = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODETRUE is deactivated, COM_PERIODICOFTXMODETRUE is deactivated.--><Parameters><Parameter name="COM_TXMODETRUE"/><Parameter name="COM_PERIODICOFTXMODETRUE"/></Parameters></Constraint><Constraint text="(COM_TXMODETRUE = &quot;STD_OFF&quot;) =&gt; (COM_REPCNTOFTXMODETRUE = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODETRUE is deactivated, COM_REPCNTOFTXMODETRUE is deactivated.--><Parameters><Parameter name="COM_TXMODETRUE"/><Parameter name="COM_REPCNTOFTXMODETRUE"/></Parameters></Constraint><Constraint text="(COM_TXMODETRUE = &quot;STD_OFF&quot;) =&gt; (COM_REPPERIODOFTXMODETRUE = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODETRUE is deactivated, COM_REPPERIODOFTXMODETRUE is deactivated.--><Parameters><Parameter name="COM_TXMODETRUE"/><Parameter name="COM_REPPERIODOFTXMODETRUE"/></Parameters></Constraint><Constraint text="(COM_TXMODETRUE = &quot;STD_OFF&quot;) =&gt; (COM_TIMEOFFSETOFTXMODETRUE = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODETRUE is deactivated, COM_TIMEOFFSETOFTXMODETRUE is deactivated.--><Parameters><Parameter name="COM_TXMODETRUE"/><Parameter name="COM_TIMEOFFSETOFTXMODETRUE"/></Parameters></Constraint><Constraint text="(COM_TXMODETRUE = &quot;STD_OFF&quot;) =&gt; (COM_TIMEPERIODOFTXMODETRUE = &quot;STD_OFF&quot;)"><!--If the structure COM_TXMODETRUE is deactivated, COM_TIMEPERIODOFTXMODETRUE is deactivated.--><Parameters><Parameter name="COM_TXMODETRUE"/><Parameter name="COM_TIMEPERIODOFTXMODETRUE"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CANCELLATIONSUPPORTOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CANCELLATIONSUPPORTOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CANCELLATIONSUPPORTOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKTXACKDEFFUNCPTRINDENDIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CBKTXACKDEFFUNCPTRINDENDIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CBKTXACKDEFFUNCPTRINDENDIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKTXACKDEFFUNCPTRINDSTARTIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CBKTXACKDEFFUNCPTRINDSTARTIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CBKTXACKDEFFUNCPTRINDSTARTIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKTXACKDEFFUNCPTRINDUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CBKTXACKDEFFUNCPTRINDUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CBKTXACKDEFFUNCPTRINDUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKTXACKIMFUNCPTRINDENDIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CBKTXACKIMFUNCPTRINDENDIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CBKTXACKIMFUNCPTRINDENDIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKTXACKIMFUNCPTRINDSTARTIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CBKTXACKIMFUNCPTRINDSTARTIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CBKTXACKIMFUNCPTRINDSTARTIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKTXACKIMFUNCPTRINDUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CBKTXACKIMFUNCPTRINDUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CBKTXACKIMFUNCPTRINDUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKTXERRFUNCPTRINDENDIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CBKTXERRFUNCPTRINDENDIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CBKTXERRFUNCPTRINDENDIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKTXERRFUNCPTRINDSTARTIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CBKTXERRFUNCPTRINDSTARTIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CBKTXERRFUNCPTRINDSTARTIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKTXERRFUNCPTRINDUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CBKTXERRFUNCPTRINDUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CBKTXERRFUNCPTRINDUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CLRUBOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CLRUBOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CLRUBOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_CLRUBOFTXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TRANSMIT_CLRUBOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_CLRUBOFTXPDUINFO is deactivated, COM_EXISTS_TRANSMIT_CLRUBOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_CLRUBOFTXPDUINFO"/><Parameter name="COM_EXISTS_TRANSMIT_CLRUBOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TRANSMIT_CLRUBOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXPDUINFO is deactivated, COM_EXISTS_TRANSMIT_CLRUBOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_EXISTS_TRANSMIT_CLRUBOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_CLRUBOFTXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TRIGGER_TRANSMIT_CLRUBOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_CLRUBOFTXPDUINFO is deactivated, COM_EXISTS_TRIGGER_TRANSMIT_CLRUBOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_CLRUBOFTXPDUINFO"/><Parameter name="COM_EXISTS_TRIGGER_TRANSMIT_CLRUBOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TRIGGER_TRANSMIT_CLRUBOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXPDUINFO is deactivated, COM_EXISTS_TRIGGER_TRANSMIT_CLRUBOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_EXISTS_TRIGGER_TRANSMIT_CLRUBOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_CLRUBOFTXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NOT_USED_CLRUBOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_CLRUBOFTXPDUINFO is deactivated, COM_EXISTS_NOT_USED_CLRUBOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_CLRUBOFTXPDUINFO"/><Parameter name="COM_EXISTS_NOT_USED_CLRUBOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NOT_USED_CLRUBOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXPDUINFO is deactivated, COM_EXISTS_NOT_USED_CLRUBOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_EXISTS_NOT_USED_CLRUBOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_CLRUBOFTXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_CONFIRMATION_CLRUBOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_CLRUBOFTXPDUINFO is deactivated, COM_EXISTS_CONFIRMATION_CLRUBOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_CLRUBOFTXPDUINFO"/><Parameter name="COM_EXISTS_CONFIRMATION_CLRUBOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_CONFIRMATION_CLRUBOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXPDUINFO is deactivated, COM_EXISTS_CONFIRMATION_CLRUBOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_EXISTS_CONFIRMATION_CLRUBOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8UBCLEARMASKENDIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CONSTVALUEUINT8UBCLEARMASKENDIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CONSTVALUEUINT8UBCLEARMASKENDIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8UBCLEARMASKSTARTIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CONSTVALUEUINT8UBCLEARMASKSTARTIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CONSTVALUEUINT8UBCLEARMASKSTARTIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEUINT8UBCLEARMASKUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_CONSTVALUEUINT8UBCLEARMASKUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CONSTVALUEUINT8UBCLEARMASKUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXTERNALIDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_EXTERNALIDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_EXTERNALIDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWTIMEOUTINFOIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_GWTIMEOUTINFOIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_GWTIMEOUTINFOIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWTIMEOUTINFOUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_GWTIMEOUTINFOUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_GWTIMEOUTINFOUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_IPDUGROUPINFOOFTXPDUINFOINDENDIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_IPDUGROUPINFOOFTXPDUINFOINDENDIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_IPDUGROUPINFOOFTXPDUINFOINDENDIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_IPDUGROUPINFOOFTXPDUINFOINDSTARTIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_IPDUGROUPINFOOFTXPDUINFOINDSTARTIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_IPDUGROUPINFOOFTXPDUINFOINDSTARTIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_IPDUGROUPINFOOFTXPDUINFOINDUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_IPDUGROUPINFOOFTXPDUINFOINDUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_IPDUGROUPINFOOFTXPDUINFOINDUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_INVALIDHNDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_INVALIDHNDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_INVALIDHNDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_METADATALENGTHOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_METADATALENGTHOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_METADATALENGTHOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_PDUGRPVECTORENDIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_PDUGRPVECTORENDIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_PDUGRPVECTORENDIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_PDUGRPVECTORSTARTIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_PDUGRPVECTORSTARTIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_PDUGRPVECTORSTARTIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_PDUGRPVECTORUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_PDUGRPVECTORUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_PDUGRPVECTORUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_PDUWITHMETADATALENGTHOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_PDUWITHMETADATALENGTHOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_PDUWITHMETADATALENGTHOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERENDIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXBUFFERENDIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXBUFFERENDIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERLENGTHOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXBUFFERLENGTHOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXBUFFERLENGTHOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERMETADATAENDIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXBUFFERMETADATAENDIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXBUFFERMETADATAENDIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERMETADATALENGTHOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXBUFFERMETADATALENGTHOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXBUFFERMETADATALENGTHOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERMETADATASTARTIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXBUFFERMETADATASTARTIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXBUFFERMETADATASTARTIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERMETADATAUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXBUFFERMETADATAUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXBUFFERMETADATAUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERSTARTIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXBUFFERSTARTIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXBUFFERSTARTIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXBUFFERUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXBUFFERUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUCALLOUTFUNCPTRIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXPDUCALLOUTFUNCPTRIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXPDUCALLOUTFUNCPTRIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUCALLOUTFUNCPTRUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXPDUCALLOUTFUNCPTRUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXPDUCALLOUTFUNCPTRUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINITVALUEENDIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXPDUINITVALUEENDIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXPDUINITVALUEENDIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINITVALUEMETADATAENDIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXPDUINITVALUEMETADATAENDIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXPDUINITVALUEMETADATAENDIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINITVALUEMETADATASTARTIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXPDUINITVALUEMETADATASTARTIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXPDUINITVALUEMETADATASTARTIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINITVALUEMETADATAUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXPDUINITVALUEMETADATAUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXPDUINITVALUEMETADATAUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINITVALUESTARTIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXPDUINITVALUESTARTIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXPDUINITVALUESTARTIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINITVALUEUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXPDUINITVALUEUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXPDUINITVALUEUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUTTCALLOUTFUNCPTRIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXPDUTTCALLOUTFUNCPTRIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXPDUTTCALLOUTFUNCPTRIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUTTCALLOUTFUNCPTRUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXPDUTTCALLOUTFUNCPTRUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXPDUTTCALLOUTFUNCPTRUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPINFOINDENDIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXSIGGRPINFOINDENDIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXSIGGRPINFOINDENDIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPINFOINDUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXSIGGRPINFOINDUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXSIGGRPINFOINDUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXTOUTINFOIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXTOUTINFOIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXTOUTINFOIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXTOUTINFOUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXTOUTINFOUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXTOUTINFOUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXTPINFOIDXOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXTPINFOIDXOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXTPINFOIDXOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXTPINFOUSEDOFTXPDUINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXPDUINFO is deactivated, COM_TXTPINFOUSEDOFTXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXTPINFOUSEDOFTXPDUINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_ARRAYACCESSUSEDOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_ARRAYACCESSUSEDOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_ARRAYACCESSUSEDOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_INVALIDHNDOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_INVALIDHNDOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_INVALIDHNDOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_PDUOFFSETOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_PDUOFFSETOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_PDUOFFSETOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_SIGGROUPONCHANGEOFFSETOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_SIGGROUPONCHANGEOFFSETOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_SIGGROUPONCHANGEOFFSETOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_SIGGROUPONCHANGESTARTPOSITIONOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_SIGGROUPONCHANGESTARTPOSITIONOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_SIGGROUPONCHANGESTARTPOSITIONOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_SIGGROUPONCHANGEWITHOUTREPOFFSETOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_SIGGROUPONCHANGEWITHOUTREPOFFSETOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_SIGGROUPONCHANGEWITHOUTREPOFFSETOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_SIGGROUPONCHANGEWITHOUTREPSTARTPOSITIONOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_SIGGROUPONCHANGEWITHOUTREPSTARTPOSITIONOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_SIGGROUPONCHANGEWITHOUTREPSTARTPOSITIONOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_SIGGRPARRAYFILTERINFOENDIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_SIGGRPARRAYFILTERINFOENDIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_SIGGRPARRAYFILTERINFOENDIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_SIGGRPARRAYFILTERINFOSTARTIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_SIGGRPARRAYFILTERINFOSTARTIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_SIGGRPARRAYFILTERINFOSTARTIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_SIGGRPARRAYFILTERINFOUSEDOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_SIGGRPARRAYFILTERINFOUSEDOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_SIGGRPARRAYFILTERINFOUSEDOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TRANSFERPROPERTYOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_PENDING_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated, COM_EXISTS_PENDING_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TRANSFERPROPERTYOFTXSIGGRPINFO"/><Parameter name="COM_EXISTS_PENDING_TRANSFERPROPERTYOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_PENDING_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGGRPINFO is deactivated, COM_EXISTS_PENDING_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_EXISTS_PENDING_TRANSFERPROPERTYOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TRIGGERED_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated, COM_EXISTS_TRIGGERED_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TRANSFERPROPERTYOFTXSIGGRPINFO"/><Parameter name="COM_EXISTS_TRIGGERED_TRANSFERPROPERTYOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TRIGGERED_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGGRPINFO is deactivated, COM_EXISTS_TRIGGERED_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_EXISTS_TRIGGERED_TRANSFERPROPERTYOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TRIGGERED_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated, COM_EXISTS_TRIGGERED_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TRANSFERPROPERTYOFTXSIGGRPINFO"/><Parameter name="COM_EXISTS_TRIGGERED_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TRIGGERED_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGGRPINFO is deactivated, COM_EXISTS_TRIGGERED_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_EXISTS_TRIGGERED_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TRIGGERED_ON_CHANGE_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated, COM_EXISTS_TRIGGERED_ON_CHANGE_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TRANSFERPROPERTYOFTXSIGGRPINFO"/><Parameter name="COM_EXISTS_TRIGGERED_ON_CHANGE_TRANSFERPROPERTYOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TRIGGERED_ON_CHANGE_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGGRPINFO is deactivated, COM_EXISTS_TRIGGERED_ON_CHANGE_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_EXISTS_TRIGGERED_ON_CHANGE_TRANSFERPROPERTYOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TRIGGERED_ON_CHANGE_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated, COM_EXISTS_TRIGGERED_ON_CHANGE_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TRANSFERPROPERTYOFTXSIGGRPINFO"/><Parameter name="COM_EXISTS_TRIGGERED_ON_CHANGE_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_TRIGGERED_ON_CHANGE_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGGRPINFO is deactivated, COM_EXISTS_TRIGGERED_ON_CHANGE_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_EXISTS_TRIGGERED_ON_CHANGE_WITHOUT_REPETITION_TRANSFERPROPERTYOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERENDIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXBUFFERENDIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXBUFFERENDIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERLENGTHOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXBUFFERLENGTHOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXBUFFERLENGTHOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERSIGGRPINTXIPDUENDIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXBUFFERSIGGRPINTXIPDUENDIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXBUFFERSIGGRPINTXIPDUENDIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERSIGGRPINTXIPDULENGTHOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXBUFFERSIGGRPINTXIPDULENGTHOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXBUFFERSIGGRPINTXIPDULENGTHOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERSIGGRPINTXIPDUSTARTIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXBUFFERSIGGRPINTXIPDUSTARTIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXBUFFERSIGGRPINTXIPDUSTARTIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERSTARTIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXBUFFERSTARTIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXBUFFERSTARTIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERUBIDXINTXBUFFERIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXBUFFERUBIDXINTXBUFFERIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXBUFFERUBIDXINTXBUFFERIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERUBIDXINTXBUFFERUSEDOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXBUFFERUBIDXINTXBUFFERUSEDOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXBUFFERUBIDXINTXBUFFERUSEDOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERUSEDOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXBUFFERUSEDOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXBUFFERUSEDOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTERINITSTATEENDIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXFILTERINITSTATEENDIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXFILTERINITSTATEENDIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTERINITSTATESTARTIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXFILTERINITSTATESTARTIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXFILTERINITSTATESTARTIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTERINITSTATEUSEDOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXFILTERINITSTATEUSEDOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXFILTERINITSTATEUSEDOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINFOIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXPDUINFOIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXPDUINFOIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPMASKENDIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGGRPMASKENDIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGGRPMASKENDIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPMASKLENGTHOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGGRPMASKLENGTHOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGGRPMASKLENGTHOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPMASKSTARTIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGGRPMASKSTARTIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGGRPMASKSTARTIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPMASKUSEDOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGGRPMASKUSEDOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGGRPMASKUSEDOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPONCHANGEMASKONCHANGEENDIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGGRPONCHANGEMASKONCHANGEENDIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGGRPONCHANGEMASKONCHANGEENDIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPONCHANGEMASKONCHANGELENGTHOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGGRPONCHANGEMASKONCHANGELENGTHOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGGRPONCHANGEMASKONCHANGELENGTHOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPONCHANGEMASKONCHANGESTARTIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGGRPONCHANGEMASKONCHANGESTARTIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGGRPONCHANGEMASKONCHANGESTARTIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPONCHANGEMASKONCHANGEUSEDOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGGRPONCHANGEMASKONCHANGEUSEDOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGGRPONCHANGEMASKONCHANGEUSEDOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPENDIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPENDIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPENDIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPLENGTHOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPLENGTHOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPLENGTHOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPSTARTIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPSTARTIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPSTARTIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPUSEDOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPUSEDOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGGRPONCHANGEMASKONCHANGEWITHOUTREPUSEDOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGINFOINVVALUEINDENDIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGINFOINVVALUEINDENDIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGINFOINVVALUEINDENDIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGINFOINVVALUEINDSTARTIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGINFOINVVALUEINDSTARTIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGINFOINVVALUEINDSTARTIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGINFOINVVALUEINDUSEDOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_TXSIGINFOINVVALUEINDUSEDOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_TXSIGINFOINVVALUEINDUSEDOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_UBMASKIDXOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_UBMASKIDXOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_UBMASKIDXOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_UBMASKUSEDOFTXSIGGRPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGGRPINFO is deactivated, COM_UBMASKUSEDOFTXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_UBMASKUSEDOFTXSIGGRPINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT8_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFTXSIGINFO is deactivated, COM_EXISTS_UINT8_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFTXSIGINFO"/><Parameter name="COM_EXISTS_UINT8_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT8_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_UINT8_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_UINT8_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT8_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFTXSIGINFO is deactivated, COM_EXISTS_SINT8_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFTXSIGINFO"/><Parameter name="COM_EXISTS_SINT8_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT8_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_SINT8_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_SINT8_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT16_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFTXSIGINFO is deactivated, COM_EXISTS_UINT16_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFTXSIGINFO"/><Parameter name="COM_EXISTS_UINT16_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT16_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_UINT16_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_UINT16_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT16_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFTXSIGINFO is deactivated, COM_EXISTS_SINT16_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFTXSIGINFO"/><Parameter name="COM_EXISTS_SINT16_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT16_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_SINT16_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_SINT16_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT32_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFTXSIGINFO is deactivated, COM_EXISTS_UINT32_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFTXSIGINFO"/><Parameter name="COM_EXISTS_UINT32_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT32_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_UINT32_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_UINT32_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT32_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFTXSIGINFO is deactivated, COM_EXISTS_SINT32_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFTXSIGINFO"/><Parameter name="COM_EXISTS_SINT32_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT32_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_SINT32_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_SINT32_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT64_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFTXSIGINFO is deactivated, COM_EXISTS_UINT64_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFTXSIGINFO"/><Parameter name="COM_EXISTS_UINT64_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT64_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_UINT64_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_UINT64_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT64_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFTXSIGINFO is deactivated, COM_EXISTS_SINT64_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFTXSIGINFO"/><Parameter name="COM_EXISTS_SINT64_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_SINT64_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_SINT64_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_SINT64_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT8_N_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFTXSIGINFO is deactivated, COM_EXISTS_UINT8_N_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFTXSIGINFO"/><Parameter name="COM_EXISTS_UINT8_N_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT8_N_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_UINT8_N_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_UINT8_N_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT8_DYN_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFTXSIGINFO is deactivated, COM_EXISTS_UINT8_DYN_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFTXSIGINFO"/><Parameter name="COM_EXISTS_UINT8_DYN_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_UINT8_DYN_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_UINT8_DYN_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_UINT8_DYN_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ZEROBIT_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFTXSIGINFO is deactivated, COM_EXISTS_ZEROBIT_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFTXSIGINFO"/><Parameter name="COM_EXISTS_ZEROBIT_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ZEROBIT_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_ZEROBIT_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_ZEROBIT_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_FLOAT32_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFTXSIGINFO is deactivated, COM_EXISTS_FLOAT32_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFTXSIGINFO"/><Parameter name="COM_EXISTS_FLOAT32_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_FLOAT32_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_FLOAT32_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_FLOAT32_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_FLOAT64_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_APPLTYPEOFTXSIGINFO is deactivated, COM_EXISTS_FLOAT64_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_APPLTYPEOFTXSIGINFO"/><Parameter name="COM_EXISTS_FLOAT64_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_FLOAT64_APPLTYPEOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_FLOAT64_APPLTYPEOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_FLOAT64_APPLTYPEOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_BITLENGTHOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_BITLENGTHOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_BITLENGTHOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_BITPOSITIONOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_BITPOSITIONOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_BITPOSITIONOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBIT_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFTXSIGINFO is deactivated, COM_EXISTS_NBIT_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFTXSIGINFO"/><Parameter name="COM_EXISTS_NBIT_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBIT_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_NBIT_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_NBIT_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_BYTE_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFTXSIGINFO is deactivated, COM_EXISTS_BYTE_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFTXSIGINFO"/><Parameter name="COM_EXISTS_BYTE_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_BYTE_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_BYTE_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_BYTE_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBYTE_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFTXSIGINFO is deactivated, COM_EXISTS_NBYTE_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFTXSIGINFO"/><Parameter name="COM_EXISTS_NBYTE_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBYTE_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_NBYTE_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_NBYTE_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBYTE_SW_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFTXSIGINFO is deactivated, COM_EXISTS_NBYTE_SW_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFTXSIGINFO"/><Parameter name="COM_EXISTS_NBYTE_SW_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBYTE_SW_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_NBYTE_SW_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_NBYTE_SW_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBITNBYTE_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFTXSIGINFO is deactivated, COM_EXISTS_NBITNBYTE_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFTXSIGINFO"/><Parameter name="COM_EXISTS_NBITNBYTE_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBITNBYTE_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_NBITNBYTE_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_NBITNBYTE_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBITNBYTE_SW_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFTXSIGINFO is deactivated, COM_EXISTS_NBITNBYTE_SW_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFTXSIGINFO"/><Parameter name="COM_EXISTS_NBITNBYTE_SW_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NBITNBYTE_SW_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_NBITNBYTE_SW_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_NBITNBYTE_SW_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ARRAY_BASED_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_BUSACCOFTXSIGINFO is deactivated, COM_EXISTS_ARRAY_BASED_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_BUSACCOFTXSIGINFO"/><Parameter name="COM_EXISTS_ARRAY_BASED_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_ARRAY_BASED_BUSACCOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXSIGINFO is deactivated, COM_EXISTS_ARRAY_BASED_BUSACCOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_EXISTS_ARRAY_BASED_BUSACCOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_BYTELENGTHOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_BYTELENGTHOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_BYTELENGTHOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_BYTEPOSITIONOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_BYTEPOSITIONOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_BYTEPOSITIONOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDINVVALUEENDIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_CONSTVALUEARRAYBASEDINVVALUEENDIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDINVVALUEENDIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDINVVALUELENGTHOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_CONSTVALUEARRAYBASEDINVVALUELENGTHOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDINVVALUELENGTHOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDINVVALUESTARTIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_CONSTVALUEARRAYBASEDINVVALUESTARTIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDINVVALUESTARTIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_CONSTVALUEARRAYBASEDINVVALUEUSEDOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_CONSTVALUEARRAYBASEDINVVALUEUSEDOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_CONSTVALUEARRAYBASEDINVVALUEUSEDOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_FILTERINFOIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_FILTERINFOIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_FILTERINFOIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_FILTERINFOUSEDOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_FILTERINFOUSEDOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_FILTERINFOUSEDOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_FILTERINITVALUEIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_FILTERINITVALUEIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_FILTERINITVALUEIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_FILTERINITVALUEUSEDOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_FILTERINITVALUEUSEDOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_FILTERINITVALUEUSEDOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_INVVALUEIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_INVVALUEIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_INVVALUEIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_INVVALUEUSEDOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_INVVALUEUSEDOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_INVVALUEUSEDOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_INVALIDHNDOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_INVALIDHNDOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_INVALIDHNDOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_ONCHANGEIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_ONCHANGEIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_ONCHANGEIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_ONCHANGEUSEDOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_ONCHANGEUSEDOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_ONCHANGEUSEDOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_STARTBYTEINPDUPOSITIONOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_STARTBYTEINPDUPOSITIONOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_STARTBYTEINPDUPOSITIONOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TRIGGEREDOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TRIGGEREDOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TRIGGEREDOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERENDIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXBUFFERENDIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXBUFFERENDIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERLENGTHOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXBUFFERLENGTHOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXBUFFERLENGTHOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERSTARTIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXBUFFERSTARTIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXBUFFERSTARTIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERUBIDXINTXBUFFERIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXBUFFERUBIDXINTXBUFFERIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXBUFFERUBIDXINTXBUFFERIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERUBIDXINTXBUFFERUSEDOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXBUFFERUBIDXINTXBUFFERUSEDOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXBUFFERUBIDXINTXBUFFERUSEDOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERUSEDOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXBUFFERUSEDOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXBUFFERUSEDOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTERINITSTATEIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXFILTERINITSTATEIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXFILTERINITSTATEIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTERINITSTATEUSEDOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXFILTERINITSTATEUSEDOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXFILTERINITSTATEUSEDOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUEENDIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUEENDIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUEENDIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUELENGTHOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUELENGTHOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUELENGTHOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUESTARTIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUESTARTIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUESTARTIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUEUSEDOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUEUSEDOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXFILTERINITVALUEARRAYBASEDFILTERINITVALUEUSEDOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINFOIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXPDUINFOIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXPDUINFOIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPINFOIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXSIGGRPINFOIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXSIGGRPINFOIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGGRPINFOUSEDOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_TXSIGGRPINFOUSEDOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_TXSIGGRPINFOUSEDOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_UBMASKIDXOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_UBMASKIDXOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_UBMASKIDXOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_UBMASKUSEDOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_UBMASKUSEDOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_UBMASKUSEDOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXSIGINFO = &quot;STD_OFF&quot;) =&gt; (COM_WITHOUTREPOFTXSIGINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXSIGINFO is deactivated, COM_WITHOUTREPOFTXSIGINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGINFO"/><Parameter name="COM_WITHOUTREPOFTXSIGINFO"/></Parameters></Constraint><Constraint text="(COM_TXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKTXTOUTFUNCPTRINDENDIDXOFTXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTOUTINFO is deactivated, COM_CBKTXTOUTFUNCPTRINDENDIDXOFTXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_TXTOUTINFO"/><Parameter name="COM_CBKTXTOUTFUNCPTRINDENDIDXOFTXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_TXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKTXTOUTFUNCPTRINDSTARTIDXOFTXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTOUTINFO is deactivated, COM_CBKTXTOUTFUNCPTRINDSTARTIDXOFTXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_TXTOUTINFO"/><Parameter name="COM_CBKTXTOUTFUNCPTRINDSTARTIDXOFTXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_TXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_CBKTXTOUTFUNCPTRINDUSEDOFTXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTOUTINFO is deactivated, COM_CBKTXTOUTFUNCPTRINDUSEDOFTXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_TXTOUTINFO"/><Parameter name="COM_CBKTXTOUTFUNCPTRINDUSEDOFTXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_TXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_FACTOROFTXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTOUTINFO is deactivated, COM_FACTOROFTXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_TXTOUTINFO"/><Parameter name="COM_FACTOROFTXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_TXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_MODEOFTXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTOUTINFO is deactivated, COM_MODEOFTXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_TXTOUTINFO"/><Parameter name="COM_MODEOFTXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_MODEOFTXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NORMAL_MODEOFTXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_MODEOFTXTOUTINFO is deactivated, COM_EXISTS_NORMAL_MODEOFTXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_MODEOFTXTOUTINFO"/><Parameter name="COM_EXISTS_NORMAL_MODEOFTXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_TXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NORMAL_MODEOFTXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXTOUTINFO is deactivated, COM_EXISTS_NORMAL_MODEOFTXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_TXTOUTINFO"/><Parameter name="COM_EXISTS_NORMAL_MODEOFTXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_MODEOFTXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_MODEOFTXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_MODEOFTXTOUTINFO is deactivated, COM_EXISTS_NONE_MODEOFTXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_MODEOFTXTOUTINFO"/><Parameter name="COM_EXISTS_NONE_MODEOFTXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_TXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_EXISTS_NONE_MODEOFTXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the data element COM_TXTOUTINFO is deactivated, COM_EXISTS_NONE_MODEOFTXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_TXTOUTINFO"/><Parameter name="COM_EXISTS_NONE_MODEOFTXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_TXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUINFOIDXOFTXTOUTINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTOUTINFO is deactivated, COM_TXPDUINFOIDXOFTXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_TXTOUTINFO"/><Parameter name="COM_TXPDUINFOIDXOFTXTOUTINFO"/></Parameters></Constraint><Constraint text="(COM_TXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_BUFFERSIZEOFTXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTPINFO is deactivated, COM_BUFFERSIZEOFTXTPINFO is deactivated.--><Parameters><Parameter name="COM_TXTPINFO"/><Parameter name="COM_BUFFERSIZEOFTXTPINFO"/></Parameters></Constraint><Constraint text="(COM_TXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_DYNAMICINITIALLENGTHOFTXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTPINFO is deactivated, COM_DYNAMICINITIALLENGTHOFTXTPINFO is deactivated.--><Parameters><Parameter name="COM_TXTPINFO"/><Parameter name="COM_DYNAMICINITIALLENGTHOFTXTPINFO"/></Parameters></Constraint><Constraint text="(COM_TXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERENDIDXOFTXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTPINFO is deactivated, COM_TXBUFFERENDIDXOFTXTPINFO is deactivated.--><Parameters><Parameter name="COM_TXTPINFO"/><Parameter name="COM_TXBUFFERENDIDXOFTXTPINFO"/></Parameters></Constraint><Constraint text="(COM_TXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERLENGTHOFTXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTPINFO is deactivated, COM_TXBUFFERLENGTHOFTXTPINFO is deactivated.--><Parameters><Parameter name="COM_TXTPINFO"/><Parameter name="COM_TXBUFFERLENGTHOFTXTPINFO"/></Parameters></Constraint><Constraint text="(COM_TXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERSTARTIDXOFTXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTPINFO is deactivated, COM_TXBUFFERSTARTIDXOFTXTPINFO is deactivated.--><Parameters><Parameter name="COM_TXTPINFO"/><Parameter name="COM_TXBUFFERSTARTIDXOFTXTPINFO"/></Parameters></Constraint><Constraint text="(COM_TXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXBUFFERUSEDOFTXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTPINFO is deactivated, COM_TXBUFFERUSEDOFTXTPINFO is deactivated.--><Parameters><Parameter name="COM_TXTPINFO"/><Parameter name="COM_TXBUFFERUSEDOFTXTPINFO"/></Parameters></Constraint><Constraint text="(COM_TXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGINFODYNSIGIDXOFTXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTPINFO is deactivated, COM_TXSIGINFODYNSIGIDXOFTXTPINFO is deactivated.--><Parameters><Parameter name="COM_TXTPINFO"/><Parameter name="COM_TXSIGINFODYNSIGIDXOFTXTPINFO"/></Parameters></Constraint><Constraint text="(COM_TXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSIGINFODYNSIGUSEDOFTXTPINFO = &quot;STD_OFF&quot;)"><!--If the structure COM_TXTPINFO is deactivated, COM_TXSIGINFODYNSIGUSEDOFTXTPINFO is deactivated.--><Parameters><Parameter name="COM_TXTPINFO"/><Parameter name="COM_TXSIGINFODYNSIGUSEDOFTXTPINFO"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CURRENTTXMODE = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_CURRENTTXMODE is deactivated, if COM_TXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CURRENTTXMODE"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CYCLETIMECNT = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_CYCLETIMECNT is deactivated, if COM_TXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CYCLETIMECNT"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_CYCLICSENDREQUEST = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_CYCLICSENDREQUEST is deactivated, if COM_TXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_CYCLICSENDREQUEST"/></Parameters></Constraint><Constraint text="(COM_DEFERREDGWMAPPINGINFO = &quot;STD_OFF&quot;) =&gt; (COM_DEFERREDGWMAPPINGEVENT = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_DEFERREDGWMAPPINGEVENT is deactivated, if COM_DEFERREDGWMAPPINGINFO is deactivated.--><Parameters><Parameter name="COM_DEFERREDGWMAPPINGINFO"/><Parameter name="COM_DEFERREDGWMAPPINGEVENT"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_DELAYTIMECNT = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_DELAYTIMECNT is deactivated, if COM_TXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_DELAYTIMECNT"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_DIRECTTRIGGER = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_DIRECTTRIGGER is deactivated, if COM_TXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_DIRECTTRIGGER"/></Parameters></Constraint><Constraint text="(COM_GWINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWEVENT = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_GWEVENT is deactivated, if COM_GWINFO is deactivated.--><Parameters><Parameter name="COM_GWINFO"/><Parameter name="COM_GWEVENT"/></Parameters></Constraint><Constraint text="(COM_GWTIMEOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_GWROUTINGTIMEOUTCOUNTER = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_GWROUTINGTIMEOUTCOUNTER is deactivated, if COM_GWTIMEOUTINFO is deactivated.--><Parameters><Parameter name="COM_GWTIMEOUTINFO"/><Parameter name="COM_GWROUTINGTIMEOUTCOUNTER"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_HANDLETXPDUDEFERRED = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_HANDLETXPDUDEFERRED is deactivated, if COM_TXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_HANDLETXPDUDEFERRED"/></Parameters></Constraint><Constraint text="(COM_IPDUGROUPINFO = &quot;STD_OFF&quot;) =&gt; (COM_IPDUGROUPSTATE = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_IPDUGROUPSTATE is deactivated, if COM_IPDUGROUPINFO is deactivated.--><Parameters><Parameter name="COM_IPDUGROUPINFO"/><Parameter name="COM_IPDUGROUPSTATE"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_REPCNT = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_REPCNT is deactivated, if COM_TXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_REPCNT"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_REPCYCLECNT = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_REPCYCLECNT is deactivated, if COM_TXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_REPCYCLECNT"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXDYNSIGNALLENGTH = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_RXDYNSIGNALLENGTH is deactivated, if COM_RXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXDYNSIGNALLENGTH"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXDYNSIGNALTMPLENGTHFORSIGNALGROUPS = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_RXDYNSIGNALTMPLENGTHFORSIGNALGROUPS is deactivated, if COM_RXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXDYNSIGNALTMPLENGTHFORSIGNALGROUPS"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXPDUDMSTATE = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_RXPDUDMSTATE is deactivated, if COM_RXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_RXPDUDMSTATE"/></Parameters></Constraint><Constraint text="(COM_RXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXPDUGRPACTIVE = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_RXPDUGRPACTIVE is deactivated, if COM_RXPDUINFO is deactivated.--><Parameters><Parameter name="COM_RXPDUINFO"/><Parameter name="COM_RXPDUGRPACTIVE"/></Parameters></Constraint><Constraint text="(COM_RXTOUTINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTOUTCNT = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_RXTOUTCNT is deactivated, if COM_RXTOUTINFO is deactivated.--><Parameters><Parameter name="COM_RXTOUTINFO"/><Parameter name="COM_RXTOUTCNT"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTPCONNECTIONSTATE = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_RXTPCONNECTIONSTATE is deactivated, if COM_RXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXTPCONNECTIONSTATE"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTPSDULENGTH = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_RXTPSDULENGTH is deactivated, if COM_RXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXTPSDULENGTH"/></Parameters></Constraint><Constraint text="(COM_RXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_RXTPWRITTENBYTESCOUNTER = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_RXTPWRITTENBYTESCOUNTER is deactivated, if COM_RXTPINFO is deactivated.--><Parameters><Parameter name="COM_RXTPINFO"/><Parameter name="COM_RXTPWRITTENBYTESCOUNTER"/></Parameters></Constraint><Constraint text="(COM_TXSIGGRPINFO = &quot;STD_OFF&quot;) =&gt; (COM_SIGGRPEVENTFLAG = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_SIGGRPEVENTFLAG is deactivated, if COM_TXSIGGRPINFO is deactivated.--><Parameters><Parameter name="COM_TXSIGGRPINFO"/><Parameter name="COM_SIGGRPEVENTFLAG"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TRANSMITREQUEST = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TRANSMITREQUEST is deactivated, if COM_TXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TRANSMITREQUEST"/></Parameters></Constraint><Constraint text="(COM_TXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXDYNSIGNALLENGTH = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXDYNSIGNALLENGTH is deactivated, if COM_TXTPINFO is deactivated.--><Parameters><Parameter name="COM_TXTPINFO"/><Parameter name="COM_TXDYNSIGNALLENGTH"/></Parameters></Constraint><Constraint text="(COM_TXFILTERINITVALUEARRAYBASED = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTEROLDVALUEARRAYBASED = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXFILTEROLDVALUEARRAYBASED is deactivated, if COM_TXFILTERINITVALUEARRAYBASED is deactivated.--><Parameters><Parameter name="COM_TXFILTERINITVALUEARRAYBASED"/><Parameter name="COM_TXFILTEROLDVALUEARRAYBASED"/></Parameters></Constraint><Constraint text="(COM_TXFILTERINITVALUEFLOAT32 = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTEROLDVALUEFLOAT32 = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXFILTEROLDVALUEFLOAT32 is deactivated, if COM_TXFILTERINITVALUEFLOAT32 is deactivated.--><Parameters><Parameter name="COM_TXFILTERINITVALUEFLOAT32"/><Parameter name="COM_TXFILTEROLDVALUEFLOAT32"/></Parameters></Constraint><Constraint text="(COM_TXFILTERINITVALUEFLOAT64 = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTEROLDVALUEFLOAT64 = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXFILTEROLDVALUEFLOAT64 is deactivated, if COM_TXFILTERINITVALUEFLOAT64 is deactivated.--><Parameters><Parameter name="COM_TXFILTERINITVALUEFLOAT64"/><Parameter name="COM_TXFILTEROLDVALUEFLOAT64"/></Parameters></Constraint><Constraint text="(COM_TXFILTERINITVALUESINT16 = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTEROLDVALUESINT16 = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXFILTEROLDVALUESINT16 is deactivated, if COM_TXFILTERINITVALUESINT16 is deactivated.--><Parameters><Parameter name="COM_TXFILTERINITVALUESINT16"/><Parameter name="COM_TXFILTEROLDVALUESINT16"/></Parameters></Constraint><Constraint text="(COM_TXFILTERINITVALUESINT32 = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTEROLDVALUESINT32 = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXFILTEROLDVALUESINT32 is deactivated, if COM_TXFILTERINITVALUESINT32 is deactivated.--><Parameters><Parameter name="COM_TXFILTERINITVALUESINT32"/><Parameter name="COM_TXFILTEROLDVALUESINT32"/></Parameters></Constraint><Constraint text="(COM_TXFILTERINITVALUESINT64 = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTEROLDVALUESINT64 = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXFILTEROLDVALUESINT64 is deactivated, if COM_TXFILTERINITVALUESINT64 is deactivated.--><Parameters><Parameter name="COM_TXFILTERINITVALUESINT64"/><Parameter name="COM_TXFILTEROLDVALUESINT64"/></Parameters></Constraint><Constraint text="(COM_TXFILTERINITVALUESINT8 = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTEROLDVALUESINT8 = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXFILTEROLDVALUESINT8 is deactivated, if COM_TXFILTERINITVALUESINT8 is deactivated.--><Parameters><Parameter name="COM_TXFILTERINITVALUESINT8"/><Parameter name="COM_TXFILTEROLDVALUESINT8"/></Parameters></Constraint><Constraint text="(COM_TXFILTERINITVALUEUINT16 = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTEROLDVALUEUINT16 = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXFILTEROLDVALUEUINT16 is deactivated, if COM_TXFILTERINITVALUEUINT16 is deactivated.--><Parameters><Parameter name="COM_TXFILTERINITVALUEUINT16"/><Parameter name="COM_TXFILTEROLDVALUEUINT16"/></Parameters></Constraint><Constraint text="(COM_TXFILTERINITVALUEUINT32 = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTEROLDVALUEUINT32 = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXFILTEROLDVALUEUINT32 is deactivated, if COM_TXFILTERINITVALUEUINT32 is deactivated.--><Parameters><Parameter name="COM_TXFILTERINITVALUEUINT32"/><Parameter name="COM_TXFILTEROLDVALUEUINT32"/></Parameters></Constraint><Constraint text="(COM_TXFILTERINITVALUEUINT64 = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTEROLDVALUEUINT64 = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXFILTEROLDVALUEUINT64 is deactivated, if COM_TXFILTERINITVALUEUINT64 is deactivated.--><Parameters><Parameter name="COM_TXFILTERINITVALUEUINT64"/><Parameter name="COM_TXFILTEROLDVALUEUINT64"/></Parameters></Constraint><Constraint text="(COM_TXFILTERINITVALUEUINT8 = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTEROLDVALUEUINT8 = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXFILTEROLDVALUEUINT8 is deactivated, if COM_TXFILTERINITVALUEUINT8 is deactivated.--><Parameters><Parameter name="COM_TXFILTERINITVALUEUINT8"/><Parameter name="COM_TXFILTEROLDVALUEUINT8"/></Parameters></Constraint><Constraint text="(COM_TXFILTERINITSTATE = &quot;STD_OFF&quot;) =&gt; (COM_TXFILTERSTATE = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXFILTERSTATE is deactivated, if COM_TXFILTERINITSTATE is deactivated.--><Parameters><Parameter name="COM_TXFILTERINITSTATE"/><Parameter name="COM_TXFILTERSTATE"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXPDUGRPACTIVE = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXPDUGRPACTIVE is deactivated, if COM_TXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXPDUGRPACTIVE"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXSDULENGTH = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXSDULENGTH is deactivated, if COM_TXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_TXSDULENGTH"/></Parameters></Constraint><Constraint text="(COM_TXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXTMPTPPDULENGTH = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXTMPTPPDULENGTH is deactivated, if COM_TXTPINFO is deactivated.--><Parameters><Parameter name="COM_TXTPINFO"/><Parameter name="COM_TXTMPTPPDULENGTH"/></Parameters></Constraint><Constraint text="(COM_TXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXTPCONNECTIONSTATE = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXTPCONNECTIONSTATE is deactivated, if COM_TXTPINFO is deactivated.--><Parameters><Parameter name="COM_TXTPINFO"/><Parameter name="COM_TXTPCONNECTIONSTATE"/></Parameters></Constraint><Constraint text="(COM_TXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXTPSDULENGTH = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXTPSDULENGTH is deactivated, if COM_TXTPINFO is deactivated.--><Parameters><Parameter name="COM_TXTPINFO"/><Parameter name="COM_TXTPSDULENGTH"/></Parameters></Constraint><Constraint text="(COM_TXTPINFO = &quot;STD_OFF&quot;) =&gt; (COM_TXTPWRITTENBYTESCOUNTER = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_TXTPWRITTENBYTESCOUNTER is deactivated, if COM_TXTPINFO is deactivated.--><Parameters><Parameter name="COM_TXTPINFO"/><Parameter name="COM_TXTPWRITTENBYTESCOUNTER"/></Parameters></Constraint><Constraint text="(COM_TXPDUINFO = &quot;STD_OFF&quot;) =&gt; (COM_WAITINGFORCONFIRMATION = &quot;STD_OFF&quot;)"><!--Based on a size relation COM_WAITINGFORCONFIRMATION is deactivated, if COM_TXPDUINFO is deactivated.--><Parameters><Parameter name="COM_TXPDUINFO"/><Parameter name="COM_WAITINGFORCONFIRMATION"/></Parameters></Constraint></Constraints></System>
