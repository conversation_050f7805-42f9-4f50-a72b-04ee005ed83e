<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>StartApplication</title>
    <style type="text/css">
      
       
      
      @media screen
      {
        body
        {
          background-color: rgb(255, 255, 255);
          font-family:      arial, verdana, trebuchet, officina, sans-serif;
        }
      }
  
      
      @media print
      {
        
        body
        {
          background-color: rgb(255, 255, 255);
          font-family:      arial, verdana, trebuchet, officina, sans-serif;
        }
        
        
        a
        {
          text-decoration: none;
        }
        
        
        .HeadingTable, .SubHeadingTable, .GroupHeadingTable , .GroupEndTable
        {
          border-width: 2px;
          border-style: solid;
          border-color: rgb(150,150,150);
        }
        
        .ResultBlockHeading
        {
          border-width: 1px;
          border-style: solid;
          border-color: rgb(150,150,150);
        }
        
        table.ResultTable, table.OverviewTable, table.CheckStatisticsTable
        {
          border-collapse: collapse;
        }
        
        td.DefaultCell, td.DefineCell, td.NumberCell, td.NumberCellPositiveResult, td.NumberCellNegativeResult, 
        td.NumberCellErrorResult, td.NumberCellInconclusiveResult, td.NumberCellNoneResult, td.NumberCellNoColor,
        td.PositiveResultCell, td.NegativeResultCell, td.WarningCell, td.NoneResultCell, td.InconclusiveResultCell,
        td.ErrorResultCell, th.TableHeadingCell
        {
          border-width: 1px;
          border-style: solid;
          border-color: rgb(150,150,150);
        }
        
        .PatternSeparator
        {
          
          border-style: none;
        }
        
        .PositiveResult
        {
          
          border-bottom: 2px solid;
        }
        
        .NegativeResult
        {
          
          border-bottom: 2px solid;
        }
        
        .NoneResult
        {
          
          border-bottom: 2px solid;
        }
        
        .InconclusiveResult
        {
          
          border-bottom: 2px solid;
        }
        
        .ErrorResult
        {
          
          border-bottom: 2px solid;
        }

        
        .TestcaseHeadingDefaultResult
        {
          
          border-bottom: 2px solid;
        }
        
        .TestcaseHeadingPositiveResult
        {
          
          border-bottom: 2px solid;
        }
        
        .TestcaseHeadingNegativeResult
        {
          
          border-bottom: 2px solid;
        }
        
        table.ScreenOnly, a.Undecorated, .LinkCell
        {
          display: none;
        }
      }
      
      a.Undecorated:hover 
      {
        color:red;
      }
      a.Undecorated
      { 
        text-decoration: none; 
      }
      
      .NavStyle
      {
        background-color: rgb(220, 221, 222);
      }
      
      td.LinkCell, td.UpCell
      {
        
        width:              1px;
        white-space:        nowrap;
      }

      
      table
      {
        margin-bottom:    10px;
        vertical-align:   top; 
        border:           0;
        border-spacing:   1px;
        padding:          2px;
      }
      table.ScreenOnly
      {
        vertical-align: top; 
        margin-bottom:  10px;
      }
      .NoMarginBottom
      {
        margin-bottom:    0px;
      }
      .HeadingTable
      {
        width:            100%;
        text-align:       center;
        margin-top:       15px;
        margin-bottom:    30px;
        margin-left:      auto;
        margin-right:     auto;
        background-color: rgb(220, 221, 222);
      }
      
      .SubHeadingTable
      {
        width:            100%;
        text-align:       center;
        margin-top:       15px;
        margin-bottom:    25px;
        background-color: rgb(220, 221, 222);
      }
      
      .GroupHeadingTable, .GroupEndTable
      {
        width:            100%;
        text-align:       center;
        margin-top:       15px;
        padding-right:    20px;
        background-color: rgb(220, 221, 222);
      }
      
      .DefaultTable
      {
        padding:        0px;
        border-spacing: 0px;
      }
      
      .CheckStatisticsTable
      {
        margin-bottom: 8px;
        padding-right:  20px;
      }
  
      .OverviewTable
      {
        text-align:     left;
      }
      
      .OverviewResultTable
      {
        width:          100%;
        margin-bottom:  0px;
        border-spacing: 0px;
        padding:        0px;
        text-align:     center;
      }
      
      .ResultTable
      {
        width:          100%;
        text-align:     left;
        padding-right:  20px;
      }
      
      .ResultTableInner
      {
        width:          100%;
        text-align:     left;
        padding-right:  0px;
      }
  
      table.ResultTable td, table.ChildLinkTable td
      {
        vertical-align: top;
      }
      .ChildLinkTable
      {
        width:          1px;
        text-align:     left;
      }
      .InfoTable
      {
        text-align:     left;
        padding-right:  0px;
        margin-bottom:  0px;
        border-spacing: 0px;
      }
      .InfoTableExpand
      {
        width:          100%;
        text-align:     left;
        padding-right:  0px;
        margin-bottom:  0px;
        border-spacing: 0px;
      }
      .OverallResultTable
      {
        width:         50%;
        text-align:    center;
        margin-bottom: 20px;
        margin-left:   auto;
        margin-right:  auto;
        font-weight:   bold;
      }
      
      .TableHeadingCell
      {
        background-color: rgb(179, 179, 179);
        font-family:      arial, verdana, trebuchet, officina, sans-serif;
        font-weight:      bold;
        text-align:       center;
      }
  
      .CellNoColor
      {
        text-align:     left;
        vertical-align: top;
      }   
      
      .DefineCell
      {
        background-color: rgb(179, 179, 179);
        text-align:       left;
      }
      
      .DefaultCell, .DefaultCellBold
      {
        background-color: rgb(229, 229, 229);
        text-align:       left;
      }
      
      .DefaultCellBold
      {
        font-weight:      bold;
      }
        
      .NumberCell
      {
        background-color: rgb(229, 229, 229);
        text-align:       center;
        min-width:        50px;
      }
      
      .NumberCellNegativeResult
      {
        background-color: rgb(240, 0, 0);
        text-align:       center;
        min-width:        50px;
      }
      
      .NumberCellPositiveResult
      {
        background-color: rgb(25, 165, 88);
        text-align:       center;
        min-width:        50px;
      }
      
      .NumberCellNoneResult
      {
        background-color: rgb(105, 105, 105);
        text-align:       center;
        min-width:        50px;
      }
      
      .NumberCellInconclusiveResult
      {
        background-color: rgb(255, 165, 0);
        text-align:       center;
        min-width:        50px;
      }
      
      .NumberCellErrorResult
      {
        background-color: rgb(205, 0, 0);
        text-align:       center;
        min-width:        50px;
      }
      
      .NumberCellNoColor
      {        
        text-align: center;
      }
      
      .PositiveResultCell
      {
        background-color: rgb(25, 165, 88);
      }
    
      .NegativeResultCell
      {
        background-color: rgb(240, 0, 0);
      }
      
      .NoneResultCell
      {
        background-color: rgb(105, 105, 105);
      }
      
      .InconclusiveResultCell
      {
        background-color: rgb(255, 165, 0);
      }
      
      .ErrorResultCell
      {
        background-color: rgb(205, 0, 0);
        white-space:        nowrap;
      }

      .WarningCell
      {
        background-color: rgb(255, 255, 0);
      }

      .TestcaseHeadingDefaultResult
      {
        background-color: rgb(179, 179, 179);
      }
      .TestcaseHeadingPositiveResult
      {
        background-color: rgb(25, 165, 88);
      }      
      .TestcaseHeadingNegativeResult
      {
        background-color: rgb(240, 0, 0);
      }      
      .TestcaseHeadingNoneResult
      {
        background-color: rgb(105, 105, 105);
      }
      .TestcaseHeadingInconclusiveResult
      {
        background-color: rgb(255, 165, 0);
      }
      .TestcaseHeadingErrorResult
      {
        background-color: rgb(205, 0, 0);
      }
      
      .PatternSeparator
      {
        height: 0.2em;
      }
      
      .NegativeResult
      {
        background-color: rgb(240, 0, 0);
        text-align:       center;
        font-weight:      bold;
      }
      .PositiveResult
      {
        background-color: rgb(25, 165, 88);
        text-align:       center;
        font-weight:      bold;
      }
      .NoneResult
      {
        background-color: rgb(105, 105, 105);
        text-align:       center;
        font-weight:      bold;
      }
      .InconclusiveResult
      {
        background-color: rgb(255, 165, 0);
        text-align:       center;
        font-weight:      bold;
      }
      .ErrorResult
      {
        background-color: rgb(205, 0, 0);
        text-align:       center;
        font-weight:      bold;
      }
      
      
      
      .Heading1
      {
        font-family: arial, sans-serif;
        font-weight: bold;
        font-size:   26px;
      }
      
      .Heading2
      {
        font-family: arial, sans-serif;
        font-weight: bold;
        font-size:   20px;
      }
      
      .Heading3
      {
        font-family:   arial, sans-serif;
        font-weight:   bold;
        font-size:     18px;
        margin-bottom: 20px;
        margin-top:    20px;
      }
      
      .Heading4
      {
        font-family:   arial, sans-serif;
        font-weight:   bold;
        font-size:     16px;
        margin-top:    10px;
        margin-bottom: 10px;
      }
      
      
      img
      {
        Margin-bottom: 10px;
        Margin-right:  10px;
      }
      
      
      p
      {
        Margin-bottom: 20px;
      }
      
      
      .DefaultTableBackground
      {
        background-color: rgb(229, 229, 229);
        border-spacing:   0px;
        padding:          0px;
      }
      
      .NegativeResultBackground
      {
        background-color: rgb(240, 0, 0);
        border-spacing:   0px;
        padding:          0px;
      }
      
      .PositiveResultBackground
      {
        background-color: rgb(25, 165, 88);
        border-spacing:   0px;
        padding:          0px;
      }
      
      .WarningResultBackground
      {
        background-color: rgb(255, 255, 0);
        border-spacing:   0px;
        padding:          0px;
      }
      
      .NoneResultBackground
      {
        background-color: rgb(105, 105, 105);
        border-spacing:   0px;
        padding:          0px;
      }
      
      .InconclusiveResultBackground
      {
        background-color: rgb(255, 165, 0);
        border-spacing:   0px;
        padding:          0px;
      }
      
      .ErrorResultBackground
      {
        background-color: rgb(205, 0, 0);
        border-spacing:   0px;
        padding:          0px;
      }
      
      .TestGroupHeadingBackground
      {
        background-color: rgb(179, 179, 179);
      }
    
      
      .Indentation
      {
        margin-left:  20px;
      }
    </style>
    <script type="text/javascript">
      var EXPANDED = '[−]';
      var COLLAPSED = '[+]';
      var VISIBLE = 'Visible';
      var HIDDEN = 'Hidden';
      var NONE = 'none';
      var BLOCK = '';
      function getDivId(id) { return 'div_' + id; }
      function getLinkId(id) { return 'lnk_' + id; }
      function getTableId(id) { return 'tbl_' + id; }
      function getTbl2Id(id) { return 'tbl_t' + id; }
      
      
      function switchText(oldText) { return oldText == COLLAPSED ? EXPANDED : COLLAPSED ; }
      function getStyleFromText(linkText) { return linkText == COLLAPSED ? HIDDEN : VISIBLE; }
      
      function getElements(tagName, elementId)
      {
        this.elements = [];
        var index = 0;
        var elm = document.getElementsByTagName(tagName);
        var len = elm.length;
        for (var i = 0; i < len; i++) 
        {
          var id = elm[i].id;
          if (id && id.indexOf(elementId) == 0)
          {
            this.elements[index] = elm[i];
            index++;
          }
        }
        return this.elements;
      }
      function matchElements(tagName, matchId)
      {
        this.elements = [];
        var index = 0;
        var elm = document.getElementsByTagName(tagName);
        for (var i = 0; i < elm.length; i++) 
        {
          var id = elm[i].id;
          var tc = id.replace(/s[0-9]+\./, "");
          if (tc && tc == matchId)
          {
            this.elements[index] = elm[i];
            index++;
          }
        }
        return this.elements;  
      }
      function onoff(elm, style)
      {
        if(elm.style.display == NONE)
        {
          if(style == null || style == VISIBLE)
            elm.style.display = BLOCK;
        }
        else
        {
          if(style == null || style == HIDDEN)
            elm.style.display = NONE;
        }
      }
      function switchTagStyle(tag, id, style)
      {
        var divs = getElements(tag, id);
        for(var i = 0; i < divs.length; i++)
          onoff(divs[i], style);
      }
      function switchRowStyle(tbl, style)
      {
        var rows = tbl.rows.length;
        for(var rowIndex=0; rowIndex < rows; rowIndex++)
        {
          var row = tbl.rows[rowIndex];
          if (row.id != "on")
            onoff(row, style);
        }
      }
      function switchTableRowStyle(tableId, style)
      {
        var tables = getElements('table', tableId);
        for(var i = 0; i < tables.length; i++)
          switchRowStyle(tables[i], style);
      }
      function switchLinkText(linkId, newText)
      {
        var link = getElements('a', linkId);
        var len = link.length;
        for(var i = 0; i < len; i++)
        {
          var lnk = link[i];
          if(newText == null)
          {
            var oldText = lnk.text;
            newText = switchText(oldText)
          }
          lnk.text = newText;
        }
      }
      function switchTag(tag, id, text)
      {
        var style = getStyleFromText(text);
        switchTagStyle(tag, id, style);
      }
      function switchTable(id)
      {
        switchLinkText(getTableId(id));
        switchTableRowStyle(getTableId(id));
      }
      function switchDiv(id, linkText)
      {
        var text = switchText(linkText);
        switchLinkText(getLinkId(id), text);
        switchTag('div', getDivId(id), text);
      }      
      function switchAll(id, linkText)
      {
        var text = switchText(linkText);
        switchLinkText(getLinkId(id), text);
        switchTag('div', getDivId(id), text);
        var style = getStyleFromText(text);
        switchTableRowStyle(getTbl2Id(id), style);
      }
      function showTable(id)
      {
        var tableId = getTableId(id);
        switchLinkText(tableId, EXPANDED);
        switchTableRowStyle(tableId, VISIBLE);
      }
      function hideTable(id)
      {
        var tableId = getTableId(id);
        switchTableRowStyle(tableId, HIDDEN);
        switchLinkText(tableId, COLLAPSED);
      }
      function showDiv(id)
      {
        switchLinkText(getLinkId(id), EXPANDED);
        switchTag('div', getDivId(id), EXPANDED);
      }
      function showTC(id)
      {
        var divId = getDivId(id);
        var elms = matchElements('div', divId); 
        if(elms != null && elms.length > 0)
        {
          id = elms[0].id.replace(getDivId(''), '');
          showDiv(id);
        }
      }
      
    </script>
  </head>
  <body>
    <a name="TOP"></a>
    <table class="HeadingTable">
      <tr>
        <td>
          <big class="Heading1">Report: StartApplication</big>
        </td>
      </tr>
    </table>
    <center>
      <table class="OverallResultTable">
        <tr>
          <td class="PositiveResult">Test passed</td>
        </tr>
      </table>
    </center>
    <a name="GeneralTestInfo"></a>
    <table class="SubHeadingTable">
      <tr>
        <td>
          <div class="Heading2">General Test Information</div>
        </td>
      </tr>
    </table>
    <div class="Indentation">
      <div class="Heading4">Test Engineer</div>
      <div class="Indentation">
        <table>
          <tr>
            <td class="CellNoColor">Windows Login Name: </td>
            <td class="CellNoColor">visvga</td>
          </tr>
        </table>
      </div>
      <div class="Heading4">Test Setup</div>
      <div class="Indentation">
        <table>
          <tr>
            <td class="CellNoColor">Version: </td>
            <td class="CellNoColor">CANoe.ISO11783.J1939.CANaero.CANopen.LIN.MOST.FlexRay.J1587.Ethernet.Car2x.AFDX.A429.XCP.AMD.Scope.Sensor 10.0.63</td>
          </tr>
          <tr>
            <td class="CellNoColor">Configuration: </td>
            <td class="CellNoColor">D:\usr\usage\Delivery\CBD20x\CBD2000456\D00\internal\StartApplication\Canoe\StartApplication\StartApplication.cfg</td>
          </tr>
          <tr>
            <td class="CellNoColor">Configuration Comment: </td>
            <td class="CellNoColor"></td>
          </tr>
          <tr>
            <td class="CellNoColor">Database CAN on Vita_CAN0: </td>
            <td class="CellNoColor">D:\usr\usage\Delivery\CBD20x\CBD2000456\D00\internal\StartApplication\Config\System\SystemExtract.arxml (2020-08-24 13:28:40)</td>
          </tr>
          <tr>
            <td class="CellNoColor">Database LIN_MyECU on Vita_LIN0: </td>
            <td class="CellNoColor">D:\usr\usage\Delivery\CBD20x\CBD2000456\D00\internal\StartApplication\input_files\LIN_MyECU.ldf (2017-11-15 13:11:18)</td>
          </tr>
          <tr>
            <td class="CellNoColor">Database FlexRay on Vita_FR0: </td>
            <td class="CellNoColor">D:\usr\usage\Delivery\CBD20x\CBD2000456\D00\internal\StartApplication\Config\System\SystemExtract.arxml (2020-08-24 13:28:40)</td>
          </tr>
          <tr>
            <td class="CellNoColor">Test Module Name: </td>
            <td class="CellNoColor">TestScript</td>
          </tr>
          <tr>
            <td class="CellNoColor">Test Module File: </td>
            <td class="CellNoColor">D:\usr\usage\Delivery\CBD20x\CBD2000456\D00\internal\StartApplication\Appl\Source\Capl\StartApplicationTest.xml</td>
          </tr>
          <tr>
            <td class="CellNoColor">Last modification of Test Module File: </td>
            <td class="CellNoColor">2020-08-07, 11:09:56</td>
          </tr>
          <tr>
            <td class="CellNoColor">Windows Computer Name: </td>
            <td class="CellNoColor">VISVGA13778NBJ</td>
          </tr>
          <tr>
            <td class="CellNoColor">Nodelayer Module osek_tp: </td>
            <td class="CellNoColor">OSEK_TP (VC10, Version 5.27.54, Build 54), D:\usr\usage\Delivery\CBD20x\CBD2000456\D00\internal\StartApplication\Canoe\Exec32\osek_tp.dll</td>
          </tr>
          <tr>
            <td class="CellNoColor">Nodelayer Module J1939TestServiceLibraryNL: </td>
            <td class="CellNoColor">J1939 Test Service Library for CANoe, Version 10.0.63.0, C:\Program Files\Vector CANwin 10.0\Exec32\J1939TestServiceLibraryNL.dll</td>
          </tr>
        </table>
      </div>
      <div class="Heading4">Hardware</div>
      <div class="Indentation">
        <div class="Heading4">CAN Channel Interfaces</div>
        <div class="Indentation">
          <table>
            <tr>
              <th class="TableHeadingCell">Channel</th>
              <th class="TableHeadingCell">Device</th>
              <th class="TableHeadingCell">Serial number</th>
              <th class="TableHeadingCell">Driver DLL version</th>
              <th class="TableHeadingCell">Driver version</th>
            </tr>
            <tr>
              <td class="NumberCell">1</td>
              <td class="NumberCell">VN1630</td>
              <td class="NumberCell">504323</td>
              <td class="NumberCell">11.4.16</td>
              <td class="NumberCell">10.9.12</td>
            </tr>
          </table>
        </div>
      </div>
      <div class="Indentation">
        <div class="Heading4">LIN Channel Interfaces</div>
        <div class="Indentation">
          <table>
            <tr>
              <th class="TableHeadingCell">Channel</th>
              <th class="TableHeadingCell">Device</th>
              <th class="TableHeadingCell">Serial number</th>
              <th class="TableHeadingCell">Driver DLL version</th>
              <th class="TableHeadingCell">Driver version</th>
              <th class="TableHeadingCell">LIN Firmware version</th>
            </tr>
            <tr>
              <td class="NumberCell">1</td>
              <td class="NumberCell">VN1630</td>
              <td class="NumberCell">504323</td>
              <td class="NumberCell">11.4.16</td>
              <td class="NumberCell">10.9.12</td>
              <td class="NumberCell">1.77</td>
            </tr>
          </table>
        </div>
      </div>
      <div class="Indentation">
        <div class="Heading4">FlexRay Channel Interfaces</div>
        <div class="Indentation">
          <table>
            <tr>
              <th class="TableHeadingCell">Channel</th>
              <th class="TableHeadingCell">Device</th>
              <th class="TableHeadingCell">Serial number</th>
              <th class="TableHeadingCell">Driver DLL version</th>
              <th class="TableHeadingCell">Driver version</th>
            </tr>
            <tr>
              <td class="NumberCell">1</td>
              <td class="NumberCell">VN3600</td>
              <td class="NumberCell">2579</td>
              <td class="NumberCell">11.4.16</td>
              <td class="NumberCell">8.2.26</td>
            </tr>
          </table>
        </div>
      </div>
      <div class="Heading4">Please note</div>
      <div class="Indentation">
        <table>
          <tr>
            <td class="CellNoColor" colspan="2">EXAMPLE CODE ONLY<br>
              <br>                This Example Code is only intended for illustrating an example of a possible BSW integration and BSW<br>                configuration. The Example Code has not passed any quality control measures and may be incomplete. The<br>                Example Code is neither intended nor qualified for use in series production. The Example Code as well<br>                as any of its modifications and/or implementations must be tested with diligent care and must comply<br>                with all quality requirements which are necessary according to the state of the art before their use.<br>            </td>
          </tr>
        </table>
      </div>
    </div>
    <a name="TestOverview"></a>
    <table class="SubHeadingTable">
      <tr>
        <td>
          <div class="Heading2">Test Overview</div>
        </td>
      </tr>
    </table>
    <div class="Indentation">
      <table class="DefaultTable">
        <tr>
          <td style="padding-right: 0.5em;" class="CellNoColor">Test begin: </td>
          <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:31</td>
          <td class="CellNoColor">(logging timestamp   21.464461)</td>
        </tr>
        <tr>
          <td style="padding-right: 0.5em;" class="CellNoColor">Test end: </td>
          <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:03:15</td>
          <td class="CellNoColor" nowrap>(logging timestamp   64.625252)</td>
        </tr>
      </table>
      <div class="Heading4">Statistics</div>
      <div class="Indentation">
        <table class="OverviewTable">
          <tr>
            <td class="DefineCell">Overall number of test cases </td>
            <td class="NumberCell" width="60">12</td>
            <td class="DefaultCell"></td>
          </tr>
          <tr>
            <td class="DefineCell">Executed test cases </td>
            <td class="NumberCell">10</td>
            <td class="DefaultCell">83% of all test cases</td>
          </tr>
          <tr>
            <td class="DefineCell">Not executed test cases </td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">17% of all test cases</td>
          </tr>
          <tr>
            <td class="DefineCell">Test cases passed </td>
            <td class="NumberCellPositiveResult">10</td>
            <td class="NumberCellPositiveResult">100% of executed test cases</td>
          </tr>
          <tr>
            <td class="DefineCell">Test cases failed </td>
            <td class="NumberCell">0</td>
            <td class="NumberCell">0% of executed test cases</td>
          </tr>
        </table>
      </div>
      <div class="Heading4">Test Case Results</div>
      <div class="Indentation">
        <table class="OverviewTable">
          <tr>
            <td class="DefineCell">
              <b>1</b>
            </td>
            <td class="DefineCell">
              <b></b>
            </td>
            <td class="DefineCell" colspan="2">
              <a href="#i__1359961936_8">
                <b>Usecase COM</b>
              </a>
            </td>
          </tr>
          <tr>
            <td class="DefineCell">
              <b>1.1</b>
            </td>
            <td class="DefineCell">
              <b></b>
            </td>
            <td class="DefineCell" colspan="2">
              <a href="#i__1359961936_12">
                <b>CAN Data Transmission and Reception</b>
              </a>
            </td>
          </tr>
          <tr>
            <td class="DefineCell">1.1.1</td>
            <td class="DefaultCell">TCASE-394602</td>
            <td class="DefaultCell">
              <a href="#i__1359961936_16">CAN-FD Data Transmission ( Vita_CAN0 )</a>
            </td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="DefineCell">1.1.2</td>
            <td class="DefaultCell">TCASE-375706</td>
            <td class="DefaultCell">
              <a href="#i__1359961936_985">CAN Data Reception ( Vita_CAN0 )</a>
            </td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="DefineCell">
              <b>1.2</b>
            </td>
            <td class="DefineCell">
              <b></b>
            </td>
            <td class="DefineCell">
              <b>LIN Data Transmission and Reception</b>
            </td>
            <td class="DefineCell">not executed</td>
          </tr>
          <tr>
            <td class="DefineCell">
              <b>1.3</b>
            </td>
            <td class="DefineCell">
              <b></b>
            </td>
            <td class="DefineCell" colspan="2">
              <a href="#i__1359961936_1959">
                <b>FlexRay Data Transmission and Reception</b>
              </a>
            </td>
          </tr>
          <tr>
            <td class="DefineCell">1.3.1</td>
            <td class="DefaultCell">TCASE-375707</td>
            <td class="DefaultCell">
              <a href="#i__1359961936_1963">FlexRay Data Transmission ( Vita_FR0 )</a>
            </td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="DefineCell">1.3.2</td>
            <td class="DefaultCell">TCASE-375708</td>
            <td class="DefaultCell">
              <a href="#i__1359961936_2932">FlexRay Data Reception ( Vita_FR0 )</a>
            </td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="DefineCell">
              <b>2</b>
            </td>
            <td class="DefineCell">
              <b></b>
            </td>
            <td class="DefineCell" colspan="2">
              <a href="#i__1359961936_3858">
                <b>Usecase DIAG</b>
              </a>
            </td>
          </tr>
          <tr>
            <td class="DefineCell">
              <b>2.1</b>
            </td>
            <td class="DefineCell">
              <b></b>
            </td>
            <td class="DefineCell" colspan="2">
              <a href="#i__1359961936_3862">
                <b>Diagnostics over CAN</b>
              </a>
            </td>
          </tr>
          <tr>
            <td class="DefineCell">2.1.1</td>
            <td class="DefaultCell">TCASE-478915</td>
            <td class="DefaultCell">
              <a href="#i__1359961936_3866">Basic Request/Response via UDS over CAN ( Vita_CAN0 )</a>
            </td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="DefineCell">2.1.2</td>
            <td class="DefaultCell">TCASE-481982</td>
            <td class="DefaultCell">
              <a href="#i__1359961936_4502">Read Diagnostic Data via UDS over CAN ( Vita_CAN0 )</a>
            </td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="DefineCell">2.1.3</td>
            <td class="DefaultCell">TCASE-483095</td>
            <td class="DefaultCell">
              <a href="#i__1359961936_5613">Write Diagnostic Data via UDS over CAN ( Vita_CAN0 )</a>
            </td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="DefineCell">
              <b>2.2</b>
            </td>
            <td class="DefineCell">
              <b></b>
            </td>
            <td class="DefineCell" colspan="2">
              <a href="#i__1359961936_6530">
                <b>Diagnostic Event Handling</b>
              </a>
            </td>
          </tr>
          <tr>
            <td class="DefineCell">2.2.1</td>
            <td class="DefaultCell">TCASE-379331</td>
            <td class="DefaultCell">
              <a href="#i__1359961936_6534">Diagnostic Event Handling with FreezeFrame and without Debouncing ( Vita_CAN0 )</a>
            </td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="DefineCell">
              <b>3</b>
            </td>
            <td class="DefineCell">
              <b></b>
            </td>
            <td class="DefineCell" colspan="2">
              <a href="#i__1359961936_9094">
                <b>Usecase MEM</b>
              </a>
            </td>
          </tr>
          <tr>
            <td class="DefineCell">
              <b>3.1</b>
            </td>
            <td class="DefineCell">
              <b></b>
            </td>
            <td class="DefineCell" colspan="2">
              <a href="#i__1359961936_9098">
                <b>Write and Read Nonvolatile Memory</b>
              </a>
            </td>
          </tr>
          <tr>
            <td class="DefineCell">3.1.1</td>
            <td class="DefaultCell">TCASE-379342</td>
            <td class="DefaultCell">
              <a href="#i__1359961936_9102">Write and Read NV block (Block 1, FEE)</a>
            </td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="DefineCell">3.1.2</td>
            <td class="DefaultCell">TCASE-379342</td>
            <td class="DefaultCell">
              <a href="#i__1359961936_10517">Write and Read NV block (Block 2, FEE)</a>
            </td>
            <td class="PositiveResultCell">pass</td>
          </tr>
        </table>
      </div>
    </div>
    <a name="TestModuleInfo"></a>
    <table class="SubHeadingTable">
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_" href="javascript:switchAll('',document.all['lnk_'].text)">[−]</a>
        </td>
        <td>
          <div class="Heading2">Test Module Information</div>
        </td>
      </tr>
    </table>
    <table class="SubHeadingTable">
      <tr>
        <td>
          <big class="Heading2">Test Case Details</big>
        </td>
      </tr>
    </table>
    <table class="GroupHeadingTable">
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_1" href="javascript:switchAll('1',document.all['lnk_1'].text)">[−]</a>
        </td>
        <td>
          <big class="Heading3">
            <a name="i__1359961936_8">1 Test Group: Usecase COM</a>
          </big>
        </td>
      </tr>
    </table>
    <div id="div_1">
      <div class="Indentation"></div>
    </div>
    <table class="GroupHeadingTable">
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_1.1" href="javascript:switchAll('1.1',document.all['lnk_1.1'].text)">[−]</a>
        </td>
        <td>
          <big class="Heading3">
            <a name="i__1359961936_12">1.1 Test Group: CAN Data Transmission and Reception</a>
          </big>
        </td>
      </tr>
    </table>
    <div id="div_1.1">
      <div class="Indentation"></div>
    </div>
    <table>
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_1.1.1" href="javascript:switchAll('1.1.1',document.all['lnk_1.1.1'].text)">[−]</a>
        </td>
        <td class="TestcaseHeadingPositiveResult">
          <big class="Heading3">1.1.1 <a name="i__1359961936_16">Test Case TCASE-394602: CAN-FD Data Transmission ( Vita_CAN0 )</a>: Passed</big>
        </td>
      </tr>
    </table>
    <div class="Indentation" id="div_1.1.1">
      <p>Verify that the application can transmit data on CAN_FD using signals or signal groups.<br>                        Ecu Rx group signal: 'SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx',<br>                        Ecu Tx signal: 'Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx'.</p>
      <div class="Indentation">
        <table class="DefaultTable">
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case begin: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:31</td>
            <td class="CellNoColor">(logging timestamp   21.464461)</td>
          </tr>
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case end: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:33</td>
            <td class="CellNoColor" nowrap>(logging timestamp   23.532978)</td>
          </tr>
        </table>
      </div>
      <div class="Indentation"></div>
      <div id="div_1.1.1">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Preparation of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">1. Clear error log: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  21.464461</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ErrorLog': </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  21.464461</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">2. Activate COM Tx Only Use Case: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  21.464461</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::UseCaseActivator': 3 (Com_TxOnly)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  21.464461</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.664461</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=1200ms (max=1200ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.664461</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 1200 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.664461</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::UseCaseActivator' value passed: = 3 (Com_TxOnly), condition: == 3 (Com_TxOnly)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.664461</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">3. Verify that the current CANoe version is suitable for the Start Application: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.664461</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.664461</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.664461</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::CANoeVersionInfo': = 100000, condition: &gt;= 80200</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">4. Initialize variables: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.664461</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComSignalPairSelector': 0 (Vita_CAN0_to_Vita_CAN0) </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.664461</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComSendCtrl': 2</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.664461</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">5. Initialize ComInput: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.664461</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.664461</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">6. Wait for rx data processing on ECU side.: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.782472</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Resumed on sysvar 'ComActualOutput' Elapsed time=118.012ms (max=10000ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.782472</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 3 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.782472</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.782472</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::ComActualOutput': = 0, condition: == 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  22.782472</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::ComExpectedOutput': = 0, condition: == 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
          </table>
        </div>
      </div>
      <table class="TestGroupHeadingBackground">
        <tr>
          <td>
            <big class="Heading3">Main Part of Test Case</big>
          </td>
        </tr>
      </table>
      <div class="Indentation">
        <table class="ResultTable">
          <tr>
            <th class="TableHeadingCell" width="1px">Timestamp</th>
            <th class="TableHeadingCell" width="1px">Test Step</th>
            <th class="TableHeadingCell" width="auto">Description</th>
            <th class="TableHeadingCell" width="1px">Result</th>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1. Set sensor value to 254, this will be scaled to the maximum value of the Rx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  22.782472</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 254</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  22.782472</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">2. Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  22.783972</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=1.50001ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  22.783972</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  22.783972</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = -254, condition: != 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">3. Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.032974</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=249.002ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.032974</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.032974</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">4. Set sensor value to 128, this will be scaled to the median value of the Rx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.032974</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 128</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.032974</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">5. Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.033974</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=1.00001ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.033974</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.033974</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 126, condition: != 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">6. Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.282476</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=248.502ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.282476</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.282476</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">7. Set sensor value to 0, this will be scaled to the minimum value of the Rx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.282476</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.282476</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">8. Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.283976</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=1.50001ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.283976</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.283976</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 128, condition: != 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">9. Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.532978</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=249.002ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.532978</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  23.532978</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
        </table>
      </div>
      <div id="div_1.1.1">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Completion of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">1. Switch back to Com Rx/Tx use case: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  23.532978</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::UseCaseActivator': 0 (Com_RxTx) </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  23.532978</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="DefineCell">  23.532978</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Value of system variable 'StartApplication::ErrorLog'  is </td>
              <td class="DefaultCell">-</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    <table>
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_1.1.2" href="javascript:switchAll('1.1.2',document.all['lnk_1.1.2'].text)">[−]</a>
        </td>
        <td class="TestcaseHeadingPositiveResult">
          <big class="Heading3">1.1.2 <a name="i__1359961936_985">Test Case TCASE-375706: CAN Data Reception ( Vita_CAN0 )</a>: Passed</big>
        </td>
      </tr>
    </table>
    <div class="Indentation" id="div_1.1.2">
      <p>Verify that the application can receive data on CAN using signals or signal groups.<br>                        Ecu Rx group signal: 'SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx',<br>                        Ecu Tx signal: 'Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx'.</p>
      <div class="Indentation">
        <table class="DefaultTable">
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case begin: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:33</td>
            <td class="CellNoColor">(logging timestamp   23.532978)</td>
          </tr>
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case end: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:35</td>
            <td class="CellNoColor" nowrap>(logging timestamp   25.282988)</td>
          </tr>
        </table>
      </div>
      <div class="Indentation"></div>
      <div id="div_1.1.2">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Preparation of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">1. Clear error log: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  23.532978</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ErrorLog': </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  23.532978</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">2. Activate COM Use Case: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  23.532978</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::UseCaseActivator': 0 (Com_RxTx)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  23.532978</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=1200ms (max=1200ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 1200 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::UseCaseActivator' value passed: = 0 (Com_RxTx), condition: == 0 (Com_RxTx)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">3. Verify that the current CANoe version is suitable for the Start Application: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::CANoeVersionInfo': = 100000, condition: &gt;= 80200</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">4. Initialize variables: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComSignalPairSelector': 0 (Vita_CAN0_to_Vita_CAN0) </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComSendCtrl': 2</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">5. Initialize ComInput: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">6. Wait for rx data processing on ECU side.: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 3 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::ComActualOutput': = 0, condition: == 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::ComExpectedOutput': = 0, condition: == 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
          </table>
        </div>
      </div>
      <table class="TestGroupHeadingBackground">
        <tr>
          <td>
            <big class="Heading3">Main Part of Test Case</big>
          </td>
        </tr>
      </table>
      <div class="Indentation">
        <table class="ResultTable">
          <tr>
            <th class="TableHeadingCell" width="1px">Timestamp</th>
            <th class="TableHeadingCell" width="1px">Test Step</th>
            <th class="TableHeadingCell" width="auto">Description</th>
            <th class="TableHeadingCell" width="1px">Result</th>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1. Set sensor value to 254, this will be scaled to the maximum value of the Rx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 254</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  24.732978</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">2. Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  24.735485</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=2.50707ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  24.735485</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  24.735485</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = -254, condition: != 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">3. Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  24.782986</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=47.5002ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  24.782986</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  24.782986</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">4. Set sensor value to 128, this will be scaled to the median value of the Rx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  24.782986</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 128</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  24.782986</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">5. Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  24.784506</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=1.52062ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  24.784506</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  24.784506</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 126, condition: != 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">6. Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  25.032987</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=248.48ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  25.032987</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  25.032987</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">7. Set sensor value to 0, this will be scaled to the minimum value of the Rx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  25.032987</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  25.032987</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">8. Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  25.034987</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=2.00001ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  25.034987</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  25.034987</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 128, condition: != 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">9. Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  25.282988</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=248.002ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  25.282988</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  25.282988</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
        </table>
      </div>
      <div id="div_1.1.2">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Completion of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="DefineCell">  25.282988</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Value of system variable 'StartApplication::ErrorLog'  is </td>
              <td class="DefaultCell">-</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    <table class="GroupEndTable">
      <tr>
        <td>End of Test Group: CAN Data Transmission and Reception</td>
      </tr>
    </table>
    <table class="GroupHeadingTable">
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_1.3" href="javascript:switchAll('1.3',document.all['lnk_1.3'].text)">[−]</a>
        </td>
        <td>
          <big class="Heading3">
            <a name="i__1359961936_1959">1.3 Test Group: FlexRay Data Transmission and Reception</a>
          </big>
        </td>
      </tr>
    </table>
    <div id="div_1.3">
      <div class="Indentation"></div>
    </div>
    <table>
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_1.3.1" href="javascript:switchAll('1.3.1',document.all['lnk_1.3.1'].text)">[−]</a>
        </td>
        <td class="TestcaseHeadingPositiveResult">
          <big class="Heading3">1.3.1 <a name="i__1359961936_1963">Test Case TCASE-375707: FlexRay Data Transmission ( Vita_FR0 )</a>: Passed</big>
        </td>
      </tr>
    </table>
    <div class="Indentation" id="div_1.3.1">
      <p>Verify that the application can transmit data on FlexRay using signals or signal groups.<br>                        Ecu Rx group signal: 'SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx',<br>                        Ecu Tx signal: 'Signal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx'.</p>
      <div class="Indentation">
        <table class="DefaultTable">
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case begin: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:35</td>
            <td class="CellNoColor">(logging timestamp   25.282988)</td>
          </tr>
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case end: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:37</td>
            <td class="CellNoColor" nowrap>(logging timestamp   27.049267)</td>
          </tr>
        </table>
      </div>
      <div class="Indentation"></div>
      <div id="div_1.3.1">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Preparation of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">1. Clear error log: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  25.282988</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ErrorLog': </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  25.282988</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">2. Activate COM Tx Only Use Case: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  25.282988</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::UseCaseActivator': 3 (Com_TxOnly)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  25.282988</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=1200ms (max=1200ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 1200 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::UseCaseActivator' value passed: = 3 (Com_TxOnly), condition: == 3 (Com_TxOnly)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">3. Verify that the current CANoe version is suitable for the Start Application: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::CANoeVersionInfo': = 100000, condition: &gt;= 80200</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">4. Initialize variables: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComSignalPairSelector': 2 (Vita_FR0_to_Vita_FR0) </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComSendCtrl': 2</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">5. Initialize ComInput: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">6. Wait for rx data processing on ECU side.: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 3 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::ComActualOutput': = 0, condition: == 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::ComExpectedOutput': = 0, condition: == 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
          </table>
        </div>
      </div>
      <table class="TestGroupHeadingBackground">
        <tr>
          <td>
            <big class="Heading3">Main Part of Test Case</big>
          </td>
        </tr>
      </table>
      <div class="Indentation">
        <table class="ResultTable">
          <tr>
            <th class="TableHeadingCell" width="1px">Timestamp</th>
            <th class="TableHeadingCell" width="1px">Test Step</th>
            <th class="TableHeadingCell" width="auto">Description</th>
            <th class="TableHeadingCell" width="1px">Result</th>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1. Set sensor value to 254, this will be scaled to the maximum value of the Rx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 254</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.482988</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">2. Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.484997</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=2.00835ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.484997</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.484997</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = -254, condition: != 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">3. Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.549260</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=64.2629ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.549260</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.549260</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">4. Set sensor value to 128, this will be scaled to the median value of the Rx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.549260</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 128</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.549260</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">5. Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.550997</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=1.73755ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.550997</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.550997</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 126, condition: != 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">6. Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.849264</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=298.267ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.849264</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.849264</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">7. Set sensor value to 0, this will be scaled to the minimum value of the Rx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.849264</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.849264</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">8. Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.850499</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=1.23524ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.850499</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  26.850499</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 128, condition: != 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">9. Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  27.049267</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=198.768ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  27.049267</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  27.049267</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
        </table>
      </div>
      <div id="div_1.3.1">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Completion of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">1. Switch back to Com Rx/Tx use case: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  27.049267</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::UseCaseActivator': 0 (Com_RxTx) </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  27.049267</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="DefineCell">  27.049267</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Value of system variable 'StartApplication::ErrorLog'  is </td>
              <td class="DefaultCell">-</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    <table>
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_1.3.2" href="javascript:switchAll('1.3.2',document.all['lnk_1.3.2'].text)">[−]</a>
        </td>
        <td class="TestcaseHeadingPositiveResult">
          <big class="Heading3">1.3.2 <a name="i__1359961936_2932">Test Case TCASE-375708: FlexRay Data Reception ( Vita_FR0 )</a>: Passed</big>
        </td>
      </tr>
    </table>
    <div class="Indentation" id="div_1.3.2">
      <p>Verify that the application can receive data on FlexRay using signals or signal groups.<br>                        Ecu Rx signal: 'StartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx',<br>                        Ecu Tx signal: 'Signal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx'.</p>
      <div class="Indentation">
        <table class="DefaultTable">
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case begin: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:37</td>
            <td class="CellNoColor">(logging timestamp   27.049267)</td>
          </tr>
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case end: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:39</td>
            <td class="CellNoColor" nowrap>(logging timestamp   28.849292)</td>
          </tr>
        </table>
      </div>
      <div class="Indentation"></div>
      <div id="div_1.3.2">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Preparation of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">1. Clear error log: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  27.049267</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ErrorLog': </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  27.049267</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">2. Activate COM Use Case: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  27.049267</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::UseCaseActivator': 0 (Com_RxTx)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  27.049267</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=1200ms (max=1200ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 1200 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::UseCaseActivator' value passed: = 0 (Com_RxTx), condition: == 0 (Com_RxTx)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">3. Verify that the current CANoe version is suitable for the Start Application: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::CANoeVersionInfo': = 100000, condition: &gt;= 80200</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">4. Initialize variables: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComSignalPairSelector': 2 (Vita_FR0_to_Vita_FR0) </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComSendCtrl': 2</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">5. Initialize ComInput: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">6. Wait for rx data processing on ECU side.: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 3 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::ComActualOutput': = 0, condition: == 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::ComExpectedOutput': = 0, condition: == 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
          </table>
        </div>
      </div>
      <table class="TestGroupHeadingBackground">
        <tr>
          <td>
            <big class="Heading3">Main Part of Test Case</big>
          </td>
        </tr>
      </table>
      <div class="Indentation">
        <table class="ResultTable">
          <tr>
            <th class="TableHeadingCell" width="1px">Timestamp</th>
            <th class="TableHeadingCell" width="1px">Test Step</th>
            <th class="TableHeadingCell" width="auto">Description</th>
            <th class="TableHeadingCell" width="1px">Result</th>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1. Set sensor value to 254, this will be scaled to the maximum value of the Rx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 254</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.249267</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">2. Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.254554</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=5.28676ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.254554</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.254554</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = -254, condition: != 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">3. Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.349283</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=94.7297ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.349283</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.349283</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">4. Set sensor value to 128, this will be scaled to the median value of the Rx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.349283</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 128</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.349283</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">5. Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.354555</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=5.27189ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.354555</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.354555</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 126, condition: != 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">6. Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.549287</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=194.731ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.549287</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.549287</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">7. Set sensor value to 0, this will be scaled to the minimum value of the Rx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.549287</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ComInput': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.549287</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">8. Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet.: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.554559</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=5.27197ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.554559</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.554559</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 128, condition: != 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">9. Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.849292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Resumed on sysvar 'ComCmpOutputs' Elapsed time=294.733ms (max=10000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.849292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  28.849292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::ComCmpOutputs': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
        </table>
      </div>
      <div id="div_1.3.2">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Completion of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="DefineCell">  28.849292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Value of system variable 'StartApplication::ErrorLog'  is </td>
              <td class="DefaultCell">-</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    <table class="GroupEndTable">
      <tr>
        <td>End of Test Group: FlexRay Data Transmission and Reception</td>
      </tr>
    </table>
    <table class="GroupEndTable">
      <tr>
        <td>End of Test Group: Usecase COM</td>
      </tr>
    </table>
    <table class="GroupHeadingTable">
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_2" href="javascript:switchAll('2',document.all['lnk_2'].text)">[−]</a>
        </td>
        <td>
          <big class="Heading3">
            <a name="i__1359961936_3858">2 Test Group: Usecase DIAG</a>
          </big>
        </td>
      </tr>
    </table>
    <div id="div_2">
      <div class="Indentation"></div>
    </div>
    <table class="GroupHeadingTable">
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_2.1" href="javascript:switchAll('2.1',document.all['lnk_2.1'].text)">[−]</a>
        </td>
        <td>
          <big class="Heading3">
            <a name="i__1359961936_3862">2.1 Test Group: Diagnostics over CAN</a>
          </big>
        </td>
      </tr>
    </table>
    <div id="div_2.1">
      <div class="Indentation"></div>
    </div>
    <table>
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_2.1.1" href="javascript:switchAll('2.1.1',document.all['lnk_2.1.1'].text)">[−]</a>
        </td>
        <td class="TestcaseHeadingPositiveResult">
          <big class="Heading3">2.1.1 <a name="i__1359961936_3866">Test Case TCASE-478915: Basic Request/Response via UDS over CAN ( Vita_CAN0 )</a>: Passed</big>
        </td>
      </tr>
    </table>
    <div class="Indentation" id="div_2.1.1">
      <p>Verify that a DCM request can be sent and a response can be received via UDS over CAN</p>
      <div class="Indentation">
        <table class="DefaultTable">
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case begin: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:39</td>
            <td class="CellNoColor">(logging timestamp   28.849292)</td>
          </tr>
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case end: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:41</td>
            <td class="CellNoColor" nowrap>(logging timestamp   31.069292)</td>
          </tr>
        </table>
      </div>
      <div class="Indentation"></div>
      <div id="div_2.1.1">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Preparation of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">1. Clear error log: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.849292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ErrorLog': </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.849292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">2. Activate DIAG Use Case: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.849292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::UseCaseActivator': 2 (Diag)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  28.849292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.049292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=1200ms (max=1200ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.049292</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 1200 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.049292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::UseCaseActivator' value passed: = 2 (Diag), condition: == 2 (Diag)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.049292</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">3. Verify that the current CANoe version is suitable for the Start Application: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.049292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.049292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.049292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::CANoeVersionInfo': = 100000, condition: &gt;= 80200</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">4. Initialize variables: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.049292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagChannel': 0 (Vita_CAN0) </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.049292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagActivateDefaultSession': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.049292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">5. Wait to allow System Variables to change: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.059292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=10ms (max=10ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.059292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for 10 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">6. Wait until there is no diagnostic request active and no further requests are queued.: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.059292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.059292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.059292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::DiagRequestState': = 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE), condition: == 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">7. Reset response code: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.059292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagResponseCode': 0 (0x00: No Response received)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.059292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.069292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=10ms (max=10ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.069292</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 10 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.069292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 0 (0x00: No Response received), condition: == 0 (0x00: No Response received)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  30.069292</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
          </table>
        </div>
      </div>
      <table class="TestGroupHeadingBackground">
        <tr>
          <td>
            <big class="Heading3">Main Part of Test Case</big>
          </td>
        </tr>
      </table>
      <div class="Indentation">
        <table class="ResultTable">
          <tr>
            <th class="TableHeadingCell" width="1px">Timestamp</th>
            <th class="TableHeadingCell" width="1px">Test Step</th>
            <th class="TableHeadingCell" width="auto">Description</th>
            <th class="TableHeadingCell" width="1px">Result</th>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1. Try to activate default session via DSC: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  30.069292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagActivateDefaultSession': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  30.069292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  31.069292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  31.069292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  31.069292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagActivateDefaultSession' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  31.069292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">2. Verify that a response can be received: either positive response (0x50) or negative response (0x7F): Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  31.069292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  31.069292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of any of 2 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  31.069292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::DiagResponseCode': = 80 (0x50: Positive Response DSC), condition: == 80 (0x50: Positive Response DSC)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  31.069292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::DiagResponseCode': = 80 (0x50: Positive Response DSC), condition: == 127 (0x7F: Negative Response)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
        </table>
      </div>
      <div id="div_2.1.1">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Completion of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="DefineCell">  31.069292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Value of system variable 'StartApplication::ErrorLog'  is </td>
              <td class="DefaultCell">-</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    <table>
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_2.1.2" href="javascript:switchAll('2.1.2',document.all['lnk_2.1.2'].text)">[−]</a>
        </td>
        <td class="TestcaseHeadingPositiveResult">
          <big class="Heading3">2.1.2 <a name="i__1359961936_4502">Test Case TCASE-481982: Read Diagnostic Data via UDS over CAN ( Vita_CAN0 )</a>: Passed</big>
        </td>
      </tr>
    </table>
    <div class="Indentation" id="div_2.1.2">
      <p>Verify that a diagnostic data record (DID) can be read via UDS over CAN</p>
      <div class="Indentation">
        <table class="DefaultTable">
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case begin: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:41</td>
            <td class="CellNoColor">(logging timestamp   31.069292)</td>
          </tr>
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case end: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:46</td>
            <td class="CellNoColor" nowrap>(logging timestamp   36.089292)</td>
          </tr>
        </table>
      </div>
      <div class="Indentation"></div>
      <div id="div_2.1.2">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Preparation of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">1. Clear error log: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  31.069292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ErrorLog': </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  31.069292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">2. Activate DIAG Use Case: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  31.069292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::UseCaseActivator': 2 (Diag)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  31.069292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.269292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=1200ms (max=1200ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.269292</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 1200 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.269292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::UseCaseActivator' value passed: = 2 (Diag), condition: == 2 (Diag)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.269292</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">3. Verify that the current CANoe version is suitable for the Start Application: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.269292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.269292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.269292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::CANoeVersionInfo': = 100000, condition: &gt;= 80200</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">4. Initialize variables: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.269292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagChannel': 0 (Vita_CAN0) </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.269292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagSetEventStatusPassed': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.269292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagGetCounter': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.269292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagResetCounterWithRxData': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.269292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagCounterValue': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.269292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">5. Wait to allow System Variables to change: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.279292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=10ms (max=10ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.279292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for 10 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">6. Wait until there is no diagnostic request active and no further requests are queued.: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.279292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.279292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.279292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::DiagRequestState': = 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE), condition: == 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">7. Reset response code: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.279292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagResponseCode': 0 (0x00: No Response received)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.279292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.289292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=10ms (max=10ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.289292</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 10 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.289292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 0 (0x00: No Response received), condition: == 0 (0x00: No Response received)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  32.289292</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
          </table>
        </div>
      </div>
      <table class="TestGroupHeadingBackground">
        <tr>
          <td>
            <big class="Heading3">Main Part of Test Case</big>
          </td>
        </tr>
      </table>
      <div class="Indentation">
        <table class="ResultTable">
          <tr>
            <th class="TableHeadingCell" width="1px">Timestamp</th>
            <th class="TableHeadingCell" width="1px">Test Step</th>
            <th class="TableHeadingCell" width="auto">Description</th>
            <th class="TableHeadingCell" width="1px">Result</th>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1. Initialize: Reset DID value to 0 via RxData: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  32.289292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagResetCounterWithRxData': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  32.289292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  32.389292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  32.389292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  32.389292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResetCounterWithRxData' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  32.389292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  32.389292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagResetCounterWithRxData': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  32.389292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  32.489292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  32.489292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">2. Wait to allow for transmission of control signals: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  33.689292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1200ms (max=1200ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  33.689292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for 1200 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">3. Get DID value via RDBI and verify that it was reset to 0x0000: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  33.689292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetCounter': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  33.689292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.689292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.689292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.689292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagCounterValue' value passed: = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.689292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 98 (0x62: Positive Response RDBI), condition: == 98 (0x62: Positive Response RDBI)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.689292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.689292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetCounter': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.689292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.789292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.789292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">4. Set event status to passed to increment the DID value by 1: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.789292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagSetEventStatusPassed': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.789292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.889292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.889292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.889292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagSetEventStatusPassed' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.889292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.889292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagSetEventStatusPassed': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.889292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.989292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.989292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">5. Get DID value via RDBI and verify that it was set to 0x0001: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.989292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetCounter': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  34.989292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  35.989292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  35.989292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  35.989292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagCounterValue' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  35.989292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 98 (0x62: Positive Response RDBI), condition: == 98 (0x62: Positive Response RDBI)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  35.989292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  35.989292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetCounter': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  35.989292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  36.089292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  36.089292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
        </table>
      </div>
      <div id="div_2.1.2">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Completion of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="DefineCell">  36.089292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Value of system variable 'StartApplication::ErrorLog'  is </td>
              <td class="DefaultCell">-</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    <table>
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_2.1.3" href="javascript:switchAll('2.1.3',document.all['lnk_2.1.3'].text)">[−]</a>
        </td>
        <td class="TestcaseHeadingPositiveResult">
          <big class="Heading3">2.1.3 <a name="i__1359961936_5613">Test Case TCASE-483095: Write Diagnostic Data via UDS over CAN ( Vita_CAN0 )</a>: Passed</big>
        </td>
      </tr>
    </table>
    <div class="Indentation" id="div_2.1.3">
      <p>Verify that a diagnostic data record (DID) can be written via UDS over CAN</p>
      <div class="Indentation">
        <table class="DefaultTable">
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case begin: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:46</td>
            <td class="CellNoColor">(logging timestamp   36.089292)</td>
          </tr>
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case end: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:50</td>
            <td class="CellNoColor" nowrap>(logging timestamp   39.709292)</td>
          </tr>
        </table>
      </div>
      <div class="Indentation"></div>
      <div id="div_2.1.3">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Preparation of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">1. Clear error log: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  36.089292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ErrorLog': </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  36.089292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">2. Activate DIAG Use Case: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  36.089292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::UseCaseActivator': 2 (Diag)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  36.089292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.289292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=1200ms (max=1200ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.289292</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 1200 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.289292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::UseCaseActivator' value passed: = 2 (Diag), condition: == 2 (Diag)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.289292</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">3. Verify that the current CANoe version is suitable for the Start Application: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.289292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.289292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.289292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::CANoeVersionInfo': = 100000, condition: &gt;= 80200</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">4. Initialize variables: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.289292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagRequestState': 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE) </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.289292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagChannel': 0 (Vita_CAN0) </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.289292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagSetCounter': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.289292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagCounterValueFromTxData': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.289292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagResetCounterWithRxData': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.289292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">5. Wait to allow System Variables to change: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.299292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=10ms (max=10ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.299292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for 10 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">6. Wait until there is no diagnostic request active and no further requests are queued.: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.299292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.299292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.299292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::DiagRequestState': = 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE), condition: == 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">7. Reset response code: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.299292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagResponseCode': 0 (0x00: No Response received)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.299292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.309292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=10ms (max=10ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.309292</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 10 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.309292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 0 (0x00: No Response received), condition: == 0 (0x00: No Response received)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  37.309292</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
          </table>
        </div>
      </div>
      <table class="TestGroupHeadingBackground">
        <tr>
          <td>
            <big class="Heading3">Main Part of Test Case</big>
          </td>
        </tr>
      </table>
      <div class="Indentation">
        <table class="ResultTable">
          <tr>
            <th class="TableHeadingCell" width="1px">Timestamp</th>
            <th class="TableHeadingCell" width="1px">Test Step</th>
            <th class="TableHeadingCell" width="auto">Description</th>
            <th class="TableHeadingCell" width="1px">Result</th>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1. Initialize: Reset DID value to 0 via RxData: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  37.309292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagResetCounterWithRxData': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  37.309292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  37.409292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  37.409292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  37.409292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResetCounterWithRxData' value passed: = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  37.409292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  37.409292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagResetCounterWithRxData': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  37.409292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  37.509292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  37.509292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">2. Wait to allow for transmission of control signals: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  38.709292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1200ms (max=1200ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  38.709292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for 1200 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">3. Get DID value via TxData and verify that it was reset to 0x0000: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  38.709292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  38.709292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  38.709292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::DiagCounterValueFromTxData': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">4. Set DID value to 0x0005 via WDBI: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  38.709292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagSetCounter': 5</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  38.709292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  39.709292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  39.709292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  39.709292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 110 (0x6E: Positive Response WDBI), condition: == 110 (0x6E: Positive Response WDBI)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  39.709292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">5. Get DID value via TxData and verify that it was set to 0x0005: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  39.709292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  39.709292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  39.709292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::DiagCounterValueFromTxData': = 5, condition: == 5</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
        </table>
      </div>
      <div id="div_2.1.3">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Completion of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="DefineCell">  39.709292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Value of system variable 'StartApplication::ErrorLog'  is </td>
              <td class="DefaultCell">-</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    <table class="GroupEndTable">
      <tr>
        <td>End of Test Group: Diagnostics over CAN</td>
      </tr>
    </table>
    <table class="GroupHeadingTable">
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_2.2" href="javascript:switchAll('2.2',document.all['lnk_2.2'].text)">[−]</a>
        </td>
        <td>
          <big class="Heading3">
            <a name="i__1359961936_6530">2.2 Test Group: Diagnostic Event Handling</a>
          </big>
        </td>
      </tr>
    </table>
    <div id="div_2.2">
      <div class="Indentation"></div>
    </div>
    <table>
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_2.2.1" href="javascript:switchAll('2.2.1',document.all['lnk_2.2.1'].text)">[−]</a>
        </td>
        <td class="TestcaseHeadingPositiveResult">
          <big class="Heading3">2.2.1 <a name="i__1359961936_6534">Test Case TCASE-379331: Diagnostic Event Handling with FreezeFrame and without Debouncing ( Vita_CAN0 )</a>: Passed</big>
        </td>
      </tr>
    </table>
    <div class="Indentation" id="div_2.2.1">
      <p>Verify that a diagnostic data record (DID) can be written and read via UDS over CAN</p>
      <div class="Indentation">
        <table class="DefaultTable">
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case begin: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:02:50</td>
            <td class="CellNoColor">(logging timestamp   39.709292)</td>
          </tr>
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case end: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:03:03</td>
            <td class="CellNoColor" nowrap>(logging timestamp   53.229292)</td>
          </tr>
        </table>
      </div>
      <div class="Indentation"></div>
      <div id="div_2.2.1">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Preparation of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">1. Clear error log: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  39.709292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ErrorLog': </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  39.709292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">2. Activate DIAG Use Case: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  39.709292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::UseCaseActivator': 2 (Diag)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  39.709292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=1200ms (max=1200ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 1200 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::UseCaseActivator' value passed: = 2 (Diag), condition: == 2 (Diag)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">3. Verify that the current CANoe version is suitable for the Start Application: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::CANoeVersionInfo': = 100000, condition: &gt;= 80200</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">4. Initialize variables: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagChannel': 0 (Vita_CAN0) </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagSetEventStatusPassed': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagGetCounter': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagGetDTCSnapshot': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagResetCounterWithRxData': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagClearDTC': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagCounterValue': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::DiagSnapshotDataValue': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.909292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">5. Wait to allow System Variables to change: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.919292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=10ms (max=10ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.919292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for 10 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">6. Wait until there is no diagnostic request active and no further requests are queued.: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.919292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.919292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of 1 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.919292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::DiagRequestState': = 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE), condition: == 0 (QUEUE_EMPTY_REQUEST_NOT_ACTIVE)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">7. Reset response code: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.919292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagResponseCode': 0 (0x00: No Response received)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.919292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.929292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=10ms (max=10ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.929292</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 10 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.929292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 0 (0x00: No Response received), condition: == 0 (0x00: No Response received)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  40.929292</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
          </table>
        </div>
      </div>
      <table class="TestGroupHeadingBackground">
        <tr>
          <td>
            <big class="Heading3">Main Part of Test Case</big>
          </td>
        </tr>
      </table>
      <div class="Indentation">
        <table class="ResultTable">
          <tr>
            <th class="TableHeadingCell" width="1px">Timestamp</th>
            <th class="TableHeadingCell" width="1px">Test Step</th>
            <th class="TableHeadingCell" width="auto">Description</th>
            <th class="TableHeadingCell" width="1px">Result</th>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1. Initialize: Reset event counter to 0 via RxData: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  40.929292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagResetCounterWithRxData': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  40.929292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  41.029292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  41.029292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  41.029292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResetCounterWithRxData' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  41.029292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  41.029292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagResetCounterWithRxData': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  41.029292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  41.129292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  41.129292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">2. Wait to allow for transmission of control signals: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  42.329292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1200ms (max=1200ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  42.329292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for 1200 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">3. Initialize: Clear DTC: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  42.329292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagClearDTC': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  42.329292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  43.329292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  43.329292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  43.329292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 84 (0x54: Positive Response CDTCI), condition: == 84 (0x54: Positive Response CDTCI)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  43.329292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  43.329292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagClearDTC': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  43.329292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  43.429292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  43.429292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">4. Get DTC snapshot and verify that no snapshot record is returned (i.e. -1): Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  43.429292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  43.429292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.429292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.429292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.429292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagSnapshotDataValue' value passed: = -1, condition: == -1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.429292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.429292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.429292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.429292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.529292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.529292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">5. Set event status to passed: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.529292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagSetEventStatusPassed': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.529292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.629292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.629292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.629292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagSetEventStatusPassed' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.629292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.629292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagSetEventStatusPassed': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.629292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.729292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.729292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">6. Get DTC snapshot and verify that still no snapshot record is returned (i.e. -1): Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.729292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  44.729292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.729292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.729292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.729292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagSnapshotDataValue' value passed: = -1, condition: == -1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.729292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.729292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.729292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.729292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.829292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.829292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">7. Set event status to failed: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.829292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagSetEventStatusFailed': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.829292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.929292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.929292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.929292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagSetEventStatusFailed' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.929292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.929292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagSetEventStatusFailed': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  45.929292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  46.029292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  46.029292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">8. Get DTC snapshot and verify that a snapshot record with the value 2 is returned: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  46.029292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  46.029292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.029292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.029292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.029292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagSnapshotDataValue' value passed: = 2, condition: == 2</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.029292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.029292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.029292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.029292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.129292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.129292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">9. Set event status to passed: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.129292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagSetEventStatusPassed': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.129292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.229292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.229292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.229292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagSetEventStatusPassed' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.229292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.229292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagSetEventStatusPassed': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.229292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.329292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.329292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">10. Get DTC snapshot and verify that still a snapshot record with the value 2 is returned: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.329292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  47.329292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.329292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.329292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.329292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagSnapshotDataValue' value passed: = 2, condition: == 2</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.329292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.329292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.329292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.329292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.429292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.429292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">11. Set event status to failed: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.429292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagSetEventStatusFailed': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.429292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.529292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.529292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.529292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagSetEventStatusFailed' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.529292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.529292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagSetEventStatusFailed': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.529292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.629292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.629292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">12. Get DTC snapshot and verify that still a snapshot record with the value 2 is returned: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.629292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  48.629292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  49.629292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  49.629292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  49.629292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagSnapshotDataValue' value passed: = 2, condition: == 2</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  49.629292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  49.629292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  49.629292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  49.629292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  49.729292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  49.729292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">13. Clear DTC: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  49.729292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagClearDTC': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  49.729292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  50.729292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  50.729292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  50.729292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 84 (0x54: Positive Response CDTCI), condition: == 84 (0x54: Positive Response CDTCI)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  50.729292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  50.729292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagClearDTC': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  50.729292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  50.829292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  50.829292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">14. Get DTC snapshot and verify that no snapshot record is returned (i.e. -1): Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  50.829292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  50.829292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  51.829292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  51.829292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  51.829292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagSnapshotDataValue' value passed: = -1, condition: == -1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  51.829292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  51.829292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  51.829292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  51.829292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  51.929292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  51.929292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">15. Set event status to failed: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  51.929292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagSetEventStatusFailed': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  51.929292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  52.029292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  52.029292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  52.029292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagSetEventStatusFailed' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  52.029292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  52.029292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagSetEventStatusFailed': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  52.029292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  52.129292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  52.129292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">16. Get DTC snapshot and verify that a snapshot record with the value 5 is returned: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  52.129292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  52.129292</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  53.129292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  53.129292</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  53.129292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagSnapshotDataValue' value passed: = 5, condition: == 5</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  53.129292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::DiagResponseCode' value passed: = 89 (0x59: Positive Response RDTCSSBDTC), condition: == 89 (0x59: Positive Response RDTCSSBDTC)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  53.129292</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  53.129292</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::DiagGetDTCSnapshot': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  53.129292</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  53.229292</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  53.229292</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
        </table>
      </div>
      <div id="div_2.2.1">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Completion of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="DefineCell">  53.229292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Value of system variable 'StartApplication::ErrorLog'  is </td>
              <td class="DefaultCell">-</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    <table class="GroupEndTable">
      <tr>
        <td>End of Test Group: Diagnostic Event Handling</td>
      </tr>
    </table>
    <table class="GroupEndTable">
      <tr>
        <td>End of Test Group: Usecase DIAG</td>
      </tr>
    </table>
    <table class="GroupHeadingTable">
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_3" href="javascript:switchAll('3',document.all['lnk_3'].text)">[−]</a>
        </td>
        <td>
          <big class="Heading3">
            <a name="i__1359961936_9094">3 Test Group: Usecase MEM</a>
          </big>
        </td>
      </tr>
    </table>
    <div id="div_3">
      <div class="Indentation"></div>
    </div>
    <table class="GroupHeadingTable">
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_3.1" href="javascript:switchAll('3.1',document.all['lnk_3.1'].text)">[−]</a>
        </td>
        <td>
          <big class="Heading3">
            <a name="i__1359961936_9098">3.1 Test Group: Write and Read Nonvolatile Memory</a>
          </big>
        </td>
      </tr>
    </table>
    <div id="div_3.1">
      <div class="Indentation"></div>
    </div>
    <table>
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_3.1.1" href="javascript:switchAll('3.1.1',document.all['lnk_3.1.1'].text)">[−]</a>
        </td>
        <td class="TestcaseHeadingPositiveResult">
          <big class="Heading3">3.1.1 <a name="i__1359961936_9102">Test Case TCASE-379342: Write and Read NV block (Block 1, FEE)</a>: Passed</big>
        </td>
      </tr>
    </table>
    <div class="Indentation" id="div_3.1.1">
      <p>Verify that the application can write and read non-volatile data.</p>
      <div class="Indentation">
        <table class="DefaultTable">
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case begin: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:03:03</td>
            <td class="CellNoColor">(logging timestamp   53.229292)</td>
          </tr>
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case end: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:03:09</td>
            <td class="CellNoColor" nowrap>(logging timestamp   58.624891)</td>
          </tr>
        </table>
      </div>
      <div class="Indentation"></div>
      <div id="div_3.1.1">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Preparation of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <div class="Heading5">Variables:</div>
          <div class="Indentation">
            <table class="OverviewTable">
              <tr>
                <th class="TableHeadingCell">Name</th>
                <th class="TableHeadingCell">Type</th>
                <th class="TableHeadingCell">Value</th>
              </tr>
              <tr>
                <td class="DefineCell">valueToWrite</td>
                <td class="DefaultCell">integer</td>
                <td class="DefaultCell">0</td>
              </tr>
            </table>
          </div>
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">1. Clear error log: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  53.229292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ErrorLog': </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  53.229292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">2. Activate MEM Use Case: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  53.229292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::UseCaseActivator': 1 (Mem)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  53.229292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.429292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=1200ms (max=1200ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.429292</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 1200 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.429292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::UseCaseActivator' value passed: = 1 (Mem), condition: == 1 (Mem)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.429292</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">3. Set active block: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.429292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvBlockSelector': 0 (Block 1 (FEE))</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.429292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.929292</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=500ms (max=500ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.929292</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 500 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.929292</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::MemNvBlockSelector' value passed: = 0 (Block 1 (FEE)), condition: == 0 (Block 1 (FEE))</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.929292</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">4. Initialize variables: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.929292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvStore': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.929292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvStoreValue': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.929292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvReadCurrValue': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.929292</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvPendingExtended': 0 (UNKNOWN) </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  54.929292</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">5. Wait for initial read operation after use case activation to complete: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  55.024891</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Resumed on sysvar 'MemNvPendingExtended' Elapsed time=95.5998ms (max=10000ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  55.024891</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of any of 2 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  55.024891</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::MemNvPendingExtended': = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  55.024891</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::MemNvPendingExtended': = 4 (READ_FINISHED), condition: == 6 (READ_FAILED)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
          </table>
        </div>
      </div>
      <table class="TestGroupHeadingBackground">
        <tr>
          <td>
            <big class="Heading3">Main Part of Test Case</big>
          </td>
        </tr>
      </table>
      <div class="Indentation">
        <table class="ResultTable">
          <tr>
            <th class="TableHeadingCell" width="1px">Timestamp</th>
            <th class="TableHeadingCell" width="1px">Test Step</th>
            <th class="TableHeadingCell" width="auto">Description</th>
            <th class="TableHeadingCell" width="1px">Result</th>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1. Repeat writing and reading of the NV block with the values 0, 15 and 30: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell" nowrap>  55.024891</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Control function begin</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  55.024891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Set specified value.<br>Variable 'valueToWrite': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  55.024891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check variable 'valueToWrite' value passed: = 0, condition: &lt; 31</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  55.024891</td>
            <td class="NumberCell">0</td>
            <td class="DefaultCell">For-loop condition is fulfilled, executing loop body.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.1. Set the new value to write: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  55.024891</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvStoreValue': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  55.024891</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.2. Trigger writing of the value. After the write is complete the read operation is started automatically: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  55.024891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvStore': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  55.024891</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  55.124891</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  55.124891</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  55.124891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::MemNvStore' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  55.124891</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  55.124891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvStore': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  55.124891</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  55.224891</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  55.224891</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.3. Wait to allow for transmission of control signals: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.224891</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.224891</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.4. Wait for the read operation to complete and verify that the received value is the one which was previously set to be written: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.224891</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.224891</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 2 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.224891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::MemNvReadCurrValue': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.224891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::MemNvPendingExtended': = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  56.224891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check variable 'valueToWrite' value passed: = 15, condition: &lt; 31</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  56.224891</td>
            <td class="NumberCell">15</td>
            <td class="DefaultCell">For-loop condition is fulfilled, executing loop body.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.5. Set the new value to write: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.224891</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvStoreValue': 15</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.224891</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.6. Trigger writing of the value. After the write is complete the read operation is started automatically: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.224891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvStore': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.224891</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.324891</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.324891</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.324891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::MemNvStore' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.324891</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.324891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvStore': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.324891</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.424891</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  56.424891</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.7. Wait to allow for transmission of control signals: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.424891</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.424891</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.8. Wait for the read operation to complete and verify that the received value is the one which was previously set to be written: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.424891</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.424891</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 2 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.424891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::MemNvReadCurrValue': = 15, condition: == 15</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.424891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::MemNvPendingExtended': = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  57.424891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check variable 'valueToWrite' value passed: = 30, condition: &lt; 31</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  57.424891</td>
            <td class="NumberCell">30</td>
            <td class="DefaultCell">For-loop condition is fulfilled, executing loop body.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.9. Set the new value to write: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.424891</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvStoreValue': 30</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.424891</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.10. Trigger writing of the value. After the write is complete the read operation is started automatically: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.424891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvStore': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.424891</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.524891</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.524891</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.524891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::MemNvStore' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.524891</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.524891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvStore': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.524891</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.624891</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  57.624891</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.11. Wait to allow for transmission of control signals: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  58.624891</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  58.624891</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.12. Wait for the read operation to complete and verify that the received value is the one which was previously set to be written: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  58.624891</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  58.624891</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 2 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  58.624891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::MemNvReadCurrValue': = 30, condition: == 30</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  58.624891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::MemNvPendingExtended': = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  58.624891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check variable 'valueToWrite' value failed: = 45, condition: &lt; 31</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  58.624891</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">For-loop condition is not fulfilled, leaving loop.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell" nowrap>  58.624891</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Control function end</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
        </table>
      </div>
      <div id="div_3.1.1">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Completion of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="DefineCell">  58.624891</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Value of system variable 'StartApplication::ErrorLog'  is </td>
              <td class="DefaultCell">-</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    <table>
      <tr>
        <td class="LinkCell">
          <a class="Undecorated" id="lnk_3.1.2" href="javascript:switchAll('3.1.2',document.all['lnk_3.1.2'].text)">[−]</a>
        </td>
        <td class="TestcaseHeadingPositiveResult">
          <big class="Heading3">3.1.2 <a name="i__1359961936_10517">Test Case TCASE-379342: Write and Read NV block (Block 2, FEE)</a>: Passed</big>
        </td>
      </tr>
    </table>
    <div class="Indentation" id="div_3.1.2">
      <p>Verify that the application can write and read non-volatile data.</p>
      <div class="Indentation">
        <table class="DefaultTable">
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case begin: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:03:09</td>
            <td class="CellNoColor">(logging timestamp   58.624891)</td>
          </tr>
          <tr>
            <td style="padding-right: 0.5em;" class="CellNoColor">Test case end: </td>
            <td style="padding-right: 0.5em;" class="CellNoColor">2020-09-01 10:03:15</td>
            <td class="CellNoColor" nowrap>(logging timestamp   64.625252)</td>
          </tr>
        </table>
      </div>
      <div class="Indentation"></div>
      <div id="div_3.1.2">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Preparation of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <div class="Heading5">Variables:</div>
          <div class="Indentation">
            <table class="OverviewTable">
              <tr>
                <th class="TableHeadingCell">Name</th>
                <th class="TableHeadingCell">Type</th>
                <th class="TableHeadingCell">Value</th>
              </tr>
              <tr>
                <td class="DefineCell">valueToWrite</td>
                <td class="DefaultCell">integer</td>
                <td class="DefaultCell">0</td>
              </tr>
            </table>
          </div>
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">1. Clear error log: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  58.624891</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::ErrorLog': </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  58.624891</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">2. Activate MEM Use Case: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  58.624891</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::UseCaseActivator': 1 (Mem)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  58.624891</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  59.824891</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=1200ms (max=1200ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  59.824891</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 1200 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  59.824891</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::UseCaseActivator' value passed: = 1 (Mem), condition: == 1 (Mem)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  59.824891</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">3. Set active block: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  59.824891</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvBlockSelector': 1 (Block 2 (FEE))</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  59.824891</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  60.324891</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell"> Elapsed time=500ms (max=500ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  60.324891</td>
              <td class="NumberCell">2</td>
              <td class="DefaultCell">Waited for 500 ms.</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  60.324891</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">Check system variable 'StartApplication::MemNvBlockSelector' value passed: = 1 (Block 2 (FEE)), condition: == 1 (Block 2 (FEE))</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  60.324891</td>
              <td class="NumberCell">3</td>
              <td class="DefaultCell">Validation of the expected parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">4. Initialize variables: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  60.324891</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvStore': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  60.324891</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvStoreValue': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  60.324891</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvReadCurrValue': 0</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  60.324891</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvPendingExtended': 0 (UNKNOWN) </td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  60.324891</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Stimulation of the input parameters</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
            <tr class="PositiveResultBackground">
              <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
                <big class="Heading4">5. Wait for initial read operation after use case activation to complete: Passed</big>
              </td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  61.025252</td>
              <td class="NumberCell">Resume reason</td>
              <td class="DefaultCell">Resumed on sysvar 'MemNvPendingExtended' Elapsed time=700.361ms (max=10000ms)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  61.025252</td>
              <td class="NumberCell">1</td>
              <td class="DefaultCell">Waited for occurrence of any of 2 value condition(s).</td>
              <td class="PositiveResultCell">pass</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  61.025252</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::MemNvPendingExtended': = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td style="text-indent: 20px;" class="DefineCell">  61.025252</td>
              <td class="NumberCell"></td>
              <td class="DefaultCell">System variable 'StartApplication::MemNvPendingExtended': = 4 (READ_FINISHED), condition: == 6 (READ_FAILED)</td>
              <td class="DefaultCell">-</td>
            </tr>
            <tr>
              <td class="PatternSeparator" colspan="4"></td>
            </tr>
          </table>
        </div>
      </div>
      <table class="TestGroupHeadingBackground">
        <tr>
          <td>
            <big class="Heading3">Main Part of Test Case</big>
          </td>
        </tr>
      </table>
      <div class="Indentation">
        <table class="ResultTable">
          <tr>
            <th class="TableHeadingCell" width="1px">Timestamp</th>
            <th class="TableHeadingCell" width="1px">Test Step</th>
            <th class="TableHeadingCell" width="auto">Description</th>
            <th class="TableHeadingCell" width="1px">Result</th>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 20px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1. Repeat writing and reading of the NV block with the values 0, 15 and 30: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell" nowrap>  61.025252</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Control function begin</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  61.025252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Set specified value.<br>Variable 'valueToWrite': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  61.025252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check variable 'valueToWrite' value passed: = 0, condition: &lt; 31</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  61.025252</td>
            <td class="NumberCell">0</td>
            <td class="DefaultCell">For-loop condition is fulfilled, executing loop body.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.1. Set the new value to write: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  61.025252</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvStoreValue': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  61.025252</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.2. Trigger writing of the value. After the write is complete the read operation is started automatically: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  61.025252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvStore': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  61.025252</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  61.125252</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  61.125252</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  61.125252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::MemNvStore' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  61.125252</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  61.125252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvStore': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  61.125252</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  61.225252</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  61.225252</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.3. Wait to allow for transmission of control signals: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.225252</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.225252</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.4. Wait for the read operation to complete and verify that the received value is the one which was previously set to be written: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.225252</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.225252</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 2 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.225252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::MemNvReadCurrValue': = 0, condition: == 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.225252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::MemNvPendingExtended': = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  62.225252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check variable 'valueToWrite' value passed: = 15, condition: &lt; 31</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  62.225252</td>
            <td class="NumberCell">15</td>
            <td class="DefaultCell">For-loop condition is fulfilled, executing loop body.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.5. Set the new value to write: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.225252</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvStoreValue': 15</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.225252</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.6. Trigger writing of the value. After the write is complete the read operation is started automatically: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.225252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvStore': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.225252</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.325252</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.325252</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.325252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::MemNvStore' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.325252</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.325252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvStore': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.325252</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.425252</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  62.425252</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.7. Wait to allow for transmission of control signals: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.425252</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.425252</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.8. Wait for the read operation to complete and verify that the received value is the one which was previously set to be written: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.425252</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.425252</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 2 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.425252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::MemNvReadCurrValue': = 15, condition: == 15</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.425252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::MemNvPendingExtended': = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  63.425252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check variable 'valueToWrite' value passed: = 30, condition: &lt; 31</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  63.425252</td>
            <td class="NumberCell">30</td>
            <td class="DefaultCell">For-loop condition is fulfilled, executing loop body.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.9. Set the new value to write: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.425252</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Set specified value.<br>System variable 'StartApplication::MemNvStoreValue': 30</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.425252</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.10. Trigger writing of the value. After the write is complete the read operation is started automatically: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.425252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvStore': 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.425252</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Stimulation of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.525252</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.525252</td>
            <td class="NumberCell">2</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.525252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check system variable 'StartApplication::MemNvStore' value passed: = 1, condition: == 1</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.525252</td>
            <td class="NumberCell">3</td>
            <td class="DefaultCell">Validation of the expected parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.525252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Successfully set system variable 'StartApplication::MemNvStore': 0</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.525252</td>
            <td class="NumberCell">4</td>
            <td class="DefaultCell">Reset of the input parameters</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.625252</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=100ms (max=100ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  63.625252</td>
            <td class="NumberCell">5</td>
            <td class="DefaultCell">Waited for 100 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.11. Wait to allow for transmission of control signals: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  64.625252</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell"> Elapsed time=1000ms (max=1000ms)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  64.625252</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for 1000 ms.</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr class="PositiveResultBackground">
            <td style="text-indent: 40px;" class="ResultBlockHeading" colspan="4">
              <big class="Heading4">1.12. Wait for the read operation to complete and verify that the received value is the one which was previously set to be written: Passed</big>
            </td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  64.625252</td>
            <td class="NumberCell">Resume reason</td>
            <td class="DefaultCell">Immediately resumed on setup of wait condition.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  64.625252</td>
            <td class="NumberCell">1</td>
            <td class="DefaultCell">Waited for occurrence of 2 value condition(s).</td>
            <td class="PositiveResultCell">pass</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  64.625252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::MemNvReadCurrValue': = 30, condition: == 30</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 40px;" class="DefineCell">  64.625252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">System variable 'StartApplication::MemNvPendingExtended': = 4 (READ_FINISHED), condition: == 4 (READ_FINISHED)</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  64.625252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">Check variable 'valueToWrite' value failed: = 45, condition: &lt; 31</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell">  64.625252</td>
            <td class="NumberCell"></td>
            <td class="DefaultCell">For-loop condition is not fulfilled, leaving loop.</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td style="text-indent: 20px;" class="DefineCell" nowrap>  64.625252</td>
            <td class="DefaultCell"></td>
            <td class="DefaultCell">Control function end</td>
            <td class="DefaultCell">-</td>
          </tr>
          <tr>
            <td class="PatternSeparator" colspan="4"></td>
          </tr>
        </table>
      </div>
      <div id="div_3.1.2">
        <table class="TestGroupHeadingBackground">
          <tr>
            <td>
              <big class="Heading3">Completion of Test Case</big>
            </td>
          </tr>
        </table>
        <div class="Indentation">
          <table class="ResultTable">
            <tr>
              <th class="TableHeadingCell" width="1px">Timestamp</th>
              <th class="TableHeadingCell" width="1px">Test Step</th>
              <th class="TableHeadingCell" width="auto">Description</th>
              <th class="TableHeadingCell" width="1px">Result</th>
            </tr>
            <tr>
              <td class="DefineCell">  64.625252</td>
              <td class="DefaultCell"></td>
              <td class="DefaultCell">Value of system variable 'StartApplication::ErrorLog'  is </td>
              <td class="DefaultCell">-</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    <table class="GroupEndTable">
      <tr>
        <td>End of Test Group: Write and Read Nonvolatile Memory</td>
      </tr>
    </table>
    <table class="GroupEndTable">
      <tr>
        <td>End of Test Group: Usecase MEM</td>
      </tr>
    </table>
    <table class="SubHeadingTable">
      <tr>
        <td>
          <div class="Heading2">End of Report</div>
        </td>
      </tr>
    </table>
  </body>
</html>
