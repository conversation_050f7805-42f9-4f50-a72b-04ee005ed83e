<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <ELEMENTS>
        <BSW-IMPLEMENTATION UUID="4cd6a036-9bc1-4174-b747-2a4f935551a6">
          <SHORT-NAME>BswM_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>13.00.00</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/BswM_ib_bswmd/BswModuleDescriptions/BswM/BswMBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="3caf2919-5d52-423f-9faf-7b78a8fcdb88">
          <SHORT-NAME>CanIf_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>6.23.00</SW-VERSION>
          <USED-CODE-GENERATOR>cMSR</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/CanIf_ib_bswmd/BswModuleDescriptions/CanIf/CanIf_Behavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="f08dba86-dea1-4578-b3c5-7afff8e5298c">
          <SHORT-NAME>CanNm_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>10.00.00</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/CanNm_ib_bswmd/BswModuleDescriptions/CanNm/CanNmBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="b0f546c7-6c38-4e9f-a1bd-0df9c9622c3a">
          <SHORT-NAME>CanSM_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>3.00.01</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/CanSM_ib_bswmd/BswModuleDescriptions/CanSM/CanSMBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="2c9f79b8-f406-4b05-b66d-eba85f3b3340">
          <SHORT-NAME>CanTp_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>3.05.00</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/CanTp_ib_bswmd/BswModuleDescriptions/CanTp/CanTpBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="f9df0718-c78b-43e7-93b9-570fa7d3efcb">
          <SHORT-NAME>Com_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>18.00.00</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/Com_ib_bswmd/BswModuleDescriptions/Com/ComBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="b484d690-bf7b-4b49-8cc0-23ce231b62c3">
          <SHORT-NAME>ComM_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>13.00.01</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/ComM_ib_bswmd/BswModuleDescriptions/ComM/ComMBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="487e2e0d-92c2-4469-a75f-21e4b5b270a5">
          <SHORT-NAME>EcuM_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>10.00.01</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/EcuM_ib_bswmd/BswModuleDescriptions/EcuM/EcuMBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="74f03833-872a-4b3f-8657-b465513f1d45">
          <SHORT-NAME>FrIf_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>5.02.00</SW-VERSION>
          <USED-CODE-GENERATOR>cMSR</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/FrIf_ib_bswmd/BswModuleDescriptions/FrIf/FrIfBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="eda7af4f-25d7-482e-bda1-863612107f63">
          <SHORT-NAME>FrNm_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>8.00.00</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/FrNm_ib_bswmd/BswModuleDescriptions/FrNm/FrNmBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="f4e2f38c-c823-4e5f-a798-7d5a3c57b7c9">
          <SHORT-NAME>FrSM_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>3.00.00</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/FrSM_ib_bswmd/BswModuleDescriptions/FrSM/FrSMBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="06f2a960-c62e-456a-80ee-db03a601390a">
          <SHORT-NAME>LinIf_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>8.00.00</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/LinIf_ib_bswmd/BswModuleDescriptions/LinIf/LinIfBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="bfa9767d-4e2c-4c72-862f-27bd21e3e19c">
          <SHORT-NAME>LinTp_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>8.00.00</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/LinIf_ib_bswmd/BswModuleDescriptions/LinTp/LinTpBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="26dfa497-423b-4707-a1d7-ff2344fe1332">
          <SHORT-NAME>LinNm_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>2.03.00</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/LinNm_ib_bswmd/BswModuleDescriptions/LinNm/LinNmBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="489c368f-1652-4e5d-8e61-f065cddaadaa">
          <SHORT-NAME>LinSM_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>7.00.02</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/LinSM_ib_bswmd/BswModuleDescriptions/LinSM/LinSMBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="10e1d426-5838-4dbc-827a-9f03994ff3ed">
          <SHORT-NAME>Nm_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>13.00.00</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/Nm_ib_bswmd/BswModuleDescriptions/Nm/NmBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="8cbb61b5-1e82-4754-827d-46a0b8b7e599">
          <SHORT-NAME>NvM_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>6.02.03</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/NvM_ib_bswmd/BswModuleDescriptions/NvM/NvMBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
        <BSW-IMPLEMENTATION UUID="4a156671-5151-4f19-a8b4-579effc27f33">
          <SHORT-NAME>PduR_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>15.04.01</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.01.02</AR-RELEASE-VERSION>
          <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/PduR_ib_bswmd/BswModuleDescriptions/PduR/PduRBehavior</BEHAVIOR-REF>
        </BSW-IMPLEMENTATION>
      </ELEMENTS>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="e5d123b2-df67-403b-ba24-7182094bfa2c">
          <SHORT-NAME>Fr_TricoreEray</SHORT-NAME>
          <ELEMENTS>
            <BSW-IMPLEMENTATION UUID="421dcf37-36be-4e79-8837-8c41b9db0f88">
              <SHORT-NAME>Fr_Impl</SHORT-NAME>
              <MC-SUPPORT/>
              <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
              <SW-VERSION>5.00.03</SW-VERSION>
              <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
              <VENDOR-ID>30</VENDOR-ID>
              <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
              <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/Fr_TricoreEray_ib_bsmwd/BswModuleDescriptions/Fr/Fr_TricoreErayBehavior</BEHAVIOR-REF>
            </BSW-IMPLEMENTATION>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="ac122923-8fb5-47c1-b860-52093755d12e">
          <SHORT-NAME>Lin_Tricore</SHORT-NAME>
          <ELEMENTS>
            <BSW-IMPLEMENTATION UUID="766f6148-df55-4023-b644-04b84dc79b3c">
              <SHORT-NAME>Lin_Impl</SHORT-NAME>
              <MC-SUPPORT/>
              <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
              <SW-VERSION>15.00.00</SW-VERSION>
              <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
              <VENDOR-ID>30</VENDOR-ID>
              <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
              <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/Lin_Tricore/Lin_ib_bswmd/BswModuleDescriptions/Lin/LinBehavior</BEHAVIOR-REF>
            </BSW-IMPLEMENTATION>
          </ELEMENTS>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="79098f69-3951-4849-8c5d-32d572ea3f03">
              <SHORT-NAME>Lin_ib_bswmd</SHORT-NAME>
              <AR-PACKAGES>
                <AR-PACKAGE UUID="6d6a7978-81a3-4875-ac83-42f7be97284d">
                  <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
                  <ELEMENTS>
                    <BSW-MODULE-DESCRIPTION UUID="ea9cb9cf-41ed-4b1a-b15b-379b89297af9">
                      <SHORT-NAME>Lin</SHORT-NAME>
                      <INTERNAL-BEHAVIORS>
                        <BSW-INTERNAL-BEHAVIOR UUID="d8f3e17e-a73c-46ab-8f50-baa52e9f6bd6">
                          <SHORT-NAME>LinBehavior</SHORT-NAME>
                        </BSW-INTERNAL-BEHAVIOR>
                      </INTERNAL-BEHAVIORS>
                    </BSW-MODULE-DESCRIPTION>
                  </ELEMENTS>
                </AR-PACKAGE>
              </AR-PACKAGES>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="64fad728-8d65-4c94-b680-640a1f3c087f">
          <SHORT-NAME>BswM_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="1a9703e2-f7a7-415e-a175-03207aef5f37">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="524c8d96-7825-4f0d-9e61-1c4d6600aca4">
                  <SHORT-NAME>BswM</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="b99c70c6-1094-49bc-9ebf-b87de3ec8230">
                      <SHORT-NAME>BswMBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="80d365fd-ef7c-4f89-8832-b5c05eba3b23">
                          <SHORT-NAME>BSWM_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="ea5bf665-f106-4fd7-a7d2-02a86c92d917">
                          <SHORT-NAME>BswM_MainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/BswM_ib_bswmd/BswModuleDescriptions/BswM_MainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="1de5031b-e4b2-4538-aa8c-902af7a133b2">
                          <SHORT-NAME>BswM_MainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/BswM_ib_bswmd/BswModuleDescriptions/BswM/BswMBehavior/BswM_MainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="2a1c4a81-5d83-4156-b04f-dfce5757b16a">
                  <SHORT-NAME>BswM_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="15e4a2eb-1bde-419a-9563-e76d404e57da">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="ca4fabd2-6a3a-4516-893e-1e5eee1008e2">
          <SHORT-NAME>CanIf_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="7eeb0321-1b82-4a62-9c69-f310b2002820">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="d878d989-e864-4ee2-99d7-03e42476a454">
                  <SHORT-NAME>CanIf</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="c9236a53-3e15-4bfb-a96c-d39702ade1f4">
                      <SHORT-NAME>CanIf_Behavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="bf895ee1-7ec4-42fe-9c58-2766a5bc310c">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="a19ed7ae-122f-4d80-aa90-ade63842949e">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_1</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="0b0d6626-584d-43c1-a8e7-5a3c56299f13">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_2</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="580fd4d9-aae5-477e-908f-68c25c479e07">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_3</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="2cf62713-23f4-4f16-a40a-a4ce5a52114a">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_4</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="e1c79637-c5d7-4d51-9600-5a53ed7fd0b2">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_5</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="a1359e97-3a5a-43c7-8718-638259ca7828">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_6</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="c27eb86d-f7be-4172-be5a-9aa563ef680b">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_7</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="13e96995-d4a5-4286-accc-c5a1ae3dc96c">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="439d8ae6-8014-4334-8b66-cc96dd7e7091">
          <SHORT-NAME>CanNm_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="067a6a8d-69f3-4c66-9bdf-f4da046e27e3">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="f60e66e8-7943-477e-ba79-6cc2087c5934">
                  <SHORT-NAME>CanNm</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="49381ed4-1cb0-4cf6-bd90-fa1978aa41e7">
                      <SHORT-NAME>CanNmBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="f6fc5974-65f3-4af8-9821-f12b8734e37c">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_0</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption by any interrupt allowed (i.e. global interrupt locks).</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="c61f92b4-a29d-4937-be1e-1dbf4c7c3d82">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_1</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption of CanNm_MainFunction or CanNm_RequestBusSynchronization by CanNm_SetUserData or CanNm_SetSleepReadyBit allowed.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="859bea19-82d0-4403-96cf-4997fa85bedd">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_2</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption of CanNm_SetUserData by CanNm_MainFunction or CanNm_RequestBusSynchronization allowed.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="6ecea4ef-62fc-4d5c-b23e-d638d9019c3c">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_3</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption of CanNm_SetSleepReadyBit by CanNm_MainFunction or CanNm_RequestBusSynchronization allowed.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="0eaf8534-978d-49f0-8baa-b74a39418a2b">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_4</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption of CanNm_RxIndication by CanNm_GetUserData or CanNm_GetPduData allowed.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="ade60f58-5f31-4e0d-93f7-ff7db25782ab">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_5</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption of CanNm_GetUserData or CanNm_GetPduData by CanNm_RxIndication allowed.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="1ec85938-7089-4b9f-b769-fd19fe673224">
                          <SHORT-NAME>CANNM_EXCLUSIVE_AREA_6</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption of CanNm_MainFunction by CanNm_RequestBusSynchronization allowed. Note: This critical section can be left empty when CanNm_MainFunction and Nm_MainFunction have the same task.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="eac9cde3-4648-4d4a-b334-8df1146eae70">
                          <SHORT-NAME>CanNm_MainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/CanNm_ib_bswmd/BswModuleDescriptions/CanNm_MainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="152703de-013b-4eda-9e6c-bbdc0c4467bb">
                          <SHORT-NAME>CanNm_MainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/CanNm_ib_bswmd/BswModuleDescriptions/CanNm/CanNmBehavior/CanNm_MainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="5caffa8d-434e-4ddf-8b75-ae285c919ecf">
                  <SHORT-NAME>CanNm_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="804e7dba-6bff-4674-8bdc-059d759df5d8">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="13acff72-8a09-410c-b5a4-d74d053ffc7f">
          <SHORT-NAME>CanSM_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="a02388a0-080f-4f74-b896-a0a8bef2aa9b">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="c449f1d2-562c-4b84-a382-0f9752f65bc6">
                  <SHORT-NAME>CanSM</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="2ca98291-c062-4407-b149-48af6f6f0958">
                      <SHORT-NAME>CanSMBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="40bee852-bb16-4e99-8a48-284b6a627238">
                          <SHORT-NAME>CANSM_EXCLUSIVE_AREA_1</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="7fbff59e-4070-42fe-9169-dafd671818ab">
                          <SHORT-NAME>CANSM_EXCLUSIVE_AREA_2</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="c151a962-17d0-4260-8be1-a33a0514e82e">
                          <SHORT-NAME>CANSM_EXCLUSIVE_AREA_3</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="3bc3a078-019b-479d-9249-eadae10c4cd3">
                          <SHORT-NAME>CANSM_EXCLUSIVE_AREA_4</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="efdf211c-4642-4979-afec-4168c3bdcddb">
                          <SHORT-NAME>CANSM_EXCLUSIVE_AREA_5</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="f75a19ea-21df-45dc-b006-07f71b938e8b">
                          <SHORT-NAME>CANSM_EXCLUSIVE_AREA_6</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="f7e74cc4-cd48-49b2-9881-7255ea65f19a">
                          <SHORT-NAME>CanSM_MainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/CanSM_ib_bswmd/BswModuleDescriptions/CanSM_MainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="d268bbba-4afe-46be-9385-8bdf67eda6f8">
                          <SHORT-NAME>CanSM_MainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/CanSM_ib_bswmd/BswModuleDescriptions/CanSM/CanSMBehavior/CanSM_MainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="4ad69f1d-ae52-421d-9287-c2df68349082">
                  <SHORT-NAME>CanSM_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="22ed3b38-ce5e-4519-8ff3-bc97c2c5a872">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="3a4d36ed-bb7b-4a2a-b118-9e291850f433">
          <SHORT-NAME>CanTp_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="9b9bdc8e-bb66-4486-b285-5985ba7f1921">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="0517be99-266b-4160-a532-00426dfafbcd">
                  <SHORT-NAME>CanTp</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="da0bc730-4227-44ec-b8e3-6092438ecc95">
                      <SHORT-NAME>CanTpBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="f237a80d-80a5-4c40-b41e-be290435da34">
                          <SHORT-NAME>CANTP_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="6e7e6c77-8dda-445c-abab-cd2887748a1e">
                          <SHORT-NAME>CanTp_MainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/CanTp_ib_bswmd/BswModuleDescriptions/CanTp_MainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="00697560-d860-4a6b-ac12-8446f725ca66">
                          <SHORT-NAME>CanTp_MainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/CanTp_ib_bswmd/BswModuleDescriptions/CanTp/CanTpBehavior/CanTp_MainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.005</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="859f0490-b517-4da9-b076-db3325caf79e">
                  <SHORT-NAME>CanTp_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="4c929e22-4d5b-4a09-a673-d4791c2cca55">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="bcea21f9-c804-4d6e-af74-fe0bc787b991">
          <SHORT-NAME>ComM_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="a6748a87-2b10-42be-b938-4d4292d3e5ba">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="4d2d3154-e253-476c-b048-170c81d95d05">
                  <SHORT-NAME>ComM</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="1a0ce095-097c-45f5-8c14-83403b43fe13">
                      <SHORT-NAME>ComMBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="b0848bb6-a1b4-4a60-9dac-b6cb577c9d7c">
                          <SHORT-NAME>COMM_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="ec207562-f16c-4626-86a9-fd25b24fa68f">
                          <SHORT-NAME>COMM_EXCLUSIVE_AREA_1</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="6e85cced-474e-4c43-8efe-18f377c2096c">
                          <SHORT-NAME>ComM_MainFunction_0</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/ComM_ib_bswmd/BswModuleDescriptions/ComM_MainFunction_0</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                        <BSW-SCHEDULABLE-ENTITY UUID="ace8ef67-59ae-47bc-9a80-4efe1492d174">
                          <SHORT-NAME>ComM_MainFunction_1</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/ComM_ib_bswmd/BswModuleDescriptions/ComM_MainFunction_1</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                        <BSW-SCHEDULABLE-ENTITY UUID="fd130ad9-9c0d-4c25-a0af-d11324d7f631">
                          <SHORT-NAME>ComM_MainFunction_2</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/ComM_ib_bswmd/BswModuleDescriptions/ComM_MainFunction_2</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="76f712a7-05ab-4e70-aef8-1e218f651cd6">
                          <SHORT-NAME>ComM_MainFunction_0TimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/ComM_ib_bswmd/BswModuleDescriptions/ComM/ComMBehavior/ComM_MainFunction_0</STARTS-ON-EVENT-REF>
                          <PERIOD>0.02</PERIOD>
                        </BSW-TIMING-EVENT>
                        <BSW-TIMING-EVENT UUID="0cf186af-3d9f-48f4-89db-cf2ec2be4ee1">
                          <SHORT-NAME>ComM_MainFunction_1TimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/ComM_ib_bswmd/BswModuleDescriptions/ComM/ComMBehavior/ComM_MainFunction_1</STARTS-ON-EVENT-REF>
                          <PERIOD>0.02</PERIOD>
                        </BSW-TIMING-EVENT>
                        <BSW-TIMING-EVENT UUID="74fdbdd9-988f-4a09-8edb-5382e0a341e0">
                          <SHORT-NAME>ComM_MainFunction_2TimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/ComM_ib_bswmd/BswModuleDescriptions/ComM/ComMBehavior/ComM_MainFunction_2</STARTS-ON-EVENT-REF>
                          <PERIOD>0.02</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="501783b4-8b04-4aa3-9f2d-c752ffaba3ea">
                  <SHORT-NAME>ComM_MainFunction_0</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="0ce1d4b0-dcbb-48ea-a5c9-5c8799b6ff4e">
                  <SHORT-NAME>ComM_MainFunction_1</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="6de8d5e5-c155-496d-9f27-d6b0287e2a9f">
                  <SHORT-NAME>ComM_MainFunction_2</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="49ecef5c-feba-4225-a030-702a4fb87951">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="b1268a98-eb55-4582-9384-43e6eed00824">
          <SHORT-NAME>Com_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="00d5752d-3719-4775-a688-8f70d3a0a8ab">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="7af70b8f-3d8e-4cb5-97fe-2750b5b8a7c8">
                  <SHORT-NAME>Com</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="6aedfc84-678a-4251-bc58-18ad4f51db61">
                      <SHORT-NAME>ComBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="fb07c604-9f1b-4521-9467-64a10e0325e8">
                          <SHORT-NAME>COM_EXCLUSIVE_AREA_TX</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">This critical section protects Tx ressources that can be accessed from various contexts. Therefore the critical section enclosed with COM_EXCLUSIVE_AREA_TX should never be interrupted by any Com API which accesses Tx ressources. For more details, see DocTechRef.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="60e29b08-e0dc-4d91-b56c-1885e8a7c38c">
                          <SHORT-NAME>COM_EXCLUSIVE_AREA_RX</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">This critical section protects Rx ressources that can be accessed from various contexts.Therefore the critical section enclosed with COM_EXCLUSIVE_AREA_RX should never be interrupted by any Com API which accesses Rx ressources. For more details, see DocTechRef.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="7ddb69f3-fea9-4817-b9c6-84998a76fb32">
                          <SHORT-NAME>COM_EXCLUSIVE_AREA_BOTH</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">This critical section protects Rx and Tx ressources that are being accessed in context of Com_MainFunctionRouteSignals for signal gateway routings or description routings with configured deferred description source.  For more details, see DocTechRef.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="a8de9901-2b2d-4771-b38d-0f6fcde64f7a">
                          <SHORT-NAME>Com_MainFunctionTx</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Com_ib_bswmd/BswModuleDescriptions/Com_MainFunctionTx</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                        <BSW-SCHEDULABLE-ENTITY UUID="b228c392-3872-4e6c-9ce5-a36056fadd16">
                          <SHORT-NAME>Com_MainFunctionRx</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Com_ib_bswmd/BswModuleDescriptions/Com_MainFunctionRx</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="7226f9e3-181f-4dc6-b52c-ebbf19394dbe">
                          <SHORT-NAME>Com_MainFunctionTxTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/Com_ib_bswmd/BswModuleDescriptions/Com/ComBehavior/Com_MainFunctionTx</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                        <BSW-TIMING-EVENT UUID="b7bc15f4-9f14-4b0e-a49e-5c7d52e573f8">
                          <SHORT-NAME>Com_MainFunctionRxTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/Com_ib_bswmd/BswModuleDescriptions/Com/ComBehavior/Com_MainFunctionRx</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="008e59a3-d2d2-420b-a9e6-f9fe8464bd5a">
                  <SHORT-NAME>Com_MainFunctionTx</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="a952b6ec-0ea0-4f4a-9355-557f3076117d">
                  <SHORT-NAME>Com_MainFunctionRx</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="1ce9a861-1cfd-4aa5-8e6f-8960f1c936b9">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="93e88eb8-2ba0-4237-8493-d174d4fbbb45">
          <SHORT-NAME>EcuM_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="3d710a2b-1d0a-4e8a-a24d-6b64136ada33">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="e110f2e1-4913-48fb-8730-3788cac4486f">
                  <SHORT-NAME>EcuM</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="ac7802d3-2969-4a17-98c3-a0f0a4bd5509">
                      <SHORT-NAME>EcuMBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="19a35f52-683e-41fe-80be-daccd96b45c8">
                          <SHORT-NAME>ECUM_EXCLUSIVE_AREA_0</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">This exclusive area shall disable all wake up interrupts to prevent overwriting of some global variables.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="9f8ddfd9-c4dc-4fc0-83d3-4facbf9e7d48">
                          <SHORT-NAME>ECUM_EXCLUSIVE_AREA_1</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">This exclusive area is only used in case of multi core and if the the EcuM is configured to handle also the slave cores. It must be configured as a spinlock to allow access to some variables simultaneously from multiple cores.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="204fc574-6014-4940-b645-************">
                          <SHORT-NAME>ECUM_EXCLUSIVE_AREA_2</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">This exclusive area is used for accessing the clock variable. It shall disable GPT interrupts and task changes. Only necessary if 32 Bit access is not atomic and Alarm Clock is present.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="f20fb013-2a34-4cc5-b0ea-f8a9b611c32e">
                          <SHORT-NAME>EcuM_MainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/EcuM_ib_bswmd/BswModuleDescriptions/EcuM_MainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="e1565524-2b0d-47b1-a588-05c92bc2fe4a">
                          <SHORT-NAME>EcuM_MainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/EcuM_ib_bswmd/BswModuleDescriptions/EcuM/EcuMBehavior/EcuM_MainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="ac93eeef-9aa8-42bb-b296-1f2b6e6948a9">
                  <SHORT-NAME>EcuM_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="3ee8295c-deb9-4dea-83ce-29401466f760">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="b5e281e3-4bd8-4c2f-b944-881c96994b0a">
          <SHORT-NAME>FrIf_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="3878e788-c67c-4232-baeb-190e3cf4f2af">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="d1c7de8f-529e-44f4-8668-4ff61d94aafd">
                  <SHORT-NAME>FrIf</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="d06a0a16-02a8-4629-8733-5bb2f0dd876c">
                      <SHORT-NAME>FrIfBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="d4c263a4-3ee2-47a9-bd6e-006650b09334">
                          <SHORT-NAME>FRIF_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="0d8a209a-ee6f-4e44-9cba-5dc0ec66873f">
                          <SHORT-NAME>FRIF_EXCLUSIVE_AREA_1</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="b4c0b64a-bd32-41c4-8f44-abf401efb7a3">
                          <SHORT-NAME>FRIF_EXCLUSIVE_AREA_2</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="0ab99abc-38ca-40da-8c3f-22513db6d6e0">
                          <SHORT-NAME>FrIf_MainFunction_0</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/FrIf_ib_bswmd/BswModuleDescriptions/FrIf_MainFunction_0</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="32995e5b-897f-415c-9a65-182dd05c4358">
                          <SHORT-NAME>FrIf_MainFunction_0TimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/FrIf_ib_bswmd/BswModuleDescriptions/FrIf/FrIfBehavior/FrIf_MainFunction_0</STARTS-ON-EVENT-REF>
                          <PERIOD>0.005</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="f2116ced-4667-4004-a907-36986bec299f">
                  <SHORT-NAME>FrIf_MainFunction_0</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="909dee41-30c6-4921-abc3-e6d36c552b8d">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="0dd0c90b-8d3f-4c1c-b6f9-3ea86d00034f">
          <SHORT-NAME>FrNm_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="64d06f06-b453-4968-aa9b-2009c4a03272">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="4119ceb9-2e0e-43f8-8cda-e8ca59f563e6">
                  <SHORT-NAME>FrNm</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="c6eb2153-4e43-4651-b640-b181fa0199f6">
                      <SHORT-NAME>FrNmBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="f6625014-3381-4da3-8c91-094709c81496">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_0</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Protects the global Pn Cluster requests from being modified from FrNm_RxIndication or FrNm_StartupError while copying to temporary buffer.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="4f9d80ea-d2ca-4437-a8d5-c459b522d833">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_1</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Protects the global TX message data from being modified while copying to the SDU.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="88dc0753-3727-42c0-9a34-8f87553a412c">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_2</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Protects the global TX message buffer from being read while user data is written to it.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="5ee4c9ae-728c-4c9c-9ab7-df58d39b0831">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_3</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Protects the global TX message buffer from being read while user data is written to it.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="d3ecf2b3-da16-4223-a782-94e18e950d39">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_4</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Ensures that the global RX message buffer is not read while data is written to it.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="2b04f3d9-41b3-41b1-819a-142b0a7d0f85">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_5</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Ensures that the global RX message buffer is not read while data is written to it.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="87328298-3cfe-4c99-ab98-e13393ac26d9">
                          <SHORT-NAME>FRNM_EXCLUSIVE_AREA_6</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Ensures that the global RX message buffer is not written while user data is read from it.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="8d9571c8-a720-4287-b8ed-adc0ba6d352a">
                          <SHORT-NAME>FrNm_MainFunction_0</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/FrNm_ib_bswmd/BswModuleDescriptions/FrNm_MainFunction_0</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="915d272f-f9aa-4fb2-ac98-27915d105bb9">
                          <SHORT-NAME>FrNm_MainFunction_0TimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/FrNm_ib_bswmd/BswModuleDescriptions/FrNm/FrNmBehavior/FrNm_MainFunction_0</STARTS-ON-EVENT-REF>
                          <PERIOD>0.005</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="6de0868e-84b6-4a57-9dea-25f1f36abe31">
                  <SHORT-NAME>FrNm_MainFunction_0</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="fa0ca78e-e887-4b52-8f3b-e1dc746e2b7b">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="dddf6112-9e97-448f-a6ec-bf177fcc5ed9">
          <SHORT-NAME>FrSM_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="d2dacda5-6a15-4d1b-97c8-a16d6543681b">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="313791f3-4a94-4e4d-9988-144c2f07f9f6">
                  <SHORT-NAME>FrSM</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="7f458218-1bf0-4eeb-b54d-53801db2cda1">
                      <SHORT-NAME>FrSMBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="7a8cca2a-3547-4509-b872-9bce0b324d83">
                          <SHORT-NAME>FRSM_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="cd571370-241f-4513-966b-c19bb4c0946d">
                          <SHORT-NAME>FRSM_EXCLUSIVE_AREA_1</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="50161f95-ac12-483b-8a22-1724f40f68e2">
                          <SHORT-NAME>FRSM_EXCLUSIVE_AREA_2</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="db7f10b1-44f9-49f6-8b0c-282161921d7c">
                          <SHORT-NAME>FRSM_EXCLUSIVE_AREA_3</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="683e33d4-e661-4447-a96d-1bd75ad24056">
                          <SHORT-NAME>FrSM_MainFunction_0</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/FrSM_ib_bswmd/BswModuleDescriptions/FrSM_MainFunction_0</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="46110127-3ab1-46c5-b853-ab82f83350c9">
                          <SHORT-NAME>FrSM_MainFunction_0TimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/FrSM_ib_bswmd/BswModuleDescriptions/FrSM/FrSMBehavior/FrSM_MainFunction_0</STARTS-ON-EVENT-REF>
                          <PERIOD>0.005</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="9f0b423c-7b7e-430a-84fe-b3cd379d34db">
                  <SHORT-NAME>FrSM_MainFunction_0</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="05326e2c-6e5c-46b3-a9bd-ec6998e0a6f5">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="5e13480c-1704-4d23-b58a-4e94fe9470fe">
          <SHORT-NAME>Fr_TricoreEray_ib_bsmwd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="b130fbfb-6cd4-44e8-89c3-725ca3198a63">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="c5bfe7a3-179c-41e9-944e-f9f29389c26a">
                  <SHORT-NAME>Fr</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="e8a46b28-70d3-4717-878a-daf53e23b211">
                      <SHORT-NAME>Fr_TricoreErayBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="6b16d97f-63c0-4fdc-82ab-6ce280b22f1f">
                          <SHORT-NAME>FR_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="993a892b-be7e-4d85-95e8-b4ac662f7974">
                          <SHORT-NAME>FR_EXCLUSIVE_AREA_1</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
              </ELEMENTS>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="96520603-376c-42a7-8302-ed0f0dec499d">
          <SHORT-NAME>Fr_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="749d0653-d5b5-4e61-8802-75bcb7ad82bb">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="5a9c1932-a1f1-4ac5-8cb6-81e84a717750">
          <SHORT-NAME>LinIf_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="f7b7f9ad-d189-4f7c-8eb5-a358fd0f60e3">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="8e40dc5d-64e2-452b-905a-d9049924bdb0">
                  <SHORT-NAME>LinIf</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="c8c46b4f-226e-4653-b48d-921b36607dc5">
                      <SHORT-NAME>LinIfBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="aa4d59f9-0889-457e-ac5b-653bef39d6d0">
                          <SHORT-NAME>LINIF_EXCLUSIVE_AREA_0</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Exclusive area for LinIf channel main function</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="49b45372-7bd0-4f8a-a5bf-602471884eb3">
                          <SHORT-NAME>LINIF_EXCLUSIVE_AREA_1</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Exclusive area for asynchronous API functions</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="9d515b43-3b15-494f-b50b-804f5655bd4f">
                          <SHORT-NAME>LINIF_EXCLUSIVE_AREA_2</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Exclusive area for callback functions from LIN driver in interrupt context</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="ef5b40b5-c4b1-4701-a9aa-************">
                          <SHORT-NAME>LinIf_MainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/LinIf_ib_bswmd/BswModuleDescriptions/LinIf_MainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="550f3678-9792-47d6-a54a-5d93beb9f074">
                          <SHORT-NAME>LinIf_MainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/LinIf_ib_bswmd/BswModuleDescriptions/LinIf/LinIfBehavior/LinIf_MainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.001</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="75bc95f2-ef16-4266-bd42-9b64183c23c0">
                  <SHORT-NAME>LinIf_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-DESCRIPTION UUID="7c2f8865-8099-4a16-902f-73c0a1042a09">
                  <SHORT-NAME>LinTp</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="c8b8ceca-98b2-4d79-a7f8-711539ae7110">
                      <SHORT-NAME>LinTpBehavior</SHORT-NAME>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="573da07d-5743-48a4-8d61-8df1a1a6164f">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="c67938c3-9301-4991-a818-f369dfb543ad">
          <SHORT-NAME>LinNm_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="26609ffe-d580-499a-a107-d33006c06587">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="be038177-5015-4ba6-9c02-be115cff92b7">
                  <SHORT-NAME>LinNm</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="a32539c7-4d98-450b-b569-7f59c1fd526a">
                      <SHORT-NAME>LinNmBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="3b125beb-ea6e-4541-982b-ea75927695dd">
                          <SHORT-NAME>LINNM_EXCLUSIVE_AREA_0</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Exclusive area for LinNm state machine handling</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="a47a1280-7550-4f09-9db4-3baeaec1faf1">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="3a4b8e7a-4766-4472-bb06-0e357bd8270c">
          <SHORT-NAME>LinSM_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="663e8737-c7f4-4bcd-90b3-5d8387838f0b">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="d703947c-db16-43b9-8a0d-416a84e627a2">
                  <SHORT-NAME>LinSM</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="7442df7e-9c75-4cec-a181-02c21b9aa0a2">
                      <SHORT-NAME>LinSMBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="d6863578-b646-4d0a-933b-fa369d9a903a">
                          <SHORT-NAME>LINSM_EXCLUSIVE_AREA_0</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Must lock task interrupts, if LinIf_MainFunction(), LinSM_MainFunction() or BswM action BswMLinScheduleSwitch priority level is higher than the level of ComM_MainFunction().</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="262b922a-b1af-419b-8b61-2f9d6a00dc9e">
                          <SHORT-NAME>LINSM_EXCLUSIVE_AREA_1</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Must lock task interrupts, if ComM_MainFunction(), LinSM_MainFunction() or BswM action BswMLinScheduleSwitch priority level is higher than the level of LinIf_MainFunction().</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="90e02364-1b6d-43b1-ad19-d35fdeb5967d">
                          <SHORT-NAME>LINSM_EXCLUSIVE_AREA_2</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Must lock task interrupts, if ComM_MainFunction(), LinIf_MainFunction() or BswM action BswMLinScheduleSwitch priority level is higher than the level of LinSM_MainFunction().</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="06ef6e49-115b-4a93-b45e-1cbf06b57d76">
                          <SHORT-NAME>LINSM_EXCLUSIVE_AREA_3</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Must lock task interrupts, if ComM_MainFunction(), LinIf_MainFunction() or LinSM_MainFunction() interrupt priority level is higher than the level of the BswM action BswMLinScheduleSwitch.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="3ede8bf4-b575-4751-99c1-46c5ea32da88">
                          <SHORT-NAME>LINSM_EXCLUSIVE_AREA_4</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Must lock task interrupts if the function LinSM_GotoSleepIndication() may be interrupted by any of the functions LinSM_MainFunction(), LinSM_RequestComMode(), LinSM_WakeupConfirmation() or LinSM_GotoSleepConfirmation().</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="*************-4c5a-827e-8898532eab6a">
                          <SHORT-NAME>LinSM_MainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/LinSM_ib_bswmd/BswModuleDescriptions/LinSM_MainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="4bac8bd4-0a63-4fd3-9a87-f92acc6a399d">
                          <SHORT-NAME>LinSM_MainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/LinSM_ib_bswmd/BswModuleDescriptions/LinSM/LinSMBehavior/LinSM_MainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="77e9e1db-7095-4900-abcf-4528eb19e9fa">
                  <SHORT-NAME>LinSM_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="3117ef14-d0c6-4b99-89dd-8f47590255d2">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="*************-4e49-9143-0b60a1e03279">
          <SHORT-NAME>LinTp_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="3debc214-6a4f-4731-a45d-a316a411a473">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="*************-4dd5-9fa6-ffdb3f3675c6">
          <SHORT-NAME>Lin_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="3e8e7145-10d9-491e-be89-d471d63128e3">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="1879bc2d-517a-45c8-8d96-67fff1264bb0">
          <SHORT-NAME>Nm_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="ccce9958-8bf7-4fad-af7a-c51b86a3e76d">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="5427908c-9530-4143-a0ed-8f297e22df01">
                  <SHORT-NAME>Nm</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="935df1e6-e158-4903-ba9c-0a2d53ef993a">
                      <SHORT-NAME>NmBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="78de0b08-9ca4-463d-8eae-7b808d1e7215">
                          <SHORT-NAME>NM_EXCLUSIVE_AREA_0</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">No interruption by any interrupt allowed</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="2c6c7795-777f-4c6d-9806-6ccaa9be9471">
                          <SHORT-NAME>NM_EXCLUSIVE_AREA_1</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Interrupt locks are required if any of these functions are executed in an interrupt context: Nm_NetworkMode, Nm_PrepareBusSleepMode, Nm_BusSleepMode, Nm_RemoteSleepIndication, Nm_RemoteSleepCancellation, Nm_CoordReadyToSleepIndication, Nm_CoordReadyToSleepCancellation</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="c3540908-75bb-43e9-8143-5baddd7e6cda">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="2e3e69d0-78d0-4b80-81eb-8af12f6fc5ed">
          <SHORT-NAME>NvM_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="8a0359d8-d96e-4c0a-be39-4a51d7ddb519">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="82985020-34dd-41fd-a657-00181b4310ed">
                  <SHORT-NAME>NvM</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="c4d96674-b805-490f-8274-728382b4228d">
                      <SHORT-NAME>NvMBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="28ce4412-4f6e-4ff9-845a-7767a0f9d3c6">
                          <SHORT-NAME>NVM_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="4b54fe32-6d09-414c-aedb-59b321fac1c6">
                          <SHORT-NAME>NvM_MainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/NvM_ib_bswmd/BswModuleDescriptions/NvM_MainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="68e5dbd3-f3ea-4cb1-b3f6-a53ba8a8e82e">
                          <SHORT-NAME>NvM_MainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/NvM_ib_bswmd/BswModuleDescriptions/NvM/NvMBehavior/NvM_MainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="bcd23b2d-9c0c-402d-b9a0-fb9139de1bf9">
                  <SHORT-NAME>NvM_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="b5e06912-2f8a-483c-8598-6c424f1a9a41">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="2b810398-7bbb-4f8d-8887-390f0b0d8c09">
          <SHORT-NAME>PduR_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="1fd2dd99-675c-4133-99ed-046e1960473a">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="ff27b7d3-8a96-47b7-87dd-b7f8deefc8c4">
                  <SHORT-NAME>PduR</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="85e3a596-1bd0-41a0-856e-bf3dbdf5d530">
                      <SHORT-NAME>PduRBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="dbab43d4-4f50-4c9d-8c5a-e318d259415a">
                          <SHORT-NAME>PDUR_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="c7e123f3-0228-4809-8bb7-dfcc015c148b">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
