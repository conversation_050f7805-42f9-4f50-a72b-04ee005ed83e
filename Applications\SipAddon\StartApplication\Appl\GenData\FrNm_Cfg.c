/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: FrNm
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: FrNm_Cfg.c
 *   Generation Time: 2025-08-05 10:37:16
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/


/* PRQA S 0779 EOF */ /* MD_MSR_Rule5.2_0779 */

#define FRNM_CFG_SOURCE

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/
#include "FrNm_Cfg.h"
#include "SchM_FrNm.h"
#include "PduR_Cfg.h"

#include "FrIf_Cfg.h"

#include "Nm_Cbk.h"

/**********************************************************************************************************************
 *  LOCAL DATA PROTOTYPES
 *********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: LOCAL DATA
**********************************************************************************************************************/

/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: LOCAL DATA
**********************************************************************************************************************/



/*********************************************************************************************************************
 *  GLOBAL DATA
 *********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL DATA
**********************************************************************************************************************/
/**********************************************************************************************************************
  FrNm_SysToNmChInd
**********************************************************************************************************************/
/** 
  \var    FrNm_SysToNmChInd
  \brief  System to FrNm Channel Indirection
*/ 
#define FRNM_START_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(FrNm_SysToNmChIndType, FRNM_CONST) FrNm_SysToNmChInd[3] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index     SysToNmChInd          */
  /*     0 */  FRNM_NO_SYSTONMCHIND,
  /*     1 */  FRNM_NO_SYSTONMCHIND,
  /*     2 */                    0u
};
#define FRNM_STOP_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_CurrentFrCycle
**********************************************************************************************************************/
/** 
  \var    FrNm_CurrentFrCycle
  \brief  Current Emulated / Actual FlexRay Cycle
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrNm_CurrentFrCycleUType, FRNM_VAR_NOINIT) FrNm_CurrentFrCycle;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/ComM/ComMConfigSet/CN_FlexRay_oChannel_A_24bd889a] */

#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_LastNetworkRequested
**********************************************************************************************************************/
/** 
  \var    FrNm_LastNetworkRequested
  \brief  Last Network Requested
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrNm_LastNetworkRequestedUType, FRNM_VAR_NOINIT) FrNm_LastNetworkRequested;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/ComM/ComMConfigSet/CN_FlexRay_oChannel_A_24bd889a] */

#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_ModuleInitialized
**********************************************************************************************************************/
#define FRNM_START_SEC_VAR_ZERO_INIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrNm_ModuleInitializedType, FRNM_VAR_ZERO_INIT) FrNm_ModuleInitialized = FALSE;  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define FRNM_STOP_SEC_VAR_ZERO_INIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_MsgConfirmationFlag
**********************************************************************************************************************/
/** 
  \var    FrNm_MsgConfirmationFlag
  \brief  Message Confirmation Flag
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrNm_MsgConfirmationFlagUType, FRNM_VAR_NOINIT) FrNm_MsgConfirmationFlag;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/ComM/ComMConfigSet/CN_FlexRay_oChannel_A_24bd889a] */

#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_MsgIndicationFlag
**********************************************************************************************************************/
/** 
  \var    FrNm_MsgIndicationFlag
  \brief  Message Indication Flag
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrNm_MsgIndicationFlagUType, FRNM_VAR_NOINIT) FrNm_MsgIndicationFlag;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/ComM/ComMConfigSet/CN_FlexRay_oChannel_A_24bd889a] */

#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_NetworkRequested
**********************************************************************************************************************/
/** 
  \var    FrNm_NetworkRequested
  \brief  Network Requested
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrNm_NetworkRequestedUType, FRNM_VAR_NOINIT) FrNm_NetworkRequested;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/ComM/ComMConfigSet/CN_FlexRay_oChannel_A_24bd889a] */

#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_NetworkRestartFlag
**********************************************************************************************************************/
/** 
  \var    FrNm_NetworkRestartFlag
  \brief  Network Restart Flag
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrNm_NetworkRestartFlagUType, FRNM_VAR_NOINIT) FrNm_NetworkRestartFlag;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/ComM/ComMConfigSet/CN_FlexRay_oChannel_A_24bd889a] */

#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_NetworkTimer
**********************************************************************************************************************/
/** 
  \var    FrNm_NetworkTimer
  \brief  Network Timer
*/ 
#define FRNM_START_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrNm_NetworkTimerUType, FRNM_VAR_NOINIT) FrNm_NetworkTimer;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/ComM/ComMConfigSet/CN_FlexRay_oChannel_A_24bd889a] */

#define FRNM_STOP_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_NmState
**********************************************************************************************************************/
/** 
  \var    FrNm_NmState
  \brief  Current state of the state machine
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrNm_NmStateUType, FRNM_VAR_NOINIT) FrNm_NmState;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/ComM/ComMConfigSet/CN_FlexRay_oChannel_A_24bd889a] */

#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_RxMessageData
**********************************************************************************************************************/
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrNm_RxMessageDataType, FRNM_VAR_NOINIT) FrNm_RxMessageData[8];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers, /ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers_CBV] */
  /*     1 */  /* [/ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers, /ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers_NID] */
  /*     2 */  /* [/ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers, /ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers_UserData] */
  /*   ... */  /* [/ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers, /ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers_UserData] */
  /*     7 */  /* [/ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers, /ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers_UserData] */

#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_RxMessageVote
**********************************************************************************************************************/
/** 
  \var    FrNm_RxMessageVote
  \brief  Rx NM message vote buffer
*/ 
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrNm_RxMessageVoteUType, FRNM_VAR_NOINIT) FrNm_RxMessageVote;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/ComM/ComMConfigSet/CN_FlexRay_oChannel_A_24bd889a] */

#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_TimeoutTimer
**********************************************************************************************************************/
/** 
  \var    FrNm_TimeoutTimer
  \brief  Timer for NM Algorithm
*/ 
#define FRNM_START_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrNm_TimeoutTimerUType, FRNM_VAR_NOINIT) FrNm_TimeoutTimer;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/ComM/ComMConfigSet/CN_FlexRay_oChannel_A_24bd889a] */

#define FRNM_STOP_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  FrNm_TxMessageData
**********************************************************************************************************************/
#define FRNM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(FrNm_TxMessageDataType, FRNM_VAR_NOINIT) FrNm_TxMessageData[8];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers, /ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers_CBV] */
  /*     1 */  /* [/ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers, /ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers_NID] */
  /*     2 */  /* [/ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers, /ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers_UserData] */
  /*   ... */  /* [/ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers, /ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers_UserData] */
  /*     7 */  /* [/ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers, /ActiveEcuC/FrNm/FrNmChannelConfig/FlexRay_6444d38e/FrNmChannelIdentifiers_UserData] */

#define FRNM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */


/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: GLOBAL DATA
**********************************************************************************************************************/


/**********************************************************************************************************************
 *  GLOBAL FUNCTIONS
 *********************************************************************************************************************/

/* ----------- Generated main function definitions(s) ----------- */
#define FRNM_START_SEC_CODE
#include "MemMap.h"  /* PRQA S 5087 */ /* MD_MSR_MemMap */

/***********************************************************************************************************************
 *  FrNm_MainFunction_0
 **********************************************************************************************************************/
/*! \brief      Main functions of the network management for channel index 0..x
 *              Cyclic task for incrementation of the counters
 *  \pre        NM is initialized
 *  \context    Function could be called from from task level only
 *  \note       Generated for each FrNm instance
 *  \note       Scheduled by the BSW scheduler
 **********************************************************************************************************************/
FUNC( void, FRNM_CODE ) FrNm_MainFunction_0( void ) 
{ 

#if defined ( FRNM_OPTIMIZE_CHANNEL_ENABLED )
  FrNm_LocalMainFunction();
#else
  FrNm_LocalMainFunction( (NetworkHandleType)0u ); 
#endif 

}


#define FRNM_STOP_SEC_CODE
#include "MemMap.h"  /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 *  END OF FILE: FrNm_Cfg.c
 *********************************************************************************************************************/

