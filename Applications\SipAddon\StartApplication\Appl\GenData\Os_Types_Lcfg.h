/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Os
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: Os_Types_Lcfg.h
 *   Generation Time: 2025-08-05 10:37:19
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

#ifndef OS_TYPES_LCFG_H
# define OS_TYPES_LCFG_H

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/

/* AUTOSAR includes */
# include "Std_Types.h"

/* Os module declarations */

/* Os kernel module dependencies */

/* Os hal dependencies */

/**********************************************************************************************************************
 *  GLOBAL CONSTANT MACROS
 *********************************************************************************************************************/

/* OS-Application identifiers. */
#define OsApplication_NonTrusted_Core0 OsApplication_NonTrusted_Core0
#define OsApplication_Trusted_Core0 OsApplication_Trusted_Core0
#define SystemApplication_OsCore0 SystemApplication_OsCore0

/* Trusted function identifiers. */

/* Non-trusted function identifiers. */

/* Fast trusted function identifiers. */

/* Task identifiers. */
#define Default_BSW_Async_Task Default_BSW_Async_Task
#define Default_BSW_Sync_Task Default_BSW_Sync_Task
#define Default_Init_Task Default_Init_Task
#define Default_Init_Task_Trusted Default_Init_Task_Trusted
#define Default_RTE_Mode_switch_Task Default_RTE_Mode_switch_Task
#define IdleTask_OsCore0 IdleTask_OsCore0
#define StartApplication_Appl_Init_Task StartApplication_Appl_Init_Task
#define StartApplication_Appl_Task StartApplication_Appl_Task

/* Category 2 ISR identifiers. */
#define CanIsr_7 CanIsr_7
#define CounterIsr_SystemTimer CounterIsr_SystemTimer
#define CounterIsr_TpCounter_OsCore0 CounterIsr_TpCounter_OsCore0
#define Fr_IrqLine0 Fr_IrqLine0
#define Fr_IrqTimer0 Fr_IrqTimer0
#define Lin_Channel_2_EX_Extended_Error_Interrupt Lin_Channel_2_EX_Extended_Error_Interrupt
#define Lin_Channel_2_RX_Receive_Interrupt Lin_Channel_2_RX_Receive_Interrupt
#define Lin_Channel_2_TX_Transmit_Interrupt Lin_Channel_2_TX_Transmit_Interrupt

/* Alarm identifiers. */
#define Rte_Al_TE2_Default_BSW_Async_Task_0_10ms Rte_Al_TE2_Default_BSW_Async_Task_0_10ms
#define Rte_Al_TE2_Default_BSW_Async_Task_0_20ms Rte_Al_TE2_Default_BSW_Async_Task_0_20ms
#define Rte_Al_TE2_Default_BSW_Async_Task_0_5ms Rte_Al_TE2_Default_BSW_Async_Task_0_5ms
#define Rte_Al_TE_LinIf_LinIf_MainFunction Rte_Al_TE_LinIf_LinIf_MainFunction
#define Rte_Al_TE_StartApplication_StartApplication_Cyclic1000ms Rte_Al_TE_StartApplication_StartApplication_Cyclic1000ms
#define Rte_Al_TE_StartApplication_StartApplication_Cyclic10ms Rte_Al_TE_StartApplication_StartApplication_Cyclic10ms
#define Rte_Al_TE_StartApplication_StartApplication_Cyclic1ms Rte_Al_TE_StartApplication_StartApplication_Cyclic1ms
#define Rte_Al_TE_StartApplication_StartApplication_Cyclic250ms Rte_Al_TE_StartApplication_StartApplication_Cyclic250ms

/* Counter identifiers. */
#define SystemTimer SystemTimer

/* ScheduleTable identifiers. */

/* Resource identifiers. */
#define OsResource OsResource

/* Spinlock identifiers. */

/* Peripheral identifiers. */
#define CAN_PROTECTED_AREA_CHANNEL CAN_PROTECTED_AREA_CHANNEL
#define CAN_PROTECTED_AREA_GLOBAL CAN_PROTECTED_AREA_GLOBAL
#define FR_CLC_PROTECTED_AREA_ENDINIT FR_CLC_PROTECTED_AREA_ENDINIT
#define FR_IR_PROTECTED_AREA_ENDINIT FR_IR_PROTECTED_AREA_ENDINIT
#define LIN_PROTECTED_AREA_ENDINIT LIN_PROTECTED_AREA_ENDINIT
#define MCU_CPU_PROTECTED_AREA_ENDINIT MCU_CPU_PROTECTED_AREA_ENDINIT
#define MCU_SAFETY_PROTECTED_AREA_ENDINIT MCU_SAFETY_PROTECTED_AREA_ENDINIT
#define SCU_WDTCPU0CON0_ENDINIT SCU_WDTCPU0CON0_ENDINIT

/* Barrier identifiers. */

/* Trace thread identifiers (Tasks and ISRs inclusive system objects). */

/* Trace spinlock identifiers (All spinlocks inclusive system objects). */

/**********************************************************************************************************************
 *  GLOBAL FUNCTION MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/
typedef uint32  Os_AppAccessMaskType;

/*! OS-Application identifiers. */
typedef enum
{
  OsApplication_NonTrusted_Core0 = 0, /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  OsApplication_Trusted_Core0 = 1, /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  SystemApplication_OsCore0 = 2, /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  OS_APPID_COUNT = 3,
  INVALID_OSAPPLICATION = OS_APPID_COUNT
} ApplicationType;

/*! Trusted function identifiers. */
typedef enum
{
  OS_TRUSTEDFUNCTIONID_COUNT = 0
} TrustedFunctionIndexType;

/*! Non-trusted function identifiers. */
typedef enum
{
  OS_NONTRUSTEDFUNCTIONID_COUNT = 0
} Os_NonTrustedFunctionIndexType;

/*! Fast trusted function identifiers. */
typedef enum
{
  OS_FASTTRUSTEDFUNCTIONID_COUNT = 0
} Os_FastTrustedFunctionIndexType;

/*! Task identifiers. */
typedef enum
{
  Default_BSW_Async_Task = 0,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Default_BSW_Sync_Task = 1,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Default_Init_Task = 2,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Default_Init_Task_Trusted = 3,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Default_RTE_Mode_switch_Task = 4,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  IdleTask_OsCore0 = 5,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  StartApplication_Appl_Init_Task = 6,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  StartApplication_Appl_Task = 7,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  OS_TASKID_COUNT = 8,
  INVALID_TASK = OS_TASKID_COUNT
} TaskType;

/*! Category 2 ISR identifiers. */
typedef enum
{
  CanIsr_7 = 0,   /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  CounterIsr_SystemTimer = 1,   /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  CounterIsr_TpCounter_OsCore0 = 2,   /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Fr_IrqLine0 = 3,   /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Fr_IrqTimer0 = 4,   /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Lin_Channel_2_EX_Extended_Error_Interrupt = 5,   /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Lin_Channel_2_RX_Receive_Interrupt = 6,   /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Lin_Channel_2_TX_Transmit_Interrupt = 7,   /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  OS_ISRID_COUNT = 8,
  INVALID_ISR = OS_ISRID_COUNT
} ISRType;

/*! Alarm identifiers. */
typedef enum
{
  Rte_Al_TE2_Default_BSW_Async_Task_0_10ms = 0,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Rte_Al_TE2_Default_BSW_Async_Task_0_20ms = 1,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Rte_Al_TE2_Default_BSW_Async_Task_0_5ms = 2,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Rte_Al_TE_LinIf_LinIf_MainFunction = 3,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Rte_Al_TE_StartApplication_StartApplication_Cyclic1000ms = 4,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Rte_Al_TE_StartApplication_StartApplication_Cyclic10ms = 5,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Rte_Al_TE_StartApplication_StartApplication_Cyclic1ms = 6,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  Rte_Al_TE_StartApplication_StartApplication_Cyclic250ms = 7,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  OS_ALARMID_COUNT = 8
} AlarmType;

/*! Counter identifiers. */
typedef enum
{
  SystemTimer = 0,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  OS_COUNTERID_COUNT = 1
} CounterType;

/*! ScheduleTable identifiers. */
typedef enum
{
  OS_SCHTID_COUNT = 0
} ScheduleTableType;

/*! Resource identifiers. */
typedef enum
{
  OsResource = 0,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  OS_RESOURCEID_COUNT = 1
} ResourceType;

/*! Spinlock identifiers. */
typedef enum
{
  OS_SPINLOCKID_COUNT = 0,
  INVALID_SPINLOCK = OS_SPINLOCKID_COUNT
} SpinlockIdType;

/*! Peripheral identifiers. */
typedef enum
{
  CAN_PROTECTED_AREA_CHANNEL = 0,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  CAN_PROTECTED_AREA_GLOBAL = 1,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  FR_CLC_PROTECTED_AREA_ENDINIT = 2,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  FR_IR_PROTECTED_AREA_ENDINIT = 3,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  LIN_PROTECTED_AREA_ENDINIT = 4,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  MCU_CPU_PROTECTED_AREA_ENDINIT = 5,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  MCU_SAFETY_PROTECTED_AREA_ENDINIT = 6,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  SCU_WDTCPU0CON0_ENDINIT = 7,  /* PRQA S 0784 */ /* MD_Os_Rule5.5_0784 */
  OS_PERIPHERALID_COUNT = 8
} Os_PeripheralIdType;

/*! Barrier identifiers. */
typedef enum
{
  OS_BARRIERID_COUNT = 0
} Os_BarrierIdType;

/*! Trace thread identifiers (Tasks and ISRs inclusive system objects). */
typedef enum
{
  OS_TRACE_THREADID_COUNT = 0,
  OS_TRACE_INVALID_THREAD = OS_TRACE_THREADID_COUNT + 1
} Os_TraceThreadIdType;

/*! Trace spinlock identifiers (All spinlocks inclusive system objects). */
typedef enum
{
  OS_TRACE_NUMBER_OF_CONFIGURED_SPINLOCKS = OS_SPINLOCKID_COUNT,
  OS_TRACE_NUMBER_OF_ALL_SPINLOCKS = OS_SPINLOCKID_COUNT + 0u,  /* PRQA S 4521 */ /* MD_Os_Rule10.1_4521 */
  OS_TRACE_INVALID_SPINLOCK = OS_TRACE_NUMBER_OF_ALL_SPINLOCKS + 1
} Os_TraceSpinlockIdType;

/**********************************************************************************************************************
 *  GLOBAL DATA PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL CONSTANT DATA PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/


#endif /* OS_TYPES_LCFG_H */

/**********************************************************************************************************************
 *  END OF FILE: Os_Types_Lcfg.h
 *********************************************************************************************************************/
