/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte.oil
 *           Config:  Demo.dpa
 *      ECU-Project:  Demo
 *
 *        Generator:  MICROSAR RTE Generator Version 4.22.1
 *                    RTE Core Version 1.22.1
 *          License:  CBD2000456
 *
 *      Description:  OIL-File containing project specific OS definitions for the MICROSAR RTE
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *                                              P L E A S E   N O T E
 *
 * The attributes in this file must not be changed. Missing mandatory attributes must be set in the including file.
 * They are presented as a comment in the corresponding object definition containing a list of all legal values.
 *********************************************************************************************************************/

   TASK Default_BSW_Async_Task {
      ACTIVATION = 1:"@RO@";
      PRIORITY = 30:"@RO@";
      SCHEDULE = NON:"@RO@";
// TIMING_PROTECTION = FALSE|TRUE; (AUTOSAR OS only)
      AUTOSTART = FALSE:"@RO@";
      EVENT = Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms:"@RO@";
      EVENT = Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_20ms:"@RO@";
      EVENT = Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_5ms:"@RO@";
      EVENT = Rte_Ev_Run_LinIf_LinIf_MainFunction:"@RO@";
   }:"@RO@@NR@";

   ALARM Rte_Al_TE2_Default_BSW_Async_Task_0_10ms {
      COUNTER = SystemTimer;
      ACTION = SETEVENT
      {
         TASK = Default_BSW_Async_Task;
         EVENT = Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms;
      }:"@ROC@";
      AUTOSTART = FALSE:"@RO@";
   }:"@RO@@NR@";

   ALARM Rte_Al_TE2_Default_BSW_Async_Task_0_20ms {
      COUNTER = SystemTimer;
      ACTION = SETEVENT
      {
         TASK = Default_BSW_Async_Task;
         EVENT = Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_20ms;
      }:"@ROC@";
      AUTOSTART = FALSE:"@RO@";
   }:"@RO@@NR@";

   ALARM Rte_Al_TE2_Default_BSW_Async_Task_0_5ms {
      COUNTER = SystemTimer;
      ACTION = SETEVENT
      {
         TASK = Default_BSW_Async_Task;
         EVENT = Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_5ms;
      }:"@ROC@";
      AUTOSTART = FALSE:"@RO@";
   }:"@RO@@NR@";

   ALARM Rte_Al_TE_LinIf_LinIf_MainFunction {
      COUNTER = SystemTimer;
      ACTION = SETEVENT
      {
         TASK = Default_BSW_Async_Task;
         EVENT = Rte_Ev_Run_LinIf_LinIf_MainFunction;
      }:"@ROC@";
      AUTOSTART = FALSE:"@RO@";
   }:"@RO@@NR@";

   TASK StartApplication_Appl_Init_Task {
      ACTIVATION = 1:"@RO@";
      PRIORITY = 45:"@RO@";
      SCHEDULE = NON:"@RO@";
// TIMING_PROTECTION = FALSE|TRUE; (AUTOSAR OS only)
      AUTOSTART = FALSE:"@RO@";
   }:"@RO@@NR@";

   TASK StartApplication_Appl_Task {
      ACTIVATION = 1:"@RO@";
      PRIORITY = 5:"@RO@";
      SCHEDULE = NON:"@RO@";
// TIMING_PROTECTION = FALSE|TRUE; (AUTOSAR OS only)
      AUTOSTART = FALSE:"@RO@";
      EVENT = Rte_Ev_Run_StartApplication_StartApplication_Cyclic1000ms:"@RO@";
      EVENT = Rte_Ev_Run_StartApplication_StartApplication_Cyclic10ms:"@RO@";
      EVENT = Rte_Ev_Run_StartApplication_StartApplication_Cyclic1ms:"@RO@";
      EVENT = Rte_Ev_Run_StartApplication_StartApplication_Cyclic250ms:"@RO@";
      EVENT = Rte_Ev_Run_StartApplication_StartApplication_OnDataRec_RxCtrl:"@RO@";
      EVENT = Rte_Ev_Run_StartApplication_StartApplication_OnDataRec_RxData:"@RO@";
   }:"@RO@@NR@";

   ALARM Rte_Al_TE_StartApplication_StartApplication_Cyclic1000ms {
      COUNTER = SystemTimer;
      ACTION = SETEVENT
      {
         TASK = StartApplication_Appl_Task;
         EVENT = Rte_Ev_Run_StartApplication_StartApplication_Cyclic1000ms;
      }:"@ROC@";
      AUTOSTART = FALSE:"@RO@";
   }:"@RO@@NR@";

   ALARM Rte_Al_TE_StartApplication_StartApplication_Cyclic10ms {
      COUNTER = SystemTimer;
      ACTION = SETEVENT
      {
         TASK = StartApplication_Appl_Task;
         EVENT = Rte_Ev_Run_StartApplication_StartApplication_Cyclic10ms;
      }:"@ROC@";
      AUTOSTART = FALSE:"@RO@";
   }:"@RO@@NR@";

   ALARM Rte_Al_TE_StartApplication_StartApplication_Cyclic1ms {
      COUNTER = SystemTimer;
      ACTION = SETEVENT
      {
         TASK = StartApplication_Appl_Task;
         EVENT = Rte_Ev_Run_StartApplication_StartApplication_Cyclic1ms;
      }:"@ROC@";
      AUTOSTART = FALSE:"@RO@";
   }:"@RO@@NR@";

   ALARM Rte_Al_TE_StartApplication_StartApplication_Cyclic250ms {
      COUNTER = SystemTimer;
      ACTION = SETEVENT
      {
         TASK = StartApplication_Appl_Task;
         EVENT = Rte_Ev_Run_StartApplication_StartApplication_Cyclic250ms;
      }:"@ROC@";
      AUTOSTART = FALSE:"@RO@";
   }:"@RO@@NR@";

   EVENT Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms {
      MASK = AUTO;
   }:"@ROC@@NR@";

   EVENT Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_20ms {
      MASK = AUTO;
   }:"@ROC@@NR@";

   EVENT Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_5ms {
      MASK = AUTO;
   }:"@ROC@@NR@";

   EVENT Rte_Ev_Run_LinIf_LinIf_MainFunction {
      MASK = AUTO;
   }:"@ROC@@NR@";

   EVENT Rte_Ev_Run_StartApplication_StartApplication_Cyclic1000ms {
      MASK = AUTO;
   }:"@ROC@@NR@";

   EVENT Rte_Ev_Run_StartApplication_StartApplication_Cyclic10ms {
      MASK = AUTO;
   }:"@ROC@@NR@";

   EVENT Rte_Ev_Run_StartApplication_StartApplication_Cyclic1ms {
      MASK = AUTO;
   }:"@ROC@@NR@";

   EVENT Rte_Ev_Run_StartApplication_StartApplication_Cyclic250ms {
      MASK = AUTO;
   }:"@ROC@@NR@";

   EVENT Rte_Ev_Run_StartApplication_StartApplication_OnDataRec_RxCtrl {
      MASK = AUTO;
   }:"@ROC@@NR@";

   EVENT Rte_Ev_Run_StartApplication_StartApplication_OnDataRec_RxData {
      MASK = AUTO;
   }:"@ROC@@NR@";


   APPLICATION OsApplication_NonTrusted_Core0 {
      TRUSTED = TRUE;
// Add the following four attributes according to your needs.
// Note: They do not have a default value and are therefore necessary in the configuration.
//      STARTUPHOOK = FALSE;
//      SHUTDOWNHOOK = FALSE;
//      ERRORHOOK = FALSE;
//      HAS_RESTARTTASK = FALSE;
      TASK = Default_BSW_Async_Task;
      ALARM = Rte_Al_TE2_Default_BSW_Async_Task_0_10ms;
      ALARM = Rte_Al_TE2_Default_BSW_Async_Task_0_20ms;
      ALARM = Rte_Al_TE2_Default_BSW_Async_Task_0_5ms;
      ALARM = Rte_Al_TE_LinIf_LinIf_MainFunction;
      TASK = StartApplication_Appl_Init_Task;
      TASK = StartApplication_Appl_Task;
      ALARM = Rte_Al_TE_StartApplication_StartApplication_Cyclic1000ms;
      ALARM = Rte_Al_TE_StartApplication_StartApplication_Cyclic10ms;
      ALARM = Rte_Al_TE_StartApplication_StartApplication_Cyclic1ms;
      ALARM = Rte_Al_TE_StartApplication_StartApplication_Cyclic250ms;
  };
