<?xml version="1.0" encoding="UTF-8" standalone="no"?><xmi:XMI xmlns:xmi="http://schema.omg.org/spec/XMI/2.1" xmlns:uml="http://schema.omg.org/spec/UML/2.1" xmi:version="2.1"><xmi:Documentation exporter="Enterprise Architect" exporterVersion="6.5"/><xmi:Extension extender="Enterprise Architect" extenderID="6.5"><elements><element name="ApplicationId2DestApplicationManagerRom:ConstStruct" scope="public" xmi:idref="EAID_ded26787e1860b9df6fe48031a13de3c" xmi:type="uml:Object"><properties documentation="Application Id to Destination Application Manager Mapping table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_ce239155f6536c1855b1cb25658c090a"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="InvalidHndOfApplicationId2DestApplicationManagerRom" scope="Private" xmi:idref="EAID_319c7171823fc4f9b0af85fe92dedd10"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_InvalidHndOfApplicationId2DestApplicationManagerRom if all values are false" type="Pre-condition"/></Constraints><documentation value="FALSE, if the handle of PduR_ApplicationId2DestApplicationManagerRom is valid and can be used in the embedded code for further processing in the embedded code."/><properties changeability="frozen" collection="true" type="PduR_InvalidHndOfApplicationId2DestApplicationManagerRomType"/></attribute></attributes></element><element name="BmTxBufferIndRom:ConstStruct" scope="public" xmi:idref="EAID_ee5e575cfac73ac43d3903e69fae8a14" xmi:type="uml:Object"><properties documentation="PduR BufferManager TxBuffer Indirection Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_dd7b929d2a35d289294274e69436982d"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="BmTxBufferInstanceRom:ConstStruct" scope="public" xmi:idref="EAID_9c252bf0f9e011d96f8bdba9fe4b5cea" xmi:type="uml:Object"><properties documentation="PduR BufferManager TxBufferInstance Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_e099018448a02e5695e04af0f82349c4"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="BmTxBufferRom:ConstStruct" scope="public" xmi:idref="EAID_bdac7bdeb331402ce6cbd145cfdd1475" xmi:type="uml:Object"><properties documentation="PduR BufferManager TxBuffer Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_0c9bc9d7df75e5fa061d222f2aea745f"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="DeferredEventCacheRom:ConstStruct" scope="public" xmi:idref="EAID_aa860125f8f09286c32f05e4ab4216de" xmi:type="uml:Object"><properties documentation="PduR Deferred Event Cache Administration Variables" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/><constraint description="No Deferred Event Cache is configured" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_9497e5cbaafb07eee8f6c3fde4f51759"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="DestApplicationManagerRom:ConstStruct" scope="public" xmi:idref="EAID_246116c073f4615685b6f46a07d525a0" xmi:type="uml:Object"><properties documentation="Destination Application Manager" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_234755db908f76bcf6066f7506945913"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ExclusiveAreaRom:ConstStruct" scope="public" xmi:idref="EAID_1f152a97dc071ceae1b8714a07a7e8b2" xmi:type="uml:Object"><properties documentation="PduR Exclusive Area Locks" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_18aecbff801a7871dd49e72e664b6112"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="LockOfExclusiveAreaRom" scope="Private" xmi:idref="EAID_034181ef8f0d05ee47aee1d9302c8001"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_LockOfExclusiveAreaRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Lock function"/><properties changeability="frozen" collection="true" type="PduR_LockFunctionType"/></attribute><attribute name="UnlockOfExclusiveAreaRom" scope="Private" xmi:idref="EAID_7c5b4110ca58931d77004a7543769b53"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_UnlockOfExclusiveAreaRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Unlock function"/><properties changeability="frozen" collection="true" type="PduR_LockFunctionType"/></attribute></attributes></element><element name="FmFifoInstanceRom:ConstStruct" scope="public" xmi:idref="EAID_da88b58e5fb558917c7fd7ec0171ae7e" xmi:type="uml:Object"><properties documentation="Instance of the PduRDestPdus using a single Fifo" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_8245226f0bb12c6346a897ecce78691d"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="FmFifoRom:ConstStruct" scope="public" xmi:idref="EAID_9c46d1fe89ae9eec3af3b950598487b5" xmi:type="uml:Object"><properties documentation="PduR FiFoManager Fifo Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_a3b413520f856fded033028d13e7a168"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="GeneralPropertiesRom:ConstStruct" scope="public" xmi:idref="EAID_4283cbcb2908aea6b13a851f4aa6800d" xmi:type="uml:Object"><properties documentation="General Properties Switches of the PduR." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_4f87798daa598aba9ab124b9700903c7"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="SpinlockRetryCounterOfGeneralPropertiesRom" scope="Private" xmi:idref="EAID_2019e155cfbef14c2efa192b4560f360"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No Spinlocks used." type="Pre-condition"/></Constraints><documentation value="Retry counter value for the Spinlocks used by the PduR."/><properties changeability="frozen" collection="true" type="PduR_SpinlockRetryCounterOfGeneralPropertiesRomType"/></attribute><attribute name="hasIfRoutingOfGeneralPropertiesRom" scope="Private" xmi:idref="EAID_51ebeb599304e3395b4eaa6d02ba1851"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No Communication Interface BswModule active." type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_hasIfRoutingOfGeneralPropertiesRom if all values are false" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="PduR_hasIfRoutingOfGeneralPropertiesRomType"/></attribute><attribute name="hasIfTxFifoOfGeneralPropertiesRom" scope="Private" xmi:idref="EAID_6e554529ba86d08d4a2f13698c7a721a"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No Communication Interface BswModule active." type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_hasIfTxFifoOfGeneralPropertiesRom if all values are false" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="PduR_hasIfTxFifoOfGeneralPropertiesRomType"/></attribute><attribute name="hasTpTxBufferedForwardingOfGeneralPropertiesRom" scope="Private" xmi:idref="EAID_561a2a5d6b1a842d6cc609bfbccc99d1"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No Transport Protocol BswModule active." type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_hasTpTxBufferedForwardingOfGeneralPropertiesRom if all values are false" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="PduR_hasTpTxBufferedForwardingOfGeneralPropertiesRomType"/></attribute></attributes></element><element name="InterfaceFifoQueueRom:ConstStruct" scope="public" xmi:idref="EAID_87c915abc9c5e5ab37d73383fe969ff5" xmi:type="uml:Object"><properties documentation="PduR Interface Fifo Queue Administration Variables" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_0df2b249465a376e24fe7b2fd274c67a"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="MaxPduLengthOfInterfaceFifoQueueRom" scope="Private" xmi:idref="EAID_b029335bd3cc6168ac3ecf54412cceba"><Constraints/><documentation value="Maximum allowed PduLength for buffered routings."/><properties changeability="frozen" collection="true" type="PduR_MaxPduLengthOfInterfaceFifoQueueRomType"/></attribute></attributes></element><element name="LockRom:ConstStruct" scope="public" xmi:idref="EAID_46776a359f0d8191bf9118726beaaa45" xmi:type="uml:Object"><properties documentation="PduR Lock Manager Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_343ef699a5ce43f5ba1d806e7fdf8bcf"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="McQBufferRom:ConstStruct" scope="public" xmi:idref="EAID_b6ed023068eb5bfcc230f923085ee524" xmi:type="uml:Object"><properties documentation="PduR Multicore Queue Administration Variables" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="/ActiveEcuC/PduR/PduRGeneral[0:PduRSupportMulticore] is configured to 'false'" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/><constraint description="No MulticoreQueueSize is configured" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_3802788f2710f41e8bc0dcec0632dd1c"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="MmRom:ConstStruct" scope="public" xmi:idref="EAID_cf385f1f71ca109b9ac36c596c7c61ea" xmi:type="uml:Object"><properties documentation="Module manager: Contains all function pointers of the bordering modules." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_8bcf6c6da18d59a20d99f8fbde1ab7fc"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="CancelReceiveSupportedOfMmRom" scope="Private" xmi:idref="EAID_03a5cfa25baff3ca07028e2c0b995254"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_CancelReceiveSupportedOfMmRom if all values are false" type="Pre-condition"/></Constraints><documentation value="Does the module support the CancelReceive API."/><properties changeability="frozen" collection="true" type="PduR_CancelReceiveSupportedOfMmRomType"/></attribute><attribute name="ChangeParameterSupportedOfMmRom" scope="Private" xmi:idref="EAID_71d62c1c7b4292e920cf94ce39972025"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_ChangeParameterSupportedOfMmRom if all values are false" type="Pre-condition"/></Constraints><documentation value="Does the module support the ChangeParameter API."/><properties changeability="frozen" collection="true" type="PduR_ChangeParameterSupportedOfMmRomType"/></attribute><attribute name="IfCancelTransmitSupportedOfMmRom" scope="Private" xmi:idref="EAID_857db3445a84b224a09b7c3e7e673832"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_IfCancelTransmitSupportedOfMmRom if all values are false" type="Pre-condition"/></Constraints><documentation value="Does the module support the Communication Interface CancelTransmit API."/><properties changeability="frozen" collection="true" type="PduR_IfCancelTransmitSupportedOfMmRomType"/></attribute><attribute name="LoIfCancelTransmitFctPtrOfMmRom" scope="Private" xmi:idref="EAID_446047980161921b0e737b0cb3c2c1b1"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_LoIfCancelTransmitFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Lower layer cancel transmit function pointers."/><properties changeability="frozen" collection="true" type="PduR_CancelTransmitFctPtrType"/></attribute><attribute name="LoIfOfMmRom" scope="Private" xmi:idref="EAID_f6ce7d526481f3916e5ed836ac7dd8cc"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_LoIfOfMmRom if all values are false" type="Pre-condition"/></Constraints><documentation value="Is the module a lower communication interface module."/><properties changeability="frozen" collection="true" type="PduR_LoIfOfMmRomType"/></attribute><attribute name="LoIfTransmitFctPtrOfMmRom" scope="Private" xmi:idref="EAID_7c7e3958630dec7f9932f3e090169d2a"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_LoIfTransmitFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Lower layer If transmit function pointers"/><properties changeability="frozen" collection="true" type="PduR_TransmitFctPtrType"/></attribute><attribute name="LoTpCancelReceiveFctPtrOfMmRom" scope="Private" xmi:idref="EAID_49f56241b685ab7bb5e581e0c7f023ad"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_LoTpCancelReceiveFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Lower layer cancel receive function pointers."/><properties changeability="frozen" collection="true" type="PduR_CancelReceiveFctPtrType"/></attribute><attribute name="LoTpCancelTransmitFctPtrOfMmRom" scope="Private" xmi:idref="EAID_ad2cf6a25350b86635e55c3da5935897"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_LoTpCancelTransmitFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Lower layer cancel transmit function pointers."/><properties changeability="frozen" collection="true" type="PduR_CancelTransmitFctPtrType"/></attribute><attribute name="LoTpChangeParameterFctPtrOfMmRom" scope="Private" xmi:idref="EAID_4159f17d405a88bc52107b1528e06ada"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_LoTpChangeParameterFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="lower layer change parameter function pointers to change e.g. BS or STmin.."/><properties changeability="frozen" collection="true" type="PduR_ChangeParameterFctPtrType"/></attribute><attribute name="LoTpOfMmRom" scope="Private" xmi:idref="EAID_e86f06f933d7785209ab9c616503f1d7"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_LoTpOfMmRom if all values are false" type="Pre-condition"/></Constraints><documentation value="Is the module a lower transport protocol module."/><properties changeability="frozen" collection="true" type="PduR_LoTpOfMmRomType"/></attribute><attribute name="LoTpTransmitFctPtrOfMmRom" scope="Private" xmi:idref="EAID_5c59d0d08caaac500499d3379c7935c1"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_LoTpTransmitFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Lower layer Tp transmit function pointers"/><properties changeability="frozen" collection="true" type="PduR_TransmitFctPtrType"/></attribute><attribute name="TpCancelTransmitSupportedOfMmRom" scope="Private" xmi:idref="EAID_0fb08164f84f3d595caaa83f078a4bbf"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_TpCancelTransmitSupportedOfMmRom if all values are false" type="Pre-condition"/></Constraints><documentation value="Does the module support the transport protocol CancelTransmit API."/><properties changeability="frozen" collection="true" type="PduR_TpCancelTransmitSupportedOfMmRomType"/></attribute><attribute name="UpIfOfMmRom" scope="Private" xmi:idref="EAID_1acbcee2944e4a373b04d7247aab66e6"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_UpIfOfMmRom if all values are false" type="Pre-condition"/></Constraints><documentation value="Is the module a upper communication interface module."/><properties changeability="frozen" collection="true" type="PduR_UpIfOfMmRomType"/></attribute><attribute name="UpIfRxIndicationFctPtrOfMmRom" scope="Private" xmi:idref="EAID_6e8f9ad281f41a60436faeb9c4c3bb16"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_UpIfRxIndicationFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Upper layer communication interface Rx indication function pointers."/><properties changeability="frozen" collection="true" type="PduR_IfRxIndicationType"/></attribute><attribute name="UpIfTriggerTransmitFctPtrOfMmRom" scope="Private" xmi:idref="EAID_f0b485db3fbac953942ddafe1ab1a179"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_UpIfTriggerTransmitFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Upper layer trigger transmit function pointers"/><properties changeability="frozen" collection="true" type="PduR_TriggerTransmitFctPtrType"/></attribute><attribute name="UpIfTxConfirmationFctPtrOfMmRom" scope="Private" xmi:idref="EAID_562c6c5f4e8754c48158e050f06b5a0d"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_UpIfTxConfirmationFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Upper layer communication interface Tx confimation function pointers"/><properties changeability="frozen" collection="true" type="PduR_IfTxConfirmationFctPtrType"/></attribute><attribute name="UpTpCopyRxDataFctPtrOfMmRom" scope="Private" xmi:idref="EAID_8caf841b6da08c3e98ded57d20aaf131"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_UpTpCopyRxDataFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Transport protocol CopyRxData function pointers"/><properties changeability="frozen" collection="true" type="PduR_CopyRxDataFctPtrType"/></attribute><attribute name="UpTpCopyTxDataFctPtrOfMmRom" scope="Private" xmi:idref="EAID_1ce3cb0154ae7e7b0835401303a8e173"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_UpTpCopyTxDataFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Transport protocol CopyTxData function pointers"/><properties changeability="frozen" collection="true" type="PduR_CopyTxDataFctPtrType"/></attribute><attribute name="UpTpOfMmRom" scope="Private" xmi:idref="EAID_31988fb928c8860dc98995c23b5494b9"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_UpTpOfMmRom if all values are false" type="Pre-condition"/></Constraints><documentation value="Is the module a upper transport protocol module."/><properties changeability="frozen" collection="true" type="PduR_UpTpOfMmRomType"/></attribute><attribute name="UpTpStartOfReceptionFctPtrOfMmRom" scope="Private" xmi:idref="EAID_f3da099036e37bf53b17898b87864ffc"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_UpTpStartOfReceptionFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Transport protocol StartOfReception function pointers"/><properties changeability="frozen" collection="true" type="PduR_StartOfReceptionFctPtrType"/></attribute><attribute name="UpTpTpRxIndicationFctPtrOfMmRom" scope="Private" xmi:idref="EAID_3084575a88597fae76eb6b5d6b913b57"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_UpTpTpRxIndicationFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Transport protocol TpRxIndication function pointers"/><properties changeability="frozen" collection="true" type="PduR_TpRxIndicationFctPtrType"/></attribute><attribute name="UpTpTpTxConfirmationFctPtrOfMmRom" scope="Private" xmi:idref="EAID_a64837f1c9e5001d80753849385546a9"><Constraints><Constraint name="CNullPtrGenCondition" notes="deactivate the PduR_UpTpTpTxConfirmationFctPtrOfMmRom if all values are NULL_PTR" type="Pre-condition"/></Constraints><documentation value="Transport protocol TpTxConfimation function pointers"/><properties changeability="frozen" collection="true" type="PduR_TpTxConfirmationFctPtrType"/></attribute></attributes></element><element name="QueueFctPtrRom:ConstStruct" scope="public" xmi:idref="EAID_c565d59b2faa599cd32de993a7ad0395" xmi:type="uml:Object"><properties documentation="Queue function pointer: Contains all function pointers to access different kinds of If queues." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_125ea91d04425de06b2045d4ba3fb830"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="FlushFctPtrOfQueueFctPtrRom" scope="Private" xmi:idref="EAID_148738ad86e409004c0757bdb845c45b"><Constraints/><documentation value="Queue flush function pointer"/><properties changeability="frozen" collection="true" type="PduR_QAL_FlushFctPtrType"/></attribute><attribute name="GetFctPtrOfQueueFctPtrRom" scope="Private" xmi:idref="EAID_799e6cd2a15fae01cf41aaec833d25a6"><Constraints/><documentation value="Queue get function pointer"/><properties changeability="frozen" collection="true" type="PduR_QAL_GetFctPtrType"/></attribute><attribute name="GetFillLevelFctPtrOfQueueFctPtrRom" scope="Private" xmi:idref="EAID_124ab439bce3c2853825b5eb362a8a21"><Constraints/><documentation value="Queue get fill level function pointer"/><properties changeability="frozen" collection="true" type="PduR_QAL_GetFillLevelFctPtrType"/></attribute><attribute name="PutFctPtrOfQueueFctPtrRom" scope="Private" xmi:idref="EAID_c879460156f298b234f0f2e307d677fe"><Constraints/><documentation value="Queue put function pointer"/><properties changeability="frozen" collection="true" type="PduR_QAL_PutFctPtrType"/></attribute><attribute name="RemoveFctPtrOfQueueFctPtrRom" scope="Private" xmi:idref="EAID_0b7126f740f109e52864962f95b02dbd"><Constraints/><documentation value="Queue remove function pointer"/><properties changeability="frozen" collection="true" type="PduR_QAL_RemoveFctPtrType"/></attribute></attributes></element><element name="RmBufferedIfPropertiesRom:ConstStruct" scope="public" xmi:idref="EAID_45ca483d77f9f774d388da7e12e67d82" xmi:type="uml:Object"><properties documentation="Properties of buffered communication interface routings." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/><constraint description="No CommunicationInterface BswModule active." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_9a644deffd16d22c764b0829d8056c8b"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ImplementationTypeOfRmBufferedIfPropertiesRom" scope="Private" xmi:idref="EAID_22943d1a7d758cba11ea23ca0a4bd2cb"><Constraints/><documentation value="Implementation type of the queue."/><properties changeability="frozen" collection="true" type="PduR_ImplementationTypeOfRmBufferedIfPropertiesRomType"/></attribute><attribute name="QueueDataProvisionTypeOfRmBufferedIfPropertiesRom" scope="Private" xmi:idref="EAID_97a209c5f2d28a40e732b8bf8813b219"><Constraints/><documentation value="Data provision type of the queue."/><properties changeability="frozen" collection="true" type="PduR_QueueDataProvisionTypeOfRmBufferedIfPropertiesRomType"/></attribute><attribute name="QueueTypeOfRmBufferedIfPropertiesRom" scope="Private" xmi:idref="EAID_db2ede062aff30c57562cbf04da0cd4f"><Constraints/><documentation value="Type of the queue."/><properties changeability="frozen" collection="true" type="PduR_QueueTypeOfRmBufferedIfPropertiesRomType"/></attribute></attributes></element><element name="RmBufferedTpPropertiesRom:ConstStruct" scope="public" xmi:idref="EAID_d09498d242e10c71f0708b3ae5d8c375" xmi:type="uml:Object"><properties documentation="PduR RoutiongManager Properties of buffered Tp routing paths." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/><constraint description="No TransportProtocol BswModule active." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/><constraint description="No TransportProtocol Gateway Routing available" name="CUserPreCompileDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_72afc0dcd59a05f0f680eee63be26e85"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="MetaDataLengthOfRmBufferedTpPropertiesRom" scope="Private" xmi:idref="EAID_6742db3316a103e42bf5df00aab5ecf8"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Meta Data Support is not active" type="Pre-condition"/><Constraint name="CNumericalArrayGenCondition" notes="deactivate the PduR_MetaDataLengthOfRmBufferedTpPropertiesRom if all values are 0" type="Pre-condition"/></Constraints><documentation value="The value represents the MetaDataLength of the related GlobalPdu or 0 if the MetaDataLength is not used for this PDU."/><properties changeability="frozen" collection="true" type="PduR_MetaDataLengthOfRmBufferedTpPropertiesRomType"/></attribute><attribute name="MetaDataLengthUsedOfRmBufferedTpPropertiesRom" scope="Private" xmi:idref="EAID_7d86724a7dab77e20ce1710bbbb01397"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_MetaDataLengthUsedOfRmBufferedTpPropertiesRom if all values are false" type="Pre-condition"/></Constraints><documentation value="TRUE if the srcPdu uses MetaData else FALSE."/><properties changeability="frozen" collection="true" type="PduR_MetaDataLengthUsedOfRmBufferedTpPropertiesRomType"/></attribute><attribute name="QueuedDestCntOfRmBufferedTpPropertiesRom" scope="Private" xmi:idref="EAID_8c7aa39fc291c36fc1b2533ffe3e407f"><Constraints/><documentation value="Number of queued TP destinations."/><properties changeability="frozen" collection="true" type="PduR_QueuedDestCntOfRmBufferedTpPropertiesRomType"/></attribute><attribute name="TpThresholdOfRmBufferedTpPropertiesRom" scope="Private" xmi:idref="EAID_f8275cf0096788b44b1cbe90f24d4019"><Constraints/><documentation value="TP threshold value."/><properties changeability="frozen" collection="true" type="PduR_TpThresholdOfRmBufferedTpPropertiesRomType"/></attribute></attributes></element><element name="RmDestRom:ConstStruct" scope="public" xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5" xmi:type="uml:Object"><properties documentation="PduR RoutiongPathManager destPdu Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_820d372bb9973a0e6ac9531deb564ca8"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="BswMPduRPreTransmitCallbackOfRmDestRom" scope="Private" xmi:idref="EAID_e40fd3461fb11b494c5c9d0c5ddfb629"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Callback Support is not active" type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_BswMPduRPreTransmitCallbackOfRmDestRom if all values are false" type="Pre-condition"/></Constraints><documentation value="TRUE if the BswM Pre-Transmit callback is enabled"/><properties changeability="frozen" collection="true" type="PduR_BswMPduRPreTransmitCallbackOfRmDestRomType"/></attribute><attribute name="PartitionIndexOfCslStringSNVOfRmDestRom" scope="Private" xmi:idref="EAID_59954cef85ee5d1f628d4b1e88e926ac"><Constraints/><properties changeability="frozen" collection="true" type="PduR_PartitionIndexOfCslStringSNVOfRmDestRomType"/></attribute><attribute name="PduLengthHandlingStrategyOfRmDestRom" scope="Private" xmi:idref="EAID_38a5e3a4afa7ddde218c0c4ca4622b47"><Constraints/><documentation value="The strategy how larger than configured If Pdus are handled."/><properties changeability="frozen" collection="true" type="PduR_PduLengthHandlingStrategyOfRmDestRomType"/></attribute><attribute name="RoutingTypeOfRmDestRom" scope="Private" xmi:idref="EAID_d39f4a3af732db9680383ff13f012452"><Constraints/><documentation value="Type of the Routing (API Forwarding, Gateway)."/><properties changeability="frozen" collection="true" type="PduR_RoutingTypeOfRmDestRomType"/></attribute></attributes></element><element name="RmDestRpgRom:ConstStruct" scope="public" xmi:idref="EAID_2f3d49b44ebddb55363af41561fd35a6" xmi:type="uml:Object"><properties documentation="PduRDestPdu specific PduRRoutingPathGroup information." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_150aee1e6e9d40bb4d3ec60847dcc3ad"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="InitialEnabledCntOfRmDestRpgRom" scope="Private" xmi:idref="EAID_4d09cb67903b00806aa4aaad49389c2c"><Constraints/><documentation value="Amount of initially enabled PduRRoutingPathGroups"/><properties changeability="frozen" collection="true" type="PduR_InitialEnabledCntOfRmDestRpgRomType"/></attribute></attributes></element><element name="RmGDestRom:ConstStruct" scope="public" xmi:idref="EAID_c753b8257c1177208da906dd979acb10" xmi:type="uml:Object"><properties documentation="PduR RoutingPathManager global destPdu Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_172e1f38560b5c083a6110319c5bbcd8"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="DestHndOfRmGDestRom" scope="Private" xmi:idref="EAID_73d540d81c458fc88301a2ebd6e9182f"><Constraints/><documentation value="handle to be used as parameter for the StartOfReception, CopyRxData, Transmit or RxIndication function call."/><properties changeability="frozen" collection="true" type="PduR_DestHndOfRmGDestRomType"/></attribute><attribute name="DirectionOfRmGDestRom" scope="Private" xmi:idref="EAID_40293f36b67b32b0546d0e91040b91d6"><Constraints/><documentation value="Direction of this Pdu: Rx or Tx"/><properties changeability="frozen" collection="true" type="PduR_DirectionOfRmGDestRomType"/></attribute><attribute name="MaxPduLengthOfRmGDestRom" scope="Private" xmi:idref="EAID_6dc9804572e9aa9039c1af311d18428f"><Constraints/><documentation value="Configured PduLength + metadata length."/><properties changeability="frozen" collection="true" type="PduR_MaxPduLengthOfRmGDestRomType"/></attribute><attribute name="PartitionIndexOfCslStringSNVOfRmGDestRom" scope="Private" xmi:idref="EAID_df0a2131bd9a2af6ebaff72e176ec55f"><Constraints/><properties changeability="frozen" collection="true" type="PduR_PartitionIndexOfCslStringSNVOfRmGDestRomType"/></attribute><attribute name="PduRDestPduProcessingOfRmGDestRom" scope="Private" xmi:idref="EAID_3941b37a3c851980879ea736f4712dba"><Constraints/><documentation value="Is Processing Type of destination PDU."/><properties changeability="frozen" collection="true" type="PduR_PduRDestPduProcessingOfRmGDestRomType"/></attribute><attribute name="TxIf2UpIdxOfRmGDestRom" scope="Private" xmi:idref="EAID_e42f0895cd49623db81792d4bb36602e"><Constraints/><documentation value="TxIf2Up index."/><properties changeability="frozen" collection="true" type="PduR_TxIf2UpIdxOfRmGDestRomType"/></attribute></attributes></element><element name="RmSrcRom:ConstStruct" scope="public" xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde" xmi:type="uml:Object"><properties documentation="PduR RoutiongManager SrcPdu Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_1d95e1b080e0a1e1eaa2b79f2f08dfba"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="PartitionIndexOfCslStringSNVOfRmSrcRom" scope="Private" xmi:idref="EAID_04565de9e18766f9e15224423527f5d4"><Constraints/><properties changeability="frozen" collection="true" type="PduR_PartitionIndexOfCslStringSNVOfRmSrcRomType"/></attribute><attribute name="SrcHndOfRmSrcRom" scope="Private" xmi:idref="EAID_4afdf8f94471fff776ea2d4fdd2399d0"><Constraints/><documentation value="handle to be used as parameter for the TxConfirmation or TriggerTransmit function call."/><properties changeability="frozen" collection="true" type="PduR_SrcHndOfRmSrcRomType"/></attribute><attribute name="TriggerTransmitSupportedOfRmSrcRom" scope="Private" xmi:idref="EAID_0f992acf84ddc6bae603b6d10b8bd384"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No BswModule with TriggerTransmit active." type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_TriggerTransmitSupportedOfRmSrcRom if all values are false" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="PduR_TriggerTransmitSupportedOfRmSrcRomType"/></attribute><attribute name="TxConfirmationSupportedOfRmSrcRom" scope="Private" xmi:idref="EAID_4de2d2baf3f3d92c1d1972cc6fb2c9e0"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No BswModule with TxConfirmation active." type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_TxConfirmationSupportedOfRmSrcRom if all values are false" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="PduR_TxConfirmationSupportedOfRmSrcRomType"/></attribute></attributes></element><element name="RpgExtIdRom:ConstStruct" scope="public" xmi:idref="EAID_10d0028e2d69ffb1cc13eec66d424cb4" xmi:type="uml:Object"><properties documentation="PduRRoutingPathGroup specific ROM information." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_2d082b068899fbfb6c55958a3bcd881b"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="InvalidHndOfRpgExtIdRom" scope="Private" xmi:idref="EAID_5792a4c1928cf7dbf60822c69fb7f33f"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_InvalidHndOfRpgExtIdRom if all values are false" type="Pre-condition"/></Constraints><documentation value="FALSE, if the handle of PduR_RpgExtIdRom is valid and can be used in the embedded code for further processing in the embedded code."/><properties changeability="frozen" collection="true" type="PduR_InvalidHndOfRpgExtIdRomType"/></attribute><attribute name="PartitionIndexOfCslStringSNVOfRpgExtIdRom" scope="Private" xmi:idref="EAID_73ff01afddff7847749f3f0d9ce2e4cc"><Constraints/><properties changeability="frozen" collection="true" type="PduR_PartitionIndexOfCslStringSNVOfRpgExtIdRomType"/></attribute></attributes></element><element name="RpgRom:ConstStruct" scope="public" xmi:idref="EAID_b3f09b00ce1f1c35f117db4c29088408" xmi:type="uml:Object"><properties documentation="PduRRoutingPathGroup specific RAM information." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_c94aa52209ab47f93d6b8eb9c449c7e5"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="EnabledAtInitOfRpgRom" scope="Private" xmi:idref="EAID_0ec913d76839dc1431575fd5b6a82a7e"><Constraints/><documentation value="TRUE, if the PduRRoutingPathGroup is enabled after PduR_Init() is called."/><properties changeability="frozen" collection="true" type="PduR_EnabledAtInitOfRpgRomType"/></attribute></attributes></element><element name="RxIf2Dest:ConstStruct" scope="public" xmi:idref="EAID_138f302e49968a3d5bd92e5e64e5cd88" xmi:type="uml:Object"><properties documentation="This table contains all routing information to perform the Rx handling of an interface routing. Used in the &amp;lt;LLIf&amp;gt;_RxIndication" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No communication interface PduRBswModule configured. Evaluated DefinitionRef: /MICROSAR/PduR/PduRBswModules/PduRCommunicationInterface" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_edb774de3cb4278ce8f86e5a9ff6fba3"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="BswMPduRRxIndicationCallbackOfRxIf2Dest" scope="Private" xmi:idref="EAID_2804a93c9b8d249ac600eb236f467ed3"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Callback Support is not active" type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_BswMPduRRxIndicationCallbackOfRxIf2Dest if all values are false" type="Pre-condition"/></Constraints><documentation value="TRUE if the BswM RxIndication callback is enabled"/><properties changeability="frozen" collection="true" type="PduR_BswMPduRRxIndicationCallbackOfRxIf2DestType"/></attribute><attribute name="InvalidHndOfRxIf2Dest" scope="Private" xmi:idref="EAID_c321d46cc07342c6a74589393a738999"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_InvalidHndOfRxIf2Dest if all values are false" type="Pre-condition"/></Constraints><documentation value="FALSE, if the handle of PduR_RxIf2Dest is valid and can be used in the embedded code for further processing in the embedded code."/><properties changeability="frozen" collection="true" type="PduR_InvalidHndOfRxIf2DestType"/></attribute><attribute name="PartitionIndexOfCslStringSNVOfRxIf2Dest" scope="Private" xmi:idref="EAID_04b4ffacae82214bd21456801bdd5840"><Constraints/><properties changeability="frozen" collection="true" type="PduR_PartitionIndexOfCslStringSNVOfRxIf2DestType"/></attribute></attributes></element><element name="RxTp2Dest:ConstStruct" scope="public" xmi:idref="EAID_2d16a949452f3e8f68f8eef2a24a09f3" xmi:type="uml:Object"><properties documentation="This table contains all routing information to perform the Rx handling of a Tp Routing. Used in the PduR_&amp;lt;LLTp&amp;gt;_StartOfReception, PduR_&amp;lt;LLTp&amp;gt;_CopyRxData and PduR_&amp;lt;LLTp&amp;gt;_RxIndication." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No transport protocol PduRBswModule configured. Evaluated DefinitionRef: /MICROSAR/PduR/PduRBswModules/PduRTransportProtocol" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_544dfcc8cd9c98f67dc8f31f8524651a"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="BswMPduRTpRxIndicationCallbackOfRxTp2Dest" scope="Private" xmi:idref="EAID_a712510d0633c404a18c1bb30ff6048f"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Callback Support is not active" type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_BswMPduRTpRxIndicationCallbackOfRxTp2Dest if all values are false" type="Pre-condition"/></Constraints><documentation value="TRUE if the BswM TpRxIndication callback is enabled"/><properties changeability="frozen" collection="true" type="PduR_BswMPduRTpRxIndicationCallbackOfRxTp2DestType"/></attribute><attribute name="BswMPduRTpStartOfReceptionCallbackOfRxTp2Dest" scope="Private" xmi:idref="EAID_1977199f5511b20ca47c4e2d9fbf9d87"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Callback Support is not active" type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_BswMPduRTpStartOfReceptionCallbackOfRxTp2Dest if all values are false" type="Pre-condition"/></Constraints><documentation value="TRUE if the BswM StartOfReception callback is enabled"/><properties changeability="frozen" collection="true" type="PduR_BswMPduRTpStartOfReceptionCallbackOfRxTp2DestType"/></attribute><attribute name="InvalidHndOfRxTp2Dest" scope="Private" xmi:idref="EAID_17009559cd76161f3068a519099b37d2"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_InvalidHndOfRxTp2Dest if all values are false" type="Pre-condition"/></Constraints><documentation value="FALSE, if the handle of PduR_RxTp2Dest is valid and can be used in the embedded code for further processing in the embedded code."/><properties changeability="frozen" collection="true" type="PduR_InvalidHndOfRxTp2DestType"/></attribute><attribute name="PartitionIndexOfCslStringSNVOfRxTp2Dest" scope="Private" xmi:idref="EAID_2c7239a136be47a98b02fc684f355a47"><Constraints/><properties changeability="frozen" collection="true" type="PduR_PartitionIndexOfCslStringSNVOfRxTp2DestType"/></attribute></attributes></element><element name="RxTp2Src:ConstStruct" scope="public" xmi:idref="EAID_19888a57d3bbef12b32188523d597738" xmi:type="uml:Object"><properties documentation="This table contains all routing information to perform the ChangeParameter or CancelReceive handling. Used in ChangeParameter and CancelReceive APIs &#9;race SPEC-1024" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="(No PduRBswModule configured which uses the CancelReceive API.. Evaluated DefinitionRef: /MICROSAR/PduR/PduRBswModules/PduRCancelReceive) &amp;&amp; (No PduRBswModule configured which uses the ChangeParameter API.. Evaluated DefinitionRef: /MICROSAR/PduR/PduRBswModules/PduRChangeParameterRequestApi)" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/><constraint description="No transport protocol PduRBswModule configured. Evaluated DefinitionRef: /MICROSAR/PduR/PduRBswModules/PduRTransportProtocol" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_665ee46505ed7fccaeab5bfd456a31f5"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="InvalidHndOfRxTp2Src" scope="Private" xmi:idref="EAID_75f6866b97fdf4d0823f303af662c14f"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_InvalidHndOfRxTp2Src if all values are false" type="Pre-condition"/></Constraints><documentation value="FALSE, if the handle of PduR_RxTp2Src is valid and can be used in the embedded code for further processing in the embedded code."/><properties changeability="frozen" collection="true" type="PduR_InvalidHndOfRxTp2SrcType"/></attribute><attribute name="PartitionIndexOfCslStringSNVOfRxTp2Src" scope="Private" xmi:idref="EAID_27b2c3d06dd0e6a61ab24e8c3a6f6a8e"><Constraints/><properties changeability="frozen" collection="true" type="PduR_PartitionIndexOfCslStringSNVOfRxTp2SrcType"/></attribute></attributes></element><element name="SingleBufferRom:ConstStruct" scope="public" xmi:idref="EAID_1094eee4236ecf35e177133cdc6264b3" xmi:type="uml:Object"><properties documentation="PduR Single Buffer Administration Variables" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_2668ba178aac0dca0fdd477c17b73800"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="SmDataPlaneRom:ConstStruct" scope="public" xmi:idref="EAID_d222d5322d686549e7faf96327a0aa3e" xmi:type="uml:Object"><properties documentation="Switching manager data plane" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="/ActiveEcuC/PduR/PduRGeneral[0:PduRSwitching] is configured to 'false'" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_5254f6337bfd723140a0c7eda31feb8f"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="SmGDestRom:ConstStruct" scope="public" xmi:idref="EAID_1aec27c02ae14791f061fd328d15fc4b" xmi:type="uml:Object"><properties documentation="Switching manager GDest Pdu info" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="/ActiveEcuC/PduR/PduRGeneral[0:PduRSwitching] is configured to 'false'" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_9067012bcacab99344a6409656fac1cb"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ConnectionIdOfSmGDestRom" scope="Private" xmi:idref="EAID_c633d0b3857d9ef7f3a237deccdf98a4"><Constraints/><documentation value="Connection ID"/><properties changeability="frozen" collection="true" type="PduR_ConnectionIdOfSmGDestRomType"/></attribute><attribute name="MetaDataLengthOfSmGDestRom" scope="Private" xmi:idref="EAID_135a9e715a9e90ea3146d37485d4e761"><Constraints/><documentation value="PDU meta data length"/><properties changeability="frozen" collection="true" type="PduR_MetaDataLengthOfSmGDestRomType"/></attribute></attributes></element><element name="SmLinearTaToSaCalculationStrategyRom:ConstStruct" scope="public" xmi:idref="EAID_f2bf0e63135beaf024228fbeb019b7ca" xmi:type="uml:Object"><properties documentation="Linear TA to SA calculation strategy" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="/ActiveEcuC/PduR/PduRGeneral[0:PduRSwitching] is configured to 'false'" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_f2b89722f9740c1902ef731e776b5323"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="MaskOfSmLinearTaToSaCalculationStrategyRom" scope="Private" xmi:idref="EAID_348363331766d285ab9ae34846746485"><Constraints/><properties changeability="frozen" collection="true" type="PduR_MaskOfSmLinearTaToSaCalculationStrategyRomType"/></attribute><attribute name="OffsetOfSmLinearTaToSaCalculationStrategyRom" scope="Private" xmi:idref="EAID_3f24018dd73097760b6c0c29be54120e"><Constraints/><properties changeability="frozen" collection="true" type="PduR_OffsetOfSmLinearTaToSaCalculationStrategyRomType"/></attribute></attributes></element><element name="SmSaTaFromMetaDataCalculationStrategyRom:ConstStruct" scope="public" xmi:idref="EAID_70e88e757e7bdabd5b0eede309663127" xmi:type="uml:Object"><properties documentation="SA and TA determination based on meta data" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="/ActiveEcuC/PduR/PduRGeneral[0:PduRSwitching] is configured to 'false'" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_fd7f24f9684fb748770b4b071c8e5682"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="SaMaskOfSmSaTaFromMetaDataCalculationStrategyRom" scope="Private" xmi:idref="EAID_f89a688eff6629642724b6664d35250d"><Constraints/><properties changeability="frozen" collection="true" type="PduR_SaMaskOfSmSaTaFromMetaDataCalculationStrategyRomType"/></attribute><attribute name="SaStartBitOfSmSaTaFromMetaDataCalculationStrategyRom" scope="Private" xmi:idref="EAID_03a0e5c3b087b8002cc05503dafb9554"><Constraints/><properties changeability="frozen" collection="true" type="PduR_SaStartBitOfSmSaTaFromMetaDataCalculationStrategyRomType"/></attribute><attribute name="TaMaskOfSmSaTaFromMetaDataCalculationStrategyRom" scope="Private" xmi:idref="EAID_46b23185783037bfc1b6a4d9fee59f30"><Constraints/><properties changeability="frozen" collection="true" type="PduR_TaMaskOfSmSaTaFromMetaDataCalculationStrategyRomType"/></attribute><attribute name="TaStartBitOfSmSaTaFromMetaDataCalculationStrategyRom" scope="Private" xmi:idref="EAID_1b413d08ec222c0066cc5c4c70340730"><Constraints/><properties changeability="frozen" collection="true" type="PduR_TaStartBitOfSmSaTaFromMetaDataCalculationStrategyRomType"/></attribute></attributes></element><element name="SmSrcRom:ConstStruct" scope="public" xmi:idref="EAID_9461196ea56528836d05cc4b6730392c" xmi:type="uml:Object"><properties documentation="Switching manager Src Pdu info" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="/ActiveEcuC/PduR/PduRGeneral[0:PduRSwitching] is configured to 'false'" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_a68104e94797c55835d396e14bac6de4"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ConnectionIdOfSmSrcRom" scope="Private" xmi:idref="EAID_066b11b0ec9fb95925edb54e9da98f14"><Constraints/><documentation value="Connection ID"/><properties changeability="frozen" collection="true" type="PduR_ConnectionIdOfSmSrcRomType"/></attribute><attribute name="MetaDataLengthOfSmSrcRom" scope="Private" xmi:idref="EAID_7a95d6881d6d9bd78d313b306ae5da1e"><Constraints/><documentation value="PDU meta data length"/><properties changeability="frozen" collection="true" type="PduR_MetaDataLengthOfSmSrcRomType"/></attribute></attributes></element><element name="SrcApplicationRom:ConstStruct" scope="public" xmi:idref="EAID_42a373c3f3ecb29036fb4b04d2901971" xmi:type="uml:Object"><properties documentation="Lookup table to find the right queue for a src/dest Application tuple." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_ad9aa9f89465ee2999f5bdb3cda91640"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="PartitionIndexOfCslStringSNVOfSrcApplicationRom" scope="Private" xmi:idref="EAID_1c746d3cf60a4e4093ce33c5e79823ac"><Constraints/><properties changeability="frozen" collection="true" type="PduR_PartitionIndexOfCslStringSNVOfSrcApplicationRomType"/></attribute></attributes></element><element name="Tx2Lo:ConstStruct" scope="public" xmi:idref="EAID_eb784c8f5da6a5e3f35b2dd2468414d1" xmi:type="uml:Object"><properties documentation="Contains all information to route a Pdu from a upper layer to a lower layer module, or to cancel a transmission" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No upper layer PduRBswModule configured. Evaluated DefinitionRef: /MICROSAR/PduR/PduRBswModules/PduRUpperModule" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_8444b936c65e70009f7ca5a9e9665030"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="BswMPduRTransmitCallbackOfTx2Lo" scope="Private" xmi:idref="EAID_c948dc6f43b02ecf2687b76284722c2f"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Callback Support is not active" type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_BswMPduRTransmitCallbackOfTx2Lo if all values are false" type="Pre-condition"/></Constraints><documentation value="TRUE if the BswM Transmit callback is enabled"/><properties changeability="frozen" collection="true" type="PduR_BswMPduRTransmitCallbackOfTx2LoType"/></attribute><attribute name="CancelTransmitUsedOfTx2Lo" scope="Private" xmi:idref="EAID_c34c68ce21cbd282dfffeefdfc3c0854"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No PduRBswModule configured which uses the CancelTransmit API.. Evaluated DefinitionRef: /MICROSAR/PduR/PduRBswModules/PduRCancelTransmit" type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_CancelTransmitUsedOfTx2Lo if all values are false" type="Pre-condition"/></Constraints><documentation value="TRUE if the routing can use the CancelTransmit API"/><properties changeability="frozen" collection="true" type="PduR_CancelTransmitUsedOfTx2LoType"/></attribute><attribute name="InvalidHndOfTx2Lo" scope="Private" xmi:idref="EAID_65292e9af27acae0f138abfb64c2a4e0"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_InvalidHndOfTx2Lo if all values are false" type="Pre-condition"/></Constraints><documentation value="FALSE, if the handle of PduR_Tx2Lo is valid and can be used in the embedded code for further processing in the embedded code."/><properties changeability="frozen" collection="true" type="PduR_InvalidHndOfTx2LoType"/></attribute><attribute name="PartitionIndexOfCslStringSNVOfTx2Lo" scope="Private" xmi:idref="EAID_8f8b6b45709a439fbd297ffedc301c22"><Constraints/><properties changeability="frozen" collection="true" type="PduR_PartitionIndexOfCslStringSNVOfTx2LoType"/></attribute></attributes></element><element name="TxIf2Up:ConstStruct" scope="public" xmi:idref="EAID_6f116412ea5a2891211117bd63b53a88" xmi:type="uml:Object"><properties documentation="This table contains all routing information to perform the Tx handling of an interface routing. Used in the &amp;lt;LLIf&amp;gt;_TriggerTransmit and &amp;lt;LLIf&amp;gt;_TxConfirmation" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_2eaa8a92b4cfe3ff5c7cb065880e818a"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="BswMPduRTxConfirmationCallbackOfTxIf2Up" scope="Private" xmi:idref="EAID_f925bcc0330f2359622852bf18e707b5"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Callback Support is not active" type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_BswMPduRTxConfirmationCallbackOfTxIf2Up if all values are false" type="Pre-condition"/></Constraints><documentation value="TRUE if the BswM TxConfirmation callback is enabled"/><properties changeability="frozen" collection="true" type="PduR_BswMPduRTxConfirmationCallbackOfTxIf2UpType"/></attribute><attribute name="InvalidHndOfTxIf2Up" scope="Private" xmi:idref="EAID_9d5d4f5e6cd190f3e57ebbaa35028ddf"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_InvalidHndOfTxIf2Up if all values are false" type="Pre-condition"/></Constraints><documentation value="FALSE, if the handle of PduR_TxIf2Up is valid and can be used in the embedded code for further processing in the embedded code."/><properties changeability="frozen" collection="true" type="PduR_InvalidHndOfTxIf2UpType"/></attribute><attribute name="PartitionIndexOfCslStringSNVOfTxIf2Up" scope="Private" xmi:idref="EAID_3f15f5a95e01dc2ee9d306015cae3339"><Constraints/><properties changeability="frozen" collection="true" type="PduR_PartitionIndexOfCslStringSNVOfTxIf2UpType"/></attribute><attribute name="TxConfirmationUsedOfTxIf2Up" scope="Private" xmi:idref="EAID_f34fc9f98ead642374d38a62755f95f1"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No tx confirmation PduRBswModule configured. Evaluated DefinitionRef: /MICROSAR/PduR/PduRBswModules/PduRTxConfirmation" type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_TxConfirmationUsedOfTxIf2Up if all values are false" type="Pre-condition"/></Constraints><documentation value="True, if any of the source PduRDestPdus uses a TxConfirmation."/><properties changeability="frozen" collection="true" type="PduR_TxConfirmationUsedOfTxIf2UpType"/></attribute></attributes></element><element name="TxTp2Src:ConstStruct" scope="public" xmi:idref="EAID_8e6e97b93852e63959c970b9de0aeedf" xmi:type="uml:Object"><properties documentation="This table contains all routing information to perform the Tx handling of a transport protocol routing, Used in the &amp;lt;LoTp&amp;gt;_CopyTxData and &amp;lt;LoTp&amp;gt;_TxConfirmation" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No transport protocol PduRBswModule configured. Evaluated DefinitionRef: /MICROSAR/PduR/PduRBswModules/PduRTransportProtocol" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_abcf3218739beaf9573094ace0c3aa32"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="BswMPduRTpTxConfirmationCallbackOfTxTp2Src" scope="Private" xmi:idref="EAID_e7e42485eb2c912b68e8b0742e8e0528"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Callback Support is not active" type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_BswMPduRTpTxConfirmationCallbackOfTxTp2Src if all values are false" type="Pre-condition"/></Constraints><documentation value="TRUE if the BswM TpTxConfirmation callback is enabled"/><properties changeability="frozen" collection="true" type="PduR_BswMPduRTpTxConfirmationCallbackOfTxTp2SrcType"/></attribute><attribute name="InvalidHndOfTxTp2Src" scope="Private" xmi:idref="EAID_83bc54875d16e1270bb5fd5f85ef1806"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the PduR_InvalidHndOfTxTp2Src if all values are false" type="Pre-condition"/></Constraints><documentation value="FALSE, if the handle of PduR_TxTp2Src is valid and can be used in the embedded code for further processing in the embedded code."/><properties changeability="frozen" collection="true" type="PduR_InvalidHndOfTxTp2SrcType"/></attribute><attribute name="PartitionIndexOfCslStringSNVOfTxTp2Src" scope="Private" xmi:idref="EAID_833f5ff4ae1a5426080b03bae3e30617"><Constraints/><properties changeability="frozen" collection="true" type="PduR_PartitionIndexOfCslStringSNVOfTxTp2SrcType"/></attribute></attributes></element><element name="BmTxBufferInstanceRam:VarStruct" scope="public" xmi:idref="EAID_156adba42865deea679d50265197677d" xmi:type="uml:Object"><properties documentation="PduR BufferManager TxBufferInstance Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_1cc4cf1613755b28c0b1df4c52b59e76"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="BmTxBufferArrayRamReadIdxOfBmTxBufferInstanceRam" scope="Private" xmi:idref="EAID_20f30a3dfd21043c4dd3887ea956453d"><Constraints/><documentation value="the index of the 0:1 relation pointing to PduR_BmTxBufferArrayRam"/><properties collection="true"/></attribute></attributes></element><element name="BmTxBufferRam:VarStruct" scope="public" xmi:idref="EAID_eb382f6741e6d8e76e2374d6eb6f9f1a" xmi:type="uml:Object"><properties documentation="PduR BufferManager TxBuffer Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_a4640f0d3ffaee4bf1d0a5e35c8cae53"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="AllocatedOfBmTxBufferRam" scope="Private" xmi:idref="EAID_9e243441ffa573be5e72263bed16b3cf"><Constraints/><documentation value="Is true, if the buffer is allocated by a routing."/><properties collection="true"/></attribute><attribute name="BmTxBufferArrayRamWriteIdxOfBmTxBufferRam" scope="Private" xmi:idref="EAID_dbe40ff6b9bb8d0290d061205d01e93b"><Constraints/><documentation value="the index of the 1:1 relation pointing to PduR_BmTxBufferArrayRam"/><properties collection="true"/></attribute><attribute name="RxLengthOfBmTxBufferRam" scope="Private" xmi:idref="EAID_1ec842382c8fd1335f4d79b65371a060"><Constraints/><documentation value="Rx Pdu Sdu length"/><properties collection="true"/></attribute></attributes></element><element name="DeferredEventCacheArrayRam:VarStruct" scope="public" xmi:idref="EAID_5731838bfe297a8735ca0072f323f7d1" xmi:type="uml:Object"><properties documentation="Deferred Event Cache Raw Array" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/><constraint description="No Deferred Event Cache is configured" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_d3e0e325429c4760c6cf5cef924781e1"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="RmGDestRomIdxOfDeferredEventCacheArrayRam" scope="Private" xmi:idref="EAID_04a022d082fef74bb5dae45dd0ec7597"><Constraints/><documentation value="the index of the 1:1 relation pointing to PduR_RmGDestRom"/><properties collection="true"/></attribute></attributes></element><element name="DeferredEventCacheRam:VarStruct" scope="public" xmi:idref="EAID_5cbb97fb371632541269f6ff4078943e" xmi:type="uml:Object"><properties documentation="PduR Deferred Event Cache Administration Variables" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_e43c0e88937ef0566a8696b6c7ad46a1"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="DeferredEventCacheArrayRamReadIdxOfDeferredEventCacheRam" scope="Private" xmi:idref="EAID_41803638f4b8f64827abce4c1cac8cfd"><Constraints/><documentation value="the index of the 1:1 relation pointing to PduR_DeferredEventCacheArrayRam"/><properties collection="true"/></attribute><attribute name="DeferredEventCacheArrayRamWriteIdxOfDeferredEventCacheRam" scope="Private" xmi:idref="EAID_bead58e73c2e1dfb1622b864e13f1fd5"><Constraints/><documentation value="the index of the 1:1 relation pointing to PduR_DeferredEventCacheArrayRam"/><properties collection="true"/></attribute></attributes></element><element name="FmFifoElementRam:VarStruct" scope="public" xmi:idref="EAID_1969ac3e9bae6b0e03744b6ff04754a6" xmi:type="uml:Object"><properties documentation="PduR FiFoManager FIFO element table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_8fa077486dbab918a5c7ee7b74537a0e"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="BmTxBufferRomIdxOfFmFifoElementRam" scope="Private" xmi:idref="EAID_950e2247480911e5cbaf1b9c532fe714"><Constraints/><documentation value="the index of the 0:1 relation pointing to PduR_BmTxBufferRom"/><properties collection="true"/></attribute><attribute name="RmDestRomIdxOfFmFifoElementRam" scope="Private" xmi:idref="EAID_1a688e3ff3893bf36cde41166dc5b1ef"><Constraints/><documentation value="the index of the 1:1 relation pointing to PduR_RmDestRom"/><properties collection="true"/></attribute></attributes></element><element name="FmFifoInstanceRam:VarStruct" scope="public" xmi:idref="EAID_311e80b8512b8aeb9ca972b84b6f37aa" xmi:type="uml:Object"><properties documentation="Instance of the PduRDestPdus using a single Fifo" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_fc33dafdbd713b970925d5f7faa1a9ee"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="BmTxBufferInstanceRomIdxOfFmFifoInstanceRam" scope="Private" xmi:idref="EAID_9fe591532b2665bd58513ab19fdc3cda"><Constraints/><documentation value="the index of the 0:1 relation pointing to PduR_BmTxBufferInstanceRom"/><properties collection="true"/></attribute></attributes></element><element name="FmFifoRam:VarStruct" scope="public" xmi:idref="EAID_092c720570d1fab8a4f6e70d7ae36e74" xmi:type="uml:Object"><properties documentation="PduR FiFoManager Fifo Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_d04342489cc8a76a9e0dcf6ab71b4002"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="FillLevelOfFmFifoRam" scope="Private" xmi:idref="EAID_5b86ef8ff53559155eec4818b41e2dbe"><Constraints/><documentation value="Fill level of the FIFO queue"/><properties collection="true"/></attribute><attribute name="FmFifoElementRamReadIdxOfFmFifoRam" scope="Private" xmi:idref="EAID_fdb8792168609fe23f219ab8ae624f4d"><Constraints/><documentation value="the index of the 1:1 relation pointing to PduR_FmFifoElementRam"/><properties collection="true"/></attribute><attribute name="FmFifoElementRamWriteIdxOfFmFifoRam" scope="Private" xmi:idref="EAID_7f1f718a069268e9c9b5c0f7d904538f"><Constraints/><documentation value="the index of the 1:1 relation pointing to PduR_FmFifoElementRam"/><properties collection="true"/></attribute><attribute name="PendingConfirmationsOfFmFifoRam" scope="Private" xmi:idref="EAID_3e3848cea0b390a5792b3e2952e40a49"><Constraints/><documentation value="Number of pending Tx Confirmations of all possible destinations."/><properties collection="true"/></attribute><attribute name="TpTxSmStateOfFmFifoRam" scope="Private" xmi:idref="EAID_97eecd0d086bd0e76be2f351ca02a564"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No TransportProtocol BswModule active." type="Pre-condition"/><Constraint name="CUserPreCompileDeactivationCondition" notes="No queued TransportProtocol Routing available" type="Pre-condition"/></Constraints><documentation value="Tp Tx state"/><properties collection="true"/></attribute></attributes></element><element name="InterfaceFifoQueueElementRam:VarStruct" scope="public" xmi:idref="EAID_bc2a2eef55c5b6c390d689695c6c065d" xmi:type="uml:Object"><properties documentation="PduR Interface Fifo Queue element" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_cc5c753aee66dfc6b6bfc7cffe37b925"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ActualLengthOfInterfaceFifoQueueElementRam" scope="Private" xmi:idref="EAID_0d2c07f107fdde8bf55bd5a5d17a8369"><Constraints/><documentation value="Actual length of the currently queued Pdu."/><properties collection="true"/></attribute></attributes></element><element name="InterfaceFifoQueueRam:VarStruct" scope="public" xmi:idref="EAID_4bc8c2a4364454950d3fc9185ff25be7" xmi:type="uml:Object"><properties documentation="PduR Interface Fifo Queue Administration Variables" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_2a25e93e75d2f5a592fe4529e442939a"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="FifoFullOfInterfaceFifoQueueRam" scope="Private" xmi:idref="EAID_ab4d3dd2b91c459bb2cdae0cd97234c0"><Constraints/><documentation value="Is the Fifo queue full."/><properties collection="true"/></attribute><attribute name="InterfaceFifoQueueElementRamReadIdxOfInterfaceFifoQueueRam" scope="Private" xmi:idref="EAID_5c5106344c4448a3c4eefb7455a45970"><Constraints/><documentation value="the index of the 1:1 relation pointing to PduR_InterfaceFifoQueueElementRam"/><properties collection="true"/></attribute><attribute name="InterfaceFifoQueueElementRamWriteIdxOfInterfaceFifoQueueRam" scope="Private" xmi:idref="EAID_9fd5293a2dba9518bb9792dde5e35d6d"><Constraints/><documentation value="the index of the 1:1 relation pointing to PduR_InterfaceFifoQueueElementRam"/><properties collection="true"/></attribute></attributes></element><element name="McQBufferRam:VarStruct" scope="public" xmi:idref="EAID_92d7a62d66b9994ec2d11f1b3f4ce883" xmi:type="uml:Object"><properties documentation="PduR Multicore Queue Administration Variables" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_768233654841f0fadf9626cdd7542ab4"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="McQBufferArrayRamPendingReadIdxOfMcQBufferRam" scope="Private" xmi:idref="EAID_ee96c82d57858c092c9981e374b2d2b6"><Constraints/><documentation value="the index of the 1:1 relation pointing to PduR_McQBufferArrayRam"/><properties collection="true"/></attribute><attribute name="McQBufferArrayRamPendingWriteIdxOfMcQBufferRam" scope="Private" xmi:idref="EAID_a161ec7e58d05c6623ca0815e3a54d2f"><Constraints/><documentation value="the index of the 1:1 relation pointing to PduR_McQBufferArrayRam"/><properties collection="true"/></attribute><attribute name="McQBufferArrayRamReadIdxOfMcQBufferRam" scope="Private" xmi:idref="EAID_6f17b98a7d0e460813cc4eee0b4a36ce"><Constraints/><documentation value="the index of the 1:1 relation pointing to PduR_McQBufferArrayRam"/><properties collection="true"/></attribute><attribute name="McQBufferArrayRamWriteIdxOfMcQBufferRam" scope="Private" xmi:idref="EAID_c529e044bb3bb2206a17aba305c8c8ec"><Constraints/><documentation value="the index of the 1:1 relation pointing to PduR_McQBufferArrayRam"/><properties collection="true"/></attribute></attributes></element><element name="PartitionLookupTableRam:VarStruct" scope="public" xmi:idref="EAID_e6e65771c0ea0439fca00096f15349f6" xmi:type="uml:Object"><properties documentation="Partition Lookup Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_aba822cee440b62ca24d22d9475d62c0"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="OsApplicationToPduROfPartitionLookupTableRam" scope="Private" xmi:idref="EAID_5690e2fded782a999066c448ad6e5c04"><Constraints/><documentation value="Os Application To PduR mapping."/><properties collection="true"/></attribute></attributes></element><element name="RmBufferedIfPropertiesRam:VarStruct" scope="public" xmi:idref="EAID_1acf8bf38bb57644a40632c9dc8897a2" xmi:type="uml:Object"><properties documentation="Properties of buffered communication interface routings." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_8197493076d1f97143f5e474be036b38"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="IfSmStateOfRmBufferedIfPropertiesRam" scope="Private" xmi:idref="EAID_5ac300afcff237f7887863a94335d363"><Constraints/><documentation value="IF State Machine state"/><properties collection="true"/></attribute></attributes></element><element name="RmBufferedTpPropertiesRam:VarStruct" scope="public" xmi:idref="EAID_752bb397ec1390a1388d1f37405ae8ea" xmi:type="uml:Object"><properties documentation="PduR RoutiongManager Properties of buffered Tp routing paths." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_223be9a4694c7c2160e74e6c9f1f18f7"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="FmFifoElementRamIdxOfRmBufferedTpPropertiesRam" scope="Private" xmi:idref="EAID_cb57c3ba01afad943c0ab772b08f886c"><Constraints/><documentation value="the index of the 0:1 relation pointing to PduR_FmFifoElementRam"/><properties collection="true"/></attribute><attribute name="TpRxSmStateOfRmBufferedTpPropertiesRam" scope="Private" xmi:idref="EAID_39daa497ee08efde9c837e6b0a351599"><Constraints/><documentation value="Tp source instance state"/><properties collection="true"/></attribute></attributes></element><element name="RmDestRpgRam:VarStruct" scope="public" xmi:idref="EAID_4032c8e6f38d693bf11cf65dcf952897" xmi:type="uml:Object"><properties documentation="PduRDestPdu specific PduRRoutingPathGroup information." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_a61403e404b16742a4c7302f3d43db7c"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="EnabledCntOfRmDestRpgRam" scope="Private" xmi:idref="EAID_8fd04f01d56ed3b5ab8bc8b365fc46e4"><Constraints/><properties collection="true"/></attribute></attributes></element><element name="RmGDestNto1InfoRam:VarStruct" scope="public" xmi:idref="EAID_7b6fe4565a5cc2aeee984f88620ef58d" xmi:type="uml:Object"><properties documentation="PduR RoutingPathManager global destPdu Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_2ef7875863f2f1751cba62aee8fc251a"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="RmDestRomIdxOfRmGDestNto1InfoRam" scope="Private" xmi:idref="EAID_718de25127ec8d4072bca52584778fdb"><Constraints/><documentation value="the index of the 0:1 relation pointing to PduR_RmDestRom"/><properties collection="true"/></attribute><attribute name="TransmissionActiveOfRmGDestNto1InfoRam" scope="Private" xmi:idref="EAID_d8339e8edc4350b1bd383b6906ff1d29"><Constraints/><documentation value="TRUE if a transmission is ongoing right now."/><properties collection="true"/></attribute></attributes></element><element name="RmGDestTpTxStateRam:VarStruct" scope="public" xmi:idref="EAID_ba1a1d2ef2705f55b52c59a0d0c07899" xmi:type="uml:Object"><properties documentation="PduR RoutiongPathManager global destPdu Tp Tx State" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/><constraint description="No TransportProtocol BswModule active." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_45d215e3f519a5175b39a46a9f547ed8"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="TpTxInstSmStateOfRmGDestTpTxStateRam" scope="Private" xmi:idref="EAID_e5306f2779c71f8bb6151c788bba2a6e"><Constraints/><documentation value="Tp dest instance state"/><properties collection="true"/></attribute></attributes></element><element name="RpgRam:VarStruct" scope="public" xmi:idref="EAID_0af0b029b9c2a583f13c3e45c8e8ad4d" xmi:type="uml:Object"><properties documentation="PduRRoutingPathGroup specific RAM information." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_d8dd2a5891aaf257a914411d034417eb"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="EnabledOfRpgRam" scope="Private" xmi:idref="EAID_28ed8f60840c6774d6c29015f488d334"><Constraints/><documentation value="TRUE, if the PduRRoutingPathGroup is active"/><properties collection="true"/></attribute></attributes></element><element name="SingleBufferRam:VarStruct" scope="public" xmi:idref="EAID_33747b7b74f574b918549d35b4ae5f52" xmi:type="uml:Object"><properties documentation="PduR Single Buffer Administration Variables" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_a9b2cbee6360a92be12e337f663a0613"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ActualLengthOfSingleBufferRam" scope="Private" xmi:idref="EAID_50e2e220585f46f53d34946530d29cff"><Constraints/><documentation value="Actual length of the currently queued Pdu."/><properties collection="true"/></attribute></attributes></element><element name="SmFibRam:VarStruct" scope="public" xmi:idref="EAID_daa8b6f32a84c888e6a6c7b47d00f205" xmi:type="uml:Object"><properties documentation="Switching manager Forwarding Information Base (FIB)" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="/ActiveEcuC/PduR/PduRGeneral[0:PduRSwitching] is configured to 'false'" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_f202b94ca2da61ff7cfb09e84a198933"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="LearnedConnectionIdOfSmFibRam" scope="Private" xmi:idref="EAID_12758ec90440604f8ab3d728c0d5103e"><Constraints/><documentation value="Learned connection ID"/><properties collection="true"/></attribute></attributes></element><element name="SpinlockRam:VarStruct" scope="public" xmi:idref="EAID_d9aa7d92241e260643b1358dffacb978" xmi:type="uml:Object"><properties documentation="PduR Spin Lock" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="/ActiveEcuC/PduR/PduRGeneral[0:PduRSupportMulticore] is configured to 'false'" name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/><constraint description="Never Deactivated Buffered Routing." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_0ee01775a191ac880968132f3e568ae6"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="CounterOfSpinlockRam" scope="Private" xmi:idref="EAID_9f9f078f25376023570136799e3f031e"><Constraints/><documentation value="Counter for Spinlocks"/><properties collection="true"/></attribute><attribute name="LockVariableOfSpinlockRam" scope="Private" xmi:idref="EAID_c205d64722a4479d8120a4828c7f3c9f"><Constraints/><documentation value="Locking variable which is used by the external spinlock function."/><properties collection="true"/></attribute></attributes></element><element name="RmTransmitFctPtr:ConstArray" scope="public" xmi:idref="EAID_7a67a4fb4e819882670e169718e8787b" xmi:type="uml:Object"><properties documentation="Internal routing manager Transmit functions." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="RmTransmitFctPtr" scope="Private" xmi:idref="EAID_9eddf7ee0593fa88672a3047e6b5ed90"><Constraints/><documentation value="Internal routing manager Transmit functions."/><properties changeability="frozen" collection="true" type="PduR_RmTransmitFctPtrType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_52d362cf26baebd81a41e6f57fb880ab"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="SingleBufferInitValuesRom:ConstArray" scope="public" xmi:idref="EAID_8296be806b543bbc3f27974973f54c80" xmi:type="uml:Object"><properties documentation="Init values for single buffers." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="SingleBufferInitValuesRom" scope="Private" xmi:idref="EAID_72161641cb13386ca8482a5ebaa0ff39"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Never Deactivated Buffered Routing." type="Pre-condition"/></Constraints><documentation value="Init values for single buffers."/><properties changeability="frozen" collection="true" type="PduR_SingleBufferInitValuesRomType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_77620941d69503ad698d3a8726b5ee8e"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="SmGDestFilterFctPtr:ConstArray" scope="public" xmi:idref="EAID_d5a103e5a6cc0475c1ec3dc8e1dbd083" xmi:type="uml:Object"><properties documentation="GDest Pdu filter" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="SmGDestFilterFctPtr" scope="Private" xmi:idref="EAID_8b7a3771129cbe0023a8c109b763f452"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="/ActiveEcuC/PduR/PduRGeneral[0:PduRSwitching] is configured to 'false'" type="Pre-condition"/></Constraints><documentation value="GDest Pdu filter"/><properties changeability="frozen" collection="true" type="PduR_Sm_DestFilterFctPtrType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_57d1ea5e26d004043b2d756c9f115d0b"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="SmLinearTaToSaCalculationStrategyGetSaFctPtr:ConstArray" scope="public" xmi:idref="EAID_93bb0c9746ce5aeb167c4035c538984f" xmi:type="uml:Object"><properties documentation="Linear SA calculation strategy" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="SmLinearTaToSaCalculationStrategyGetSaFctPtr" scope="Private" xmi:idref="EAID_29b494146afbd0f36f985f5d7dbf80ab"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="/ActiveEcuC/PduR/PduRGeneral[0:PduRSwitching] is configured to 'false'" type="Pre-condition"/></Constraints><documentation value="Linear SA calculation strategy"/><properties changeability="frozen" collection="true" type="PduR_Sm_LinearTaToSaCalculationStrategy_GetSaFctPtrType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_bc9f0dcc0daeedfbe5aa6450e167c0fb"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="SmSrcFilterFctPtr:ConstArray" scope="public" xmi:idref="EAID_72292af1e228249cb8ec3f91aa7c30ec" xmi:type="uml:Object"><properties documentation="Src Pdu filter" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="SmSrcFilterFctPtr" scope="Private" xmi:idref="EAID_a8a892d302359ebc284db69fcaa2fdec"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="/ActiveEcuC/PduR/PduRGeneral[0:PduRSwitching] is configured to 'false'" type="Pre-condition"/></Constraints><documentation value="Src Pdu filter"/><properties changeability="frozen" collection="true" type="PduR_Sm_SrcFilterFctPtrType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_675324d8110a0051edef55da9d3c1b94"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ConfigId:ConstVar" scope="public" xmi:idref="EAID_cf7a45675a928b9a1174720d2d0459e7" xmi:type="uml:Object"><properties documentation="DefinitionRef: /MICROSAR/PduR/PduRRoutingTables/PduRConfigurationId" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ConfigId" scope="Private" xmi:idref="EAID_4795c954e848aa44734a92a1af58b8c0"><Constraints/><documentation value="DefinitionRef: /MICROSAR/PduR/PduRRoutingTables/PduRConfigurationId"/><properties changeability="frozen" type="PduR_ConfigIdType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_f18d94d6b6b6c494b2a78bb3eb35e134"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="BmTxBufferArrayRam:VarArray" scope="public" xmi:idref="EAID_0b3bb79055f64547aff63ad1c9af60aa" xmi:type="uml:Object"><properties documentation="PduR BufferManagere TxBufferArray Table" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="BmTxBufferArrayRam" scope="Private" xmi:idref="EAID_be17388f34fb9215efa966a278aa596e"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Never Deactivated Buffered Routing." type="Pre-condition"/></Constraints><documentation value="PduR BufferManagere TxBufferArray Table"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_bb47eaa4d8b551e772a79beb80b3d2e8"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="InterfaceFifoQueueArrayRam:VarArray" scope="public" xmi:idref="EAID_b3a723d4b5036b9c36e217c7db701110" xmi:type="uml:Object"><properties documentation="Interface Fifo queue array." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="InterfaceFifoQueueArrayRam" scope="Private" xmi:idref="EAID_f5cdee3ecae0302167893e3382d9bee4"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Never Deactivated Buffered Routing." type="Pre-condition"/></Constraints><documentation value="Interface Fifo queue array."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_506064b66f4758be7c1d2357886f65ec"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="McQBufferArrayRam:VarArray" scope="public" xmi:idref="EAID_98ead75a4e649dab381a9d3c11c70cf3" xmi:type="uml:Object"><properties documentation="PduR Multicore Queue Buffer" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="McQBufferArrayRam" scope="Private" xmi:idref="EAID_56ed886753fb0dd551bfa83697269461"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="/ActiveEcuC/PduR/PduRGeneral[0:PduRSupportMulticore] is configured to 'false'" type="Pre-condition"/><Constraint name="IndirectableFeatureDeactivationCondition" notes="Never Deactivated Buffered Routing." type="Pre-condition"/><Constraint name="IndirectableFeatureDeactivationCondition" notes="No MulticoreQueueSize is configured" type="Pre-condition"/></Constraints><documentation value="PduR Multicore Queue Buffer"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_60b6f488832a0eb967a60e2ed2fb3366"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="SingleBufferArrayRam:VarArray" scope="public" xmi:idref="EAID_2b5839f711308fa5f78c48a05d874fb7" xmi:type="uml:Object"><properties documentation="Single buffer array." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="SingleBufferArrayRam" scope="Private" xmi:idref="EAID_63619fc6aec3ce9717839e6895ef0b85"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Never Deactivated Buffered Routing." type="Pre-condition"/></Constraints><documentation value="Single buffer array."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_366772c495b2d5bcb5c909b073d0d087"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="Initialized:Var" scope="public" xmi:idref="EAID_045c79f73ab47665923017576a0b630d" xmi:type="uml:Object"><properties documentation="Initialization state of PduR. TRUE, if PduR_Init() has been called, else FALSE" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="Initialized" scope="Private" xmi:idref="EAID_8be96278268205bdeaa9aac5b1366ea3"><Constraints/><documentation value="Initialization state of PduR. TRUE, if PduR_Init() has been called, else FALSE"/><properties/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_1cd452f35061813df58fa6a4b1cf3e69"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element></elements><connectors><connector xmi:idref="EAID_9a3c28261ab10588d5ccfa9cbeafd680"><source xmi:idref="EAID_ded26787e1860b9df6fe48031a13de3c"><model name="ApplicationId2DestApplicationManagerRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_246116c073f4615685b6f46a07d525a0"><model name="DestApplicationManagerRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_0db42fb60965aab1ba79b0c9efe39c1e"><source xmi:idref="EAID_ee5e575cfac73ac43d3903e69fae8a14"><model name="BmTxBufferIndRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_bdac7bdeb331402ce6cbd145cfdd1475"><model name="BmTxBufferRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_32bc990093dbc442e5d8fa45eaf4d1a1"><source xmi:idref="EAID_9c252bf0f9e011d96f8bdba9fe4b5cea"><model name="BmTxBufferInstanceRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_bdac7bdeb331402ce6cbd145cfdd1475"><model name="BmTxBufferRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_ec0d2f2de9131a0514bed5feca04d43d"><source xmi:idref="EAID_bdac7bdeb331402ce6cbd145cfdd1475"><model name="BmTxBufferRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_0b3bb79055f64547aff63ad1c9af60aa"><model name="BmTxBufferArrayRam" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_7cb59b5c256a487b5623bd8aea51493b"><source xmi:idref="EAID_bdac7bdeb331402ce6cbd145cfdd1475"><model name="BmTxBufferRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_9c252bf0f9e011d96f8bdba9fe4b5cea"><model name="BmTxBufferInstanceRom" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_e1635822b42104099e8bb45a717ff5ce"><source xmi:idref="EAID_aa860125f8f09286c32f05e4ab4216de"><model name="DeferredEventCacheRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_5731838bfe297a8735ca0072f323f7d1"><model name="DeferredEventCacheArrayRam" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_d827c005628dcab444ad7aa4f1e0d2cc"><source xmi:idref="EAID_246116c073f4615685b6f46a07d525a0"><model name="DestApplicationManagerRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_624c032ecf7e8bbf68ce1e04f4bbb430"><model name="MmRomInd" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_3e7d7362e953e514d949c7d9741b59d0"><source xmi:idref="EAID_246116c073f4615685b6f46a07d525a0"><model name="DestApplicationManagerRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_cf385f1f71ca109b9ac36c596c7c61ea"><model name="MmRom" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_4e2b5f7b225d9aba7f5e35f03e30567d"><source xmi:idref="EAID_246116c073f4615685b6f46a07d525a0"><model name="DestApplicationManagerRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_42a373c3f3ecb29036fb4b04d2901971"><model name="SrcApplicationRom" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_a51d1d44b8917d03c57bf2654d4f420b"><source xmi:idref="EAID_da88b58e5fb558917c7fd7ec0171ae7e"><model name="FmFifoInstanceRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_9c46d1fe89ae9eec3af3b950598487b5"><model name="FmFifoRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_5c99108e3e9b854a84b37159c113c2cc"><source xmi:idref="EAID_9c46d1fe89ae9eec3af3b950598487b5"><model name="FmFifoRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_1969ac3e9bae6b0e03744b6ff04754a6"><model name="FmFifoElementRam" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_203431f398328fdb2be8ae423e4ffb32"><source xmi:idref="EAID_9c46d1fe89ae9eec3af3b950598487b5"><model name="FmFifoRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_ee5e575cfac73ac43d3903e69fae8a14"><model name="BmTxBufferIndRom" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_381078806b92be9b5ebea9b9249e2119"><source xmi:idref="EAID_9c46d1fe89ae9eec3af3b950598487b5"><model name="FmFifoRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_46776a359f0d8191bf9118726beaaa45"><model name="LockRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_dde9ae6da66ec000836378abc808800f"><source xmi:idref="EAID_87c915abc9c5e5ab37d73383fe969ff5"><model name="InterfaceFifoQueueRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_b3a723d4b5036b9c36e217c7db701110"><model name="InterfaceFifoQueueArrayRam" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_dbfc1d3d3c864d29850188977333d0e7"><source xmi:idref="EAID_87c915abc9c5e5ab37d73383fe969ff5"><model name="InterfaceFifoQueueRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_bc2a2eef55c5b6c390d689695c6c065d"><model name="InterfaceFifoQueueElementRam" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_27cab97755d70143c1b3835c40367b6c"><source xmi:idref="EAID_87c915abc9c5e5ab37d73383fe969ff5"><model name="InterfaceFifoQueueRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"><model name="RmDestRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_13f3650a29a87b1d43673ff16283f11a"><source xmi:idref="EAID_46776a359f0d8191bf9118726beaaa45"><model name="LockRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_1f152a97dc071ceae1b8714a07a7e8b2"><model name="ExclusiveAreaRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_ae5c10aa1043e67b3af61df1001b416a"><source xmi:idref="EAID_46776a359f0d8191bf9118726beaaa45"><model name="LockRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_d9aa7d92241e260643b1358dffacb978"><model name="SpinlockRam" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_db579899ff759aad4a339099f1ba4d39"><source xmi:idref="EAID_b6ed023068eb5bfcc230f923085ee524"><model name="McQBufferRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_98ead75a4e649dab381a9d3c11c70cf3"><model name="McQBufferArrayRam" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_3d252f71416ab2a602c1bce513b67228"><source xmi:idref="EAID_cf385f1f71ca109b9ac36c596c7c61ea"><model name="MmRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_c753b8257c1177208da906dd979acb10"><model name="RmGDestRom" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_774427d189f31852220c1b4dd51e1281"><source xmi:idref="EAID_cf385f1f71ca109b9ac36c596c7c61ea"><model name="MmRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_246116c073f4615685b6f46a07d525a0"><model name="DestApplicationManagerRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_1141591835a460e3ddd8762eb7bb4656"><source xmi:idref="EAID_45ca483d77f9f774d388da7e12e67d82"><model name="RmBufferedIfPropertiesRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_1094eee4236ecf35e177133cdc6264b3"><model name="SingleBufferRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_30b7b60297f136c318438d156a8a1628"><source xmi:idref="EAID_45ca483d77f9f774d388da7e12e67d82"><model name="RmBufferedIfPropertiesRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_87c915abc9c5e5ab37d73383fe969ff5"><model name="InterfaceFifoQueueRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_d4a01b9398db8aac32dc4ac7b064b45a"><source xmi:idref="EAID_45ca483d77f9f774d388da7e12e67d82"><model name="RmBufferedIfPropertiesRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_c565d59b2faa599cd32de993a7ad0395"><model name="QueueFctPtrRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_e708b7011b2cf0808542f044575a06a5"><source xmi:idref="EAID_d09498d242e10c71f0708b3ae5d8c375"><model name="RmBufferedTpPropertiesRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_9c46d1fe89ae9eec3af3b950598487b5"><model name="FmFifoRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_ccf0bc21f51ed7ace275d1e4819fa593"><source xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"><model name="RmDestRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_2f3d49b44ebddb55363af41561fd35a6"><model name="RmDestRpgRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_b18dbb9cb2897f0102d9ef1823c5a348"><source xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"><model name="RmDestRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_c753b8257c1177208da906dd979acb10"><model name="RmGDestRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_9f9308775d173cba59331c14c4e75502"><source xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"><model name="RmDestRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"><model name="RmSrcRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_cdc216229b7360bc51755b20ddf7eb99"><source xmi:idref="EAID_2f3d49b44ebddb55363af41561fd35a6"><model name="RmDestRpgRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"><model name="RmDestRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_a0678b6714d58c3b11593fb3cf3b08f3"><source xmi:idref="EAID_c753b8257c1177208da906dd979acb10"><model name="RmGDestRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_da88b58e5fb558917c7fd7ec0171ae7e"><model name="FmFifoInstanceRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_d37632adbb87d7325b9dd837c9799189"><source xmi:idref="EAID_c753b8257c1177208da906dd979acb10"><model name="RmGDestRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_1aec27c02ae14791f061fd328d15fc4b"><model name="SmGDestRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_b817ce47c5d143afb8a543a75d96e23d"><source xmi:idref="EAID_c753b8257c1177208da906dd979acb10"><model name="RmGDestRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"><model name="RmDestRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_16f555da32f9c7812530d8a55e5c8f1d"><source xmi:idref="EAID_c753b8257c1177208da906dd979acb10"><model name="RmGDestRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_cf385f1f71ca109b9ac36c596c7c61ea"><model name="MmRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_17147fe9242479a57ceb30e2ddaf36c4"><source xmi:idref="EAID_c753b8257c1177208da906dd979acb10"><model name="RmGDestRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_45ca483d77f9f774d388da7e12e67d82"><model name="RmBufferedIfPropertiesRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_10b8694d3938011217de0e5feddccf51"><source xmi:idref="EAID_c753b8257c1177208da906dd979acb10"><model name="RmGDestRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_ba1a1d2ef2705f55b52c59a0d0c07899"><model name="RmGDestTpTxStateRam" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_f24a81a1159ad505c0f15bd48f6ed0c8"><source xmi:idref="EAID_c753b8257c1177208da906dd979acb10"><model name="RmGDestRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_7b6fe4565a5cc2aeee984f88620ef58d"><model name="RmGDestNto1InfoRam" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_03ff7484cf384ced46945c7cb1b929a4"><source xmi:idref="EAID_c753b8257c1177208da906dd979acb10"><model name="RmGDestRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_46776a359f0d8191bf9118726beaaa45"><model name="LockRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_a641c59fe8166729cb8802c6604abd76"><source xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"><model name="RmSrcRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"><model name="RmDestRom" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_673d91c2ca235e07ae1891881989f6ae"><source xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"><model name="RmSrcRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_9461196ea56528836d05cc4b6730392c"><model name="SmSrcRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_0b9ef36cf91d071d3d45824c92bd4d32"><source xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"><model name="RmSrcRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_cf385f1f71ca109b9ac36c596c7c61ea"><model name="MmRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_08311f9ba92be36886736868a3058b92"><source xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"><model name="RmSrcRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_d09498d242e10c71f0708b3ae5d8c375"><model name="RmBufferedTpPropertiesRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_d440f12708a8de182e19d5b933c303bb"><source xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"><model name="RmSrcRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_46776a359f0d8191bf9118726beaaa45"><model name="LockRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_b9bf3e8b9ef1f6e24ebf80d00ab8bfcb"><source xmi:idref="EAID_10d0028e2d69ffb1cc13eec66d424cb4"><model name="RpgExtIdRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_b3f09b00ce1f1c35f117db4c29088408"><model name="RpgRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_92124387d4aa35f25e5841956e34ca11"><source xmi:idref="EAID_b3f09b00ce1f1c35f117db4c29088408"><model name="RpgRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_76c65e39cb1673bcdf0b4c0aadafd675"><model name="RmDestRpgRomInd" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_d421a039dd4e3a8d28499a2a0f2e29f4"><source xmi:idref="EAID_b3f09b00ce1f1c35f117db4c29088408"><model name="RpgRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_2f3d49b44ebddb55363af41561fd35a6"><model name="RmDestRpgRom" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_d9627ad31d5900738e3e2056ef30dee9"><source xmi:idref="EAID_138f302e49968a3d5bd92e5e64e5cd88"><model name="RxIf2Dest" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"><model name="RmSrcRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_7cfe881fe1a15754e2fbbf6e450e2093"><source xmi:idref="EAID_2d16a949452f3e8f68f8eef2a24a09f3"><model name="RxTp2Dest" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"><model name="RmSrcRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_dcce7b235763a1c36ece8cf6a85639fc"><source xmi:idref="EAID_19888a57d3bbef12b32188523d597738"><model name="RxTp2Src" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"><model name="RmDestRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_525b7ab2f81edb7bc6fb104126a7f73b"><source xmi:idref="EAID_1094eee4236ecf35e177133cdc6264b3"><model name="SingleBufferRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_2b5839f711308fa5f78c48a05d874fb7"><model name="SingleBufferArrayRam" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_b1c21dac2beb755c4ff8bb3240c2626c"><source xmi:idref="EAID_1094eee4236ecf35e177133cdc6264b3"><model name="SingleBufferRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_8296be806b543bbc3f27974973f54c80"><model name="SingleBufferInitValuesRom" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_f7167d18e42032c0592a926f22c0459c"><source xmi:idref="EAID_1094eee4236ecf35e177133cdc6264b3"><model name="SingleBufferRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"><model name="RmDestRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_2cc39486d56b4aaf136e471a8e41e33b"><source xmi:idref="EAID_d222d5322d686549e7faf96327a0aa3e"><model name="SmDataPlaneRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_daa8b6f32a84c888e6a6c7b47d00f205"><model name="SmFibRam" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_62e202d303415e5837b85ea3b1c60826"><source xmi:idref="EAID_d222d5322d686549e7faf96327a0aa3e"><model name="SmDataPlaneRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_f2bf0e63135beaf024228fbeb019b7ca"><model name="SmLinearTaToSaCalculationStrategyRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_e8e52e63b342f336eec5bf1b991f2ceb"><source xmi:idref="EAID_d222d5322d686549e7faf96327a0aa3e"><model name="SmDataPlaneRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_70e88e757e7bdabd5b0eede309663127"><model name="SmSaTaFromMetaDataCalculationStrategyRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_b0583b6cb4aafcb48816df39b7690f48"><source xmi:idref="EAID_1aec27c02ae14791f061fd328d15fc4b"><model name="SmGDestRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_d222d5322d686549e7faf96327a0aa3e"><model name="SmDataPlaneRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_47078b0e11aa41e2aeb4782b635611bb"><source xmi:idref="EAID_1aec27c02ae14791f061fd328d15fc4b"><model name="SmGDestRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_d5a103e5a6cc0475c1ec3dc8e1dbd083"><model name="SmGDestFilterFctPtr" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_20fe2765a4cb6729b3d9eeb5499ce645"><source xmi:idref="EAID_9461196ea56528836d05cc4b6730392c"><model name="SmSrcRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_d222d5322d686549e7faf96327a0aa3e"><model name="SmDataPlaneRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_d85bfe1b8ef2c3471b98fe16c0a0e018"><source xmi:idref="EAID_9461196ea56528836d05cc4b6730392c"><model name="SmSrcRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_93bb0c9746ce5aeb167c4035c538984f"><model name="SmLinearTaToSaCalculationStrategyGetSaFctPtr" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_f8ce52d2fb8abb4d11c6a29adce01a97"><source xmi:idref="EAID_9461196ea56528836d05cc4b6730392c"><model name="SmSrcRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_72292af1e228249cb8ec3f91aa7c30ec"><model name="SmSrcFilterFctPtr" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_9686d56487a779926aeb755d1cbafc48"><source xmi:idref="EAID_42a373c3f3ecb29036fb4b04d2901971"><model name="SrcApplicationRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_aa860125f8f09286c32f05e4ab4216de"><model name="DeferredEventCacheRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_bfc8afc358392da397211dce03a7a5ed"><source xmi:idref="EAID_42a373c3f3ecb29036fb4b04d2901971"><model name="SrcApplicationRom" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_b6ed023068eb5bfcc230f923085ee524"><model name="McQBufferRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_0aee36c9c32633d8d1571d62ad40c603"><source xmi:idref="EAID_eb784c8f5da6a5e3f35b2dd2468414d1"><model name="Tx2Lo" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"><model name="RmSrcRom" type="Object"/><type aggregation="none" multiplicity="0..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..1"/></connector><connector xmi:idref="EAID_a6237fd892fdd21f52996c491159f780"><source xmi:idref="EAID_eb784c8f5da6a5e3f35b2dd2468414d1"><model name="Tx2Lo" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_7a67a4fb4e819882670e169718e8787b"><model name="RmTransmitFctPtr" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_b3a1326635e6a7974eb5b3dca062a699"><source xmi:idref="EAID_6f116412ea5a2891211117bd63b53a88"><model name="TxIf2Up" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_c753b8257c1177208da906dd979acb10"><model name="RmGDestRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_4834f1560178265da7c0099da85c0656"><source xmi:idref="EAID_8e6e97b93852e63959c970b9de0aeedf"><model name="TxTp2Src" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_c753b8257c1177208da906dd979acb10"><model name="RmGDestRom" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_e81304b62134424597e9f179a169e422"><source xmi:idref="EAID_156adba42865deea679d50265197677d"><model name="BmTxBufferInstanceRam" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_9c252bf0f9e011d96f8bdba9fe4b5cea"><model name="BmTxBufferInstanceRom" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_27e66215dbb73274980f23a83e908220"><source xmi:idref="EAID_eb382f6741e6d8e76e2374d6eb6f9f1a"><model name="BmTxBufferRam" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_bdac7bdeb331402ce6cbd145cfdd1475"><model name="BmTxBufferRom" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_389ec2329ebb58d9c97a654fd4d0054b"><source xmi:idref="EAID_5cbb97fb371632541269f6ff4078943e"><model name="DeferredEventCacheRam" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_aa860125f8f09286c32f05e4ab4216de"><model name="DeferredEventCacheRom" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_09c2a96c50e61301404c89c690dca93e"><source xmi:idref="EAID_311e80b8512b8aeb9ca972b84b6f37aa"><model name="FmFifoInstanceRam" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_da88b58e5fb558917c7fd7ec0171ae7e"><model name="FmFifoInstanceRom" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_25d4c0fc875a048916c92f6aa57cd6cc"><source xmi:idref="EAID_092c720570d1fab8a4f6e70d7ae36e74"><model name="FmFifoRam" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_9c46d1fe89ae9eec3af3b950598487b5"><model name="FmFifoRom" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_e17308053a459173118d8f44efbda509"><source xmi:idref="EAID_4bc8c2a4364454950d3fc9185ff25be7"><model name="InterfaceFifoQueueRam" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_87c915abc9c5e5ab37d73383fe969ff5"><model name="InterfaceFifoQueueRom" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_fc31071b000ed7779054c4bf1ce1bf9a"><source xmi:idref="EAID_92d7a62d66b9994ec2d11f1b3f4ce883"><model name="McQBufferRam" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_b6ed023068eb5bfcc230f923085ee524"><model name="McQBufferRom" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_7fe1e50996a14b222a4536ad2f53e617"><source xmi:idref="EAID_1acf8bf38bb57644a40632c9dc8897a2"><model name="RmBufferedIfPropertiesRam" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_45ca483d77f9f774d388da7e12e67d82"><model name="RmBufferedIfPropertiesRom" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_4e51cd79316f880384574bf22f807ac8"><source xmi:idref="EAID_752bb397ec1390a1388d1f37405ae8ea"><model name="RmBufferedTpPropertiesRam" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_d09498d242e10c71f0708b3ae5d8c375"><model name="RmBufferedTpPropertiesRom" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_3b0353fecd8b0154696372df3a5d0c9a"><source xmi:idref="EAID_4032c8e6f38d693bf11cf65dcf952897"><model name="RmDestRpgRam" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_2f3d49b44ebddb55363af41561fd35a6"><model name="RmDestRpgRom" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_82f8a45cf31057d0b5e8c64b14f7ec5d"><source xmi:idref="EAID_0af0b029b9c2a583f13c3e45c8e8ad4d"><model name="RpgRam" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_b3f09b00ce1f1c35f117db4c29088408"><model name="RpgRom" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_173bfe5e6f8ff3d96881966a06169184"><source xmi:idref="EAID_33747b7b74f574b918549d35b4ae5f52"><model name="SingleBufferRam" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_1094eee4236ecf35e177133cdc6264b3"><model name="SingleBufferRom" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector></connectors><diagrams><diagram xmi:id="EAID_e3c49fc2a193c49d90882b104f2d3ffd"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ded26787e1860b9df6fe48031a13de3c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9a3c28261ab10588d5ccfa9cbeafd680"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ee5e575cfac73ac43d3903e69fae8a14"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0db42fb60965aab1ba79b0c9efe39c1e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9c252bf0f9e011d96f8bdba9fe4b5cea"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_32bc990093dbc442e5d8fa45eaf4d1a1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bdac7bdeb331402ce6cbd145cfdd1475"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ec0d2f2de9131a0514bed5feca04d43d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7cb59b5c256a487b5623bd8aea51493b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_aa860125f8f09286c32f05e4ab4216de"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e1635822b42104099e8bb45a717ff5ce"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_246116c073f4615685b6f46a07d525a0"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d827c005628dcab444ad7aa4f1e0d2cc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3e7d7362e953e514d949c7d9741b59d0"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4e2b5f7b225d9aba7f5e35f03e30567d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1f152a97dc071ceae1b8714a07a7e8b2"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_da88b58e5fb558917c7fd7ec0171ae7e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a51d1d44b8917d03c57bf2654d4f420b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9c46d1fe89ae9eec3af3b950598487b5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5c99108e3e9b854a84b37159c113c2cc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_203431f398328fdb2be8ae423e4ffb32"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_381078806b92be9b5ebea9b9249e2119"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4283cbcb2908aea6b13a851f4aa6800d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_87c915abc9c5e5ab37d73383fe969ff5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_dde9ae6da66ec000836378abc808800f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_dbfc1d3d3c864d29850188977333d0e7"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_27cab97755d70143c1b3835c40367b6c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_46776a359f0d8191bf9118726beaaa45"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_13f3650a29a87b1d43673ff16283f11a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ae5c10aa1043e67b3af61df1001b416a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b6ed023068eb5bfcc230f923085ee524"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_db579899ff759aad4a339099f1ba4d39"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_cf385f1f71ca109b9ac36c596c7c61ea"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3d252f71416ab2a602c1bce513b67228"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_774427d189f31852220c1b4dd51e1281"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c565d59b2faa599cd32de993a7ad0395"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_45ca483d77f9f774d388da7e12e67d82"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1141591835a460e3ddd8762eb7bb4656"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_30b7b60297f136c318438d156a8a1628"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d4a01b9398db8aac32dc4ac7b064b45a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d09498d242e10c71f0708b3ae5d8c375"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e708b7011b2cf0808542f044575a06a5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_47af84a5d0862ebb47b8ab0049f666a5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ccf0bc21f51ed7ace275d1e4819fa593"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b18dbb9cb2897f0102d9ef1823c5a348"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9f9308775d173cba59331c14c4e75502"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2f3d49b44ebddb55363af41561fd35a6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_cdc216229b7360bc51755b20ddf7eb99"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c753b8257c1177208da906dd979acb10"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a0678b6714d58c3b11593fb3cf3b08f3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d37632adbb87d7325b9dd837c9799189"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b817ce47c5d143afb8a543a75d96e23d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_16f555da32f9c7812530d8a55e5c8f1d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_17147fe9242479a57ceb30e2ddaf36c4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_10b8694d3938011217de0e5feddccf51"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f24a81a1159ad505c0f15bd48f6ed0c8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_03ff7484cf384ced46945c7cb1b929a4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1d8e04381a4a0122c954c32f57b97bde"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a641c59fe8166729cb8802c6604abd76"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_673d91c2ca235e07ae1891881989f6ae"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0b9ef36cf91d071d3d45824c92bd4d32"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_08311f9ba92be36886736868a3058b92"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d440f12708a8de182e19d5b933c303bb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_10d0028e2d69ffb1cc13eec66d424cb4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b9bf3e8b9ef1f6e24ebf80d00ab8bfcb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b3f09b00ce1f1c35f117db4c29088408"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_92124387d4aa35f25e5841956e34ca11"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d421a039dd4e3a8d28499a2a0f2e29f4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_138f302e49968a3d5bd92e5e64e5cd88"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d9627ad31d5900738e3e2056ef30dee9"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2d16a949452f3e8f68f8eef2a24a09f3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7cfe881fe1a15754e2fbbf6e450e2093"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_19888a57d3bbef12b32188523d597738"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_dcce7b235763a1c36ece8cf6a85639fc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1094eee4236ecf35e177133cdc6264b3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_525b7ab2f81edb7bc6fb104126a7f73b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b1c21dac2beb755c4ff8bb3240c2626c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f7167d18e42032c0592a926f22c0459c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d222d5322d686549e7faf96327a0aa3e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2cc39486d56b4aaf136e471a8e41e33b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_62e202d303415e5837b85ea3b1c60826"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e8e52e63b342f336eec5bf1b991f2ceb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1aec27c02ae14791f061fd328d15fc4b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b0583b6cb4aafcb48816df39b7690f48"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_47078b0e11aa41e2aeb4782b635611bb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f2bf0e63135beaf024228fbeb019b7ca"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_70e88e757e7bdabd5b0eede309663127"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9461196ea56528836d05cc4b6730392c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_20fe2765a4cb6729b3d9eeb5499ce645"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d85bfe1b8ef2c3471b98fe16c0a0e018"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f8ce52d2fb8abb4d11c6a29adce01a97"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_42a373c3f3ecb29036fb4b04d2901971"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9686d56487a779926aeb755d1cbafc48"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bfc8afc358392da397211dce03a7a5ed"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_eb784c8f5da6a5e3f35b2dd2468414d1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0aee36c9c32633d8d1571d62ad40c603"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a6237fd892fdd21f52996c491159f780"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6f116412ea5a2891211117bd63b53a88"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b3a1326635e6a7974eb5b3dca062a699"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8e6e97b93852e63959c970b9de0aeedf"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4834f1560178265da7c0099da85c0656"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_156adba42865deea679d50265197677d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e81304b62134424597e9f179a169e422"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_eb382f6741e6d8e76e2374d6eb6f9f1a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_27e66215dbb73274980f23a83e908220"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5731838bfe297a8735ca0072f323f7d1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5cbb97fb371632541269f6ff4078943e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_389ec2329ebb58d9c97a654fd4d0054b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1969ac3e9bae6b0e03744b6ff04754a6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_311e80b8512b8aeb9ca972b84b6f37aa"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_09c2a96c50e61301404c89c690dca93e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_092c720570d1fab8a4f6e70d7ae36e74"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_25d4c0fc875a048916c92f6aa57cd6cc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bc2a2eef55c5b6c390d689695c6c065d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4bc8c2a4364454950d3fc9185ff25be7"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e17308053a459173118d8f44efbda509"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_92d7a62d66b9994ec2d11f1b3f4ce883"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_fc31071b000ed7779054c4bf1ce1bf9a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e6e65771c0ea0439fca00096f15349f6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1acf8bf38bb57644a40632c9dc8897a2"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7fe1e50996a14b222a4536ad2f53e617"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_752bb397ec1390a1388d1f37405ae8ea"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4e51cd79316f880384574bf22f807ac8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4032c8e6f38d693bf11cf65dcf952897"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3b0353fecd8b0154696372df3a5d0c9a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7b6fe4565a5cc2aeee984f88620ef58d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ba1a1d2ef2705f55b52c59a0d0c07899"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0af0b029b9c2a583f13c3e45c8e8ad4d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_82f8a45cf31057d0b5e8c64b14f7ec5d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_33747b7b74f574b918549d35b4ae5f52"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_173bfe5e6f8ff3d96881966a06169184"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_daa8b6f32a84c888e6a6c7b47d00f205"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d9aa7d92241e260643b1358dffacb978"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7a67a4fb4e819882670e169718e8787b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8296be806b543bbc3f27974973f54c80"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d5a103e5a6cc0475c1ec3dc8e1dbd083"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_93bb0c9746ce5aeb167c4035c538984f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_72292af1e228249cb8ec3f91aa7c30ec"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0b3bb79055f64547aff63ad1c9af60aa"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b3a723d4b5036b9c36e217c7db701110"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_98ead75a4e649dab381a9d3c11c70cf3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2b5839f711308fa5f78c48a05d874fb7"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_045c79f73ab47665923017576a0b630d"/></elements><model owner="EAPK24f17efde2f9b83f8ef89424f9933ee3" package="EAPK24f17efde2f9b83f8ef89424f9933ee3"/><properties name="All Data and Relations" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_4b2429e264757b99c7a2da56038cdf48"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ded26787e1860b9df6fe48031a13de3c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9a3c28261ab10588d5ccfa9cbeafd680"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ee5e575cfac73ac43d3903e69fae8a14"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0db42fb60965aab1ba79b0c9efe39c1e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9c252bf0f9e011d96f8bdba9fe4b5cea"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_32bc990093dbc442e5d8fa45eaf4d1a1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bdac7bdeb331402ce6cbd145cfdd1475"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ec0d2f2de9131a0514bed5feca04d43d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7cb59b5c256a487b5623bd8aea51493b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_aa860125f8f09286c32f05e4ab4216de"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e1635822b42104099e8bb45a717ff5ce"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_246116c073f4615685b6f46a07d525a0"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d827c005628dcab444ad7aa4f1e0d2cc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3e7d7362e953e514d949c7d9741b59d0"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4e2b5f7b225d9aba7f5e35f03e30567d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1f152a97dc071ceae1b8714a07a7e8b2"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_da88b58e5fb558917c7fd7ec0171ae7e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a51d1d44b8917d03c57bf2654d4f420b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9c46d1fe89ae9eec3af3b950598487b5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5c99108e3e9b854a84b37159c113c2cc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_203431f398328fdb2be8ae423e4ffb32"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_381078806b92be9b5ebea9b9249e2119"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4283cbcb2908aea6b13a851f4aa6800d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_87c915abc9c5e5ab37d73383fe969ff5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_dde9ae6da66ec000836378abc808800f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_dbfc1d3d3c864d29850188977333d0e7"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_27cab97755d70143c1b3835c40367b6c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_46776a359f0d8191bf9118726beaaa45"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_13f3650a29a87b1d43673ff16283f11a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ae5c10aa1043e67b3af61df1001b416a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b6ed023068eb5bfcc230f923085ee524"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_db579899ff759aad4a339099f1ba4d39"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_cf385f1f71ca109b9ac36c596c7c61ea"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3d252f71416ab2a602c1bce513b67228"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_774427d189f31852220c1b4dd51e1281"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c565d59b2faa599cd32de993a7ad0395"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_45ca483d77f9f774d388da7e12e67d82"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1141591835a460e3ddd8762eb7bb4656"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_30b7b60297f136c318438d156a8a1628"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d4a01b9398db8aac32dc4ac7b064b45a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d09498d242e10c71f0708b3ae5d8c375"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e708b7011b2cf0808542f044575a06a5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_47af84a5d0862ebb47b8ab0049f666a5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ccf0bc21f51ed7ace275d1e4819fa593"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b18dbb9cb2897f0102d9ef1823c5a348"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9f9308775d173cba59331c14c4e75502"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2f3d49b44ebddb55363af41561fd35a6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_cdc216229b7360bc51755b20ddf7eb99"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c753b8257c1177208da906dd979acb10"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a0678b6714d58c3b11593fb3cf3b08f3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d37632adbb87d7325b9dd837c9799189"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b817ce47c5d143afb8a543a75d96e23d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_16f555da32f9c7812530d8a55e5c8f1d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_17147fe9242479a57ceb30e2ddaf36c4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_10b8694d3938011217de0e5feddccf51"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f24a81a1159ad505c0f15bd48f6ed0c8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_03ff7484cf384ced46945c7cb1b929a4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1d8e04381a4a0122c954c32f57b97bde"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a641c59fe8166729cb8802c6604abd76"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_673d91c2ca235e07ae1891881989f6ae"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0b9ef36cf91d071d3d45824c92bd4d32"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_08311f9ba92be36886736868a3058b92"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d440f12708a8de182e19d5b933c303bb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_10d0028e2d69ffb1cc13eec66d424cb4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b9bf3e8b9ef1f6e24ebf80d00ab8bfcb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b3f09b00ce1f1c35f117db4c29088408"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_92124387d4aa35f25e5841956e34ca11"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d421a039dd4e3a8d28499a2a0f2e29f4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_138f302e49968a3d5bd92e5e64e5cd88"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d9627ad31d5900738e3e2056ef30dee9"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2d16a949452f3e8f68f8eef2a24a09f3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7cfe881fe1a15754e2fbbf6e450e2093"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_19888a57d3bbef12b32188523d597738"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_dcce7b235763a1c36ece8cf6a85639fc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1094eee4236ecf35e177133cdc6264b3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_525b7ab2f81edb7bc6fb104126a7f73b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b1c21dac2beb755c4ff8bb3240c2626c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f7167d18e42032c0592a926f22c0459c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d222d5322d686549e7faf96327a0aa3e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2cc39486d56b4aaf136e471a8e41e33b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_62e202d303415e5837b85ea3b1c60826"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e8e52e63b342f336eec5bf1b991f2ceb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1aec27c02ae14791f061fd328d15fc4b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b0583b6cb4aafcb48816df39b7690f48"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_47078b0e11aa41e2aeb4782b635611bb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f2bf0e63135beaf024228fbeb019b7ca"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_70e88e757e7bdabd5b0eede309663127"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9461196ea56528836d05cc4b6730392c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_20fe2765a4cb6729b3d9eeb5499ce645"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d85bfe1b8ef2c3471b98fe16c0a0e018"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f8ce52d2fb8abb4d11c6a29adce01a97"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_42a373c3f3ecb29036fb4b04d2901971"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9686d56487a779926aeb755d1cbafc48"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bfc8afc358392da397211dce03a7a5ed"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_eb784c8f5da6a5e3f35b2dd2468414d1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0aee36c9c32633d8d1571d62ad40c603"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a6237fd892fdd21f52996c491159f780"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6f116412ea5a2891211117bd63b53a88"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b3a1326635e6a7974eb5b3dca062a699"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8e6e97b93852e63959c970b9de0aeedf"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4834f1560178265da7c0099da85c0656"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7a67a4fb4e819882670e169718e8787b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8296be806b543bbc3f27974973f54c80"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d5a103e5a6cc0475c1ec3dc8e1dbd083"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_93bb0c9746ce5aeb167c4035c538984f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_72292af1e228249cb8ec3f91aa7c30ec"/></elements><model owner="EAPK24f17efde2f9b83f8ef89424f9933ee3" package="EAPK24f17efde2f9b83f8ef89424f9933ee3"/><properties name="CONST with Struct Elements" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_8d02fa7f1dc7ea5501522cf2dd1fda54"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ded26787e1860b9df6fe48031a13de3c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9a3c28261ab10588d5ccfa9cbeafd680"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ee5e575cfac73ac43d3903e69fae8a14"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0db42fb60965aab1ba79b0c9efe39c1e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9c252bf0f9e011d96f8bdba9fe4b5cea"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_32bc990093dbc442e5d8fa45eaf4d1a1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bdac7bdeb331402ce6cbd145cfdd1475"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ec0d2f2de9131a0514bed5feca04d43d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7cb59b5c256a487b5623bd8aea51493b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_aa860125f8f09286c32f05e4ab4216de"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e1635822b42104099e8bb45a717ff5ce"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_246116c073f4615685b6f46a07d525a0"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d827c005628dcab444ad7aa4f1e0d2cc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3e7d7362e953e514d949c7d9741b59d0"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4e2b5f7b225d9aba7f5e35f03e30567d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1f152a97dc071ceae1b8714a07a7e8b2"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_da88b58e5fb558917c7fd7ec0171ae7e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a51d1d44b8917d03c57bf2654d4f420b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9c46d1fe89ae9eec3af3b950598487b5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5c99108e3e9b854a84b37159c113c2cc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_203431f398328fdb2be8ae423e4ffb32"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_381078806b92be9b5ebea9b9249e2119"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4283cbcb2908aea6b13a851f4aa6800d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_87c915abc9c5e5ab37d73383fe969ff5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_dde9ae6da66ec000836378abc808800f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_dbfc1d3d3c864d29850188977333d0e7"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_27cab97755d70143c1b3835c40367b6c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_46776a359f0d8191bf9118726beaaa45"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_13f3650a29a87b1d43673ff16283f11a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ae5c10aa1043e67b3af61df1001b416a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b6ed023068eb5bfcc230f923085ee524"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_db579899ff759aad4a339099f1ba4d39"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_cf385f1f71ca109b9ac36c596c7c61ea"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3d252f71416ab2a602c1bce513b67228"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_774427d189f31852220c1b4dd51e1281"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c565d59b2faa599cd32de993a7ad0395"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_45ca483d77f9f774d388da7e12e67d82"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1141591835a460e3ddd8762eb7bb4656"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_30b7b60297f136c318438d156a8a1628"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d4a01b9398db8aac32dc4ac7b064b45a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d09498d242e10c71f0708b3ae5d8c375"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e708b7011b2cf0808542f044575a06a5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_47af84a5d0862ebb47b8ab0049f666a5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ccf0bc21f51ed7ace275d1e4819fa593"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b18dbb9cb2897f0102d9ef1823c5a348"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9f9308775d173cba59331c14c4e75502"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2f3d49b44ebddb55363af41561fd35a6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_cdc216229b7360bc51755b20ddf7eb99"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c753b8257c1177208da906dd979acb10"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a0678b6714d58c3b11593fb3cf3b08f3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d37632adbb87d7325b9dd837c9799189"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b817ce47c5d143afb8a543a75d96e23d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_16f555da32f9c7812530d8a55e5c8f1d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_17147fe9242479a57ceb30e2ddaf36c4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_10b8694d3938011217de0e5feddccf51"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f24a81a1159ad505c0f15bd48f6ed0c8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_03ff7484cf384ced46945c7cb1b929a4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1d8e04381a4a0122c954c32f57b97bde"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a641c59fe8166729cb8802c6604abd76"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_673d91c2ca235e07ae1891881989f6ae"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0b9ef36cf91d071d3d45824c92bd4d32"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_08311f9ba92be36886736868a3058b92"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d440f12708a8de182e19d5b933c303bb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_10d0028e2d69ffb1cc13eec66d424cb4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b9bf3e8b9ef1f6e24ebf80d00ab8bfcb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b3f09b00ce1f1c35f117db4c29088408"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_92124387d4aa35f25e5841956e34ca11"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d421a039dd4e3a8d28499a2a0f2e29f4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_138f302e49968a3d5bd92e5e64e5cd88"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d9627ad31d5900738e3e2056ef30dee9"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2d16a949452f3e8f68f8eef2a24a09f3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7cfe881fe1a15754e2fbbf6e450e2093"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_19888a57d3bbef12b32188523d597738"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_dcce7b235763a1c36ece8cf6a85639fc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1094eee4236ecf35e177133cdc6264b3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_525b7ab2f81edb7bc6fb104126a7f73b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b1c21dac2beb755c4ff8bb3240c2626c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f7167d18e42032c0592a926f22c0459c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d222d5322d686549e7faf96327a0aa3e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2cc39486d56b4aaf136e471a8e41e33b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_62e202d303415e5837b85ea3b1c60826"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e8e52e63b342f336eec5bf1b991f2ceb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1aec27c02ae14791f061fd328d15fc4b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b0583b6cb4aafcb48816df39b7690f48"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_47078b0e11aa41e2aeb4782b635611bb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f2bf0e63135beaf024228fbeb019b7ca"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_70e88e757e7bdabd5b0eede309663127"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9461196ea56528836d05cc4b6730392c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_20fe2765a4cb6729b3d9eeb5499ce645"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d85bfe1b8ef2c3471b98fe16c0a0e018"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f8ce52d2fb8abb4d11c6a29adce01a97"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_42a373c3f3ecb29036fb4b04d2901971"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9686d56487a779926aeb755d1cbafc48"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bfc8afc358392da397211dce03a7a5ed"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_eb784c8f5da6a5e3f35b2dd2468414d1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0aee36c9c32633d8d1571d62ad40c603"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a6237fd892fdd21f52996c491159f780"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6f116412ea5a2891211117bd63b53a88"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b3a1326635e6a7974eb5b3dca062a699"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8e6e97b93852e63959c970b9de0aeedf"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4834f1560178265da7c0099da85c0656"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7a67a4fb4e819882670e169718e8787b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8296be806b543bbc3f27974973f54c80"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d5a103e5a6cc0475c1ec3dc8e1dbd083"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_93bb0c9746ce5aeb167c4035c538984f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_72292af1e228249cb8ec3f91aa7c30ec"/></elements><model owner="EAPK24f17efde2f9b83f8ef89424f9933ee3" package="EAPK24f17efde2f9b83f8ef89424f9933ee3"/><properties name="CONST without Struct Elements" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_5f7703db87891bdbcb860bea66c5f19c"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_156adba42865deea679d50265197677d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9c252bf0f9e011d96f8bdba9fe4b5cea"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_eb382f6741e6d8e76e2374d6eb6f9f1a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bdac7bdeb331402ce6cbd145cfdd1475"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5731838bfe297a8735ca0072f323f7d1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5cbb97fb371632541269f6ff4078943e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_aa860125f8f09286c32f05e4ab4216de"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1969ac3e9bae6b0e03744b6ff04754a6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_311e80b8512b8aeb9ca972b84b6f37aa"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_da88b58e5fb558917c7fd7ec0171ae7e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_092c720570d1fab8a4f6e70d7ae36e74"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9c46d1fe89ae9eec3af3b950598487b5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bc2a2eef55c5b6c390d689695c6c065d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4bc8c2a4364454950d3fc9185ff25be7"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_87c915abc9c5e5ab37d73383fe969ff5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_92d7a62d66b9994ec2d11f1b3f4ce883"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b6ed023068eb5bfcc230f923085ee524"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e6e65771c0ea0439fca00096f15349f6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1acf8bf38bb57644a40632c9dc8897a2"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_45ca483d77f9f774d388da7e12e67d82"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_752bb397ec1390a1388d1f37405ae8ea"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d09498d242e10c71f0708b3ae5d8c375"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4032c8e6f38d693bf11cf65dcf952897"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2f3d49b44ebddb55363af41561fd35a6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7b6fe4565a5cc2aeee984f88620ef58d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ba1a1d2ef2705f55b52c59a0d0c07899"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0af0b029b9c2a583f13c3e45c8e8ad4d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b3f09b00ce1f1c35f117db4c29088408"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_33747b7b74f574b918549d35b4ae5f52"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1094eee4236ecf35e177133cdc6264b3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_daa8b6f32a84c888e6a6c7b47d00f205"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d9aa7d92241e260643b1358dffacb978"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0b3bb79055f64547aff63ad1c9af60aa"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b3a723d4b5036b9c36e217c7db701110"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_98ead75a4e649dab381a9d3c11c70cf3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2b5839f711308fa5f78c48a05d874fb7"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_045c79f73ab47665923017576a0b630d"/></elements><model owner="EAPK24f17efde2f9b83f8ef89424f9933ee3" package="EAPK24f17efde2f9b83f8ef89424f9933ee3"/><properties name="VAR and Relations" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_d86d2d746b47c6eaf5aaa8320f9b1a28"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8296be806b543bbc3f27974973f54c80"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0b3bb79055f64547aff63ad1c9af60aa"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b3a723d4b5036b9c36e217c7db701110"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_98ead75a4e649dab381a9d3c11c70cf3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2b5839f711308fa5f78c48a05d874fb7"/></elements><model owner="EAPK24f17efde2f9b83f8ef89424f9933ee3" package="EAPK24f17efde2f9b83f8ef89424f9933ee3"/><properties name="Data Accessed by Adress Operator" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_81decd53dde8ac802a865472c7d89c4a"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ded26787e1860b9df6fe48031a13de3c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_10d0028e2d69ffb1cc13eec66d424cb4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_138f302e49968a3d5bd92e5e64e5cd88"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2d16a949452f3e8f68f8eef2a24a09f3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_19888a57d3bbef12b32188523d597738"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_eb784c8f5da6a5e3f35b2dd2468414d1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6f116412ea5a2891211117bd63b53a88"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8e6e97b93852e63959c970b9de0aeedf"/></elements><model owner="EAPK24f17efde2f9b83f8ef89424f9933ee3" package="EAPK24f17efde2f9b83f8ef89424f9933ee3"/><properties name="Data Accessed by Interface Handles" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_d142fbff7ecbc9e7bd8dc5653f9d50bb"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1f152a97dc071ceae1b8714a07a7e8b2"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_cf385f1f71ca109b9ac36c596c7c61ea"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c565d59b2faa599cd32de993a7ad0395"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_045c79f73ab47665923017576a0b630d"/></elements><model owner="EAPK24f17efde2f9b83f8ef89424f9933ee3" package="EAPK24f17efde2f9b83f8ef89424f9933ee3"/><properties name="Max Precompile Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_51d048105bd130cdc10b0a31204eb916"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7a67a4fb4e819882670e169718e8787b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d5a103e5a6cc0475c1ec3dc8e1dbd083"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_93bb0c9746ce5aeb167c4035c538984f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_72292af1e228249cb8ec3f91aa7c30ec"/></elements><model owner="EAPK24f17efde2f9b83f8ef89424f9933ee3" package="EAPK24f17efde2f9b83f8ef89424f9933ee3"/><properties name="Max Linktime Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_11060f69e0f2c5262d4b410d3243c948"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ded26787e1860b9df6fe48031a13de3c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ee5e575cfac73ac43d3903e69fae8a14"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9c252bf0f9e011d96f8bdba9fe4b5cea"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bdac7bdeb331402ce6cbd145cfdd1475"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_aa860125f8f09286c32f05e4ab4216de"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_246116c073f4615685b6f46a07d525a0"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_da88b58e5fb558917c7fd7ec0171ae7e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9c46d1fe89ae9eec3af3b950598487b5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4283cbcb2908aea6b13a851f4aa6800d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_87c915abc9c5e5ab37d73383fe969ff5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_46776a359f0d8191bf9118726beaaa45"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b6ed023068eb5bfcc230f923085ee524"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_45ca483d77f9f774d388da7e12e67d82"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d09498d242e10c71f0708b3ae5d8c375"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_47af84a5d0862ebb47b8ab0049f666a5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2f3d49b44ebddb55363af41561fd35a6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c753b8257c1177208da906dd979acb10"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1d8e04381a4a0122c954c32f57b97bde"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_10d0028e2d69ffb1cc13eec66d424cb4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b3f09b00ce1f1c35f117db4c29088408"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_138f302e49968a3d5bd92e5e64e5cd88"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2d16a949452f3e8f68f8eef2a24a09f3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_19888a57d3bbef12b32188523d597738"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1094eee4236ecf35e177133cdc6264b3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d222d5322d686549e7faf96327a0aa3e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1aec27c02ae14791f061fd328d15fc4b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f2bf0e63135beaf024228fbeb019b7ca"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_70e88e757e7bdabd5b0eede309663127"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9461196ea56528836d05cc4b6730392c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_42a373c3f3ecb29036fb4b04d2901971"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_eb784c8f5da6a5e3f35b2dd2468414d1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6f116412ea5a2891211117bd63b53a88"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8e6e97b93852e63959c970b9de0aeedf"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_156adba42865deea679d50265197677d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_eb382f6741e6d8e76e2374d6eb6f9f1a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5731838bfe297a8735ca0072f323f7d1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5cbb97fb371632541269f6ff4078943e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1969ac3e9bae6b0e03744b6ff04754a6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_311e80b8512b8aeb9ca972b84b6f37aa"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_092c720570d1fab8a4f6e70d7ae36e74"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bc2a2eef55c5b6c390d689695c6c065d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4bc8c2a4364454950d3fc9185ff25be7"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_92d7a62d66b9994ec2d11f1b3f4ce883"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e6e65771c0ea0439fca00096f15349f6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1acf8bf38bb57644a40632c9dc8897a2"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_752bb397ec1390a1388d1f37405ae8ea"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4032c8e6f38d693bf11cf65dcf952897"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7b6fe4565a5cc2aeee984f88620ef58d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ba1a1d2ef2705f55b52c59a0d0c07899"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0af0b029b9c2a583f13c3e45c8e8ad4d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_33747b7b74f574b918549d35b4ae5f52"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_daa8b6f32a84c888e6a6c7b47d00f205"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d9aa7d92241e260643b1358dffacb978"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8296be806b543bbc3f27974973f54c80"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_cf7a45675a928b9a1174720d2d0459e7"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0b3bb79055f64547aff63ad1c9af60aa"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b3a723d4b5036b9c36e217c7db701110"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_98ead75a4e649dab381a9d3c11c70cf3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2b5839f711308fa5f78c48a05d874fb7"/></elements><model owner="EAPK24f17efde2f9b83f8ef89424f9933ee3" package="EAPK24f17efde2f9b83f8ef89424f9933ee3"/><properties name="Max Postbuild Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_940872ee134256a7db8056d222cc7fcc"><elements/><model owner="EAPK24f17efde2f9b83f8ef89424f9933ee3" package="EAPK24f17efde2f9b83f8ef89424f9933ee3"/><properties name="Calibration Lite Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_ec19cabebbdc7a3f484da67ec0b69e9f"><elements/><model owner="EAPK24f17efde2f9b83f8ef89424f9933ee3" package="EAPK24f17efde2f9b83f8ef89424f9933ee3"/><properties name="Sandbox with Details" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_41bec53fd9b0c37caa5d71f194398f41"><elements/><model owner="EAPK24f17efde2f9b83f8ef89424f9933ee3" package="EAPK24f17efde2f9b83f8ef89424f9933ee3"/><properties name="Sandbox without Details" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram></diagrams></xmi:Extension><uml:Model name="PduR Abstract Data Model" visibility="public" xmi:id="EAPK24f17efde2f9b83f8ef89424f9933ee3" xmi:type="uml:Package"><packagedElement name="ApplicationId2DestApplicationManagerRom:ConstStruct" visibility="public" xmi:id="EAID_ded26787e1860b9df6fe48031a13de3c" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_9a3c28261ab10588d5ccfa9cbeafd680" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst9a3c28261ab10588d5ccfa9cbeafd680" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_2a240cbe4677d5545dfec73a9327cc28" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_e9c4cecf97c735981ca206b2bcaccb6d" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_246116c073f4615685b6f46a07d525a0"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_9a3c28261ab10588d5ccfa9cbeafd680" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst9a3c28261ab10588d5ccfa9cbeafd680"/><memberEnd xmi:idref="EAID_src9a3c28261ab10588d5ccfa9cbeafd680"/><ownedEnd aggregation="none" association="EAID_9a3c28261ab10588d5ccfa9cbeafd680" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src9a3c28261ab10588d5ccfa9cbeafd680" xmi:type="uml:Property"><type xmi:idref="EAID_ded26787e1860b9df6fe48031a13de3c"/></ownedEnd></packagedElement><packagedElement name="BmTxBufferIndRom:ConstStruct" visibility="public" xmi:id="EAID_ee5e575cfac73ac43d3903e69fae8a14" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_0db42fb60965aab1ba79b0c9efe39c1e" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst0db42fb60965aab1ba79b0c9efe39c1e" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_589b47f1c8b3527183c557b5237d0fca" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_715e8e1456318f3e155936bca471e4e2" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_bdac7bdeb331402ce6cbd145cfdd1475"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_0db42fb60965aab1ba79b0c9efe39c1e" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst0db42fb60965aab1ba79b0c9efe39c1e"/><memberEnd xmi:idref="EAID_src0db42fb60965aab1ba79b0c9efe39c1e"/><ownedEnd aggregation="none" association="EAID_0db42fb60965aab1ba79b0c9efe39c1e" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src0db42fb60965aab1ba79b0c9efe39c1e" xmi:type="uml:Property"><type xmi:idref="EAID_ee5e575cfac73ac43d3903e69fae8a14"/></ownedEnd></packagedElement><packagedElement name="BmTxBufferInstanceRom:ConstStruct" visibility="public" xmi:id="EAID_9c252bf0f9e011d96f8bdba9fe4b5cea" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_32bc990093dbc442e5d8fa45eaf4d1a1" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst32bc990093dbc442e5d8fa45eaf4d1a1" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_055965fac7ed468886dbc015d5b08ab3" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_3b76632b9a61b27d53c7deae02221c04" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_bdac7bdeb331402ce6cbd145cfdd1475"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_32bc990093dbc442e5d8fa45eaf4d1a1" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst32bc990093dbc442e5d8fa45eaf4d1a1"/><memberEnd xmi:idref="EAID_src32bc990093dbc442e5d8fa45eaf4d1a1"/><ownedEnd aggregation="none" association="EAID_32bc990093dbc442e5d8fa45eaf4d1a1" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src32bc990093dbc442e5d8fa45eaf4d1a1" xmi:type="uml:Property"><type xmi:idref="EAID_9c252bf0f9e011d96f8bdba9fe4b5cea"/></ownedEnd></packagedElement><packagedElement name="BmTxBufferRom:ConstStruct" visibility="public" xmi:id="EAID_bdac7bdeb331402ce6cbd145cfdd1475" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_ec0d2f2de9131a0514bed5feca04d43d" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstec0d2f2de9131a0514bed5feca04d43d" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_cd8fc65cb72a2ab9743c581485177dfa" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_5baf2c8b61b7cd2e528ebd5be07d9ee3" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_0b3bb79055f64547aff63ad1c9af60aa"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_7cb59b5c256a487b5623bd8aea51493b" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst7cb59b5c256a487b5623bd8aea51493b" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_e6cb18fb03969d69a0183b8f54551745" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_9f601de7d72af5060caea86872cdb6cd" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_9c252bf0f9e011d96f8bdba9fe4b5cea"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_ec0d2f2de9131a0514bed5feca04d43d" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstec0d2f2de9131a0514bed5feca04d43d"/><memberEnd xmi:idref="EAID_srcec0d2f2de9131a0514bed5feca04d43d"/><ownedEnd aggregation="none" association="EAID_ec0d2f2de9131a0514bed5feca04d43d" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcec0d2f2de9131a0514bed5feca04d43d" xmi:type="uml:Property"><type xmi:idref="EAID_bdac7bdeb331402ce6cbd145cfdd1475"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_7cb59b5c256a487b5623bd8aea51493b" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst7cb59b5c256a487b5623bd8aea51493b"/><memberEnd xmi:idref="EAID_src7cb59b5c256a487b5623bd8aea51493b"/><ownedEnd aggregation="none" association="EAID_7cb59b5c256a487b5623bd8aea51493b" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src7cb59b5c256a487b5623bd8aea51493b" xmi:type="uml:Property"><type xmi:idref="EAID_bdac7bdeb331402ce6cbd145cfdd1475"/></ownedEnd></packagedElement><packagedElement name="DeferredEventCacheRom:ConstStruct" visibility="public" xmi:id="EAID_aa860125f8f09286c32f05e4ab4216de" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_e1635822b42104099e8bb45a717ff5ce" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dste1635822b42104099e8bb45a717ff5ce" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_d50c1124c86a53089cc3323a033e37c5" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_bf3e2c76e889fe9a857d20880745c992" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_5731838bfe297a8735ca0072f323f7d1"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_e1635822b42104099e8bb45a717ff5ce" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dste1635822b42104099e8bb45a717ff5ce"/><memberEnd xmi:idref="EAID_srce1635822b42104099e8bb45a717ff5ce"/><ownedEnd aggregation="none" association="EAID_e1635822b42104099e8bb45a717ff5ce" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srce1635822b42104099e8bb45a717ff5ce" xmi:type="uml:Property"><type xmi:idref="EAID_aa860125f8f09286c32f05e4ab4216de"/></ownedEnd></packagedElement><packagedElement name="DestApplicationManagerRom:ConstStruct" visibility="public" xmi:id="EAID_246116c073f4615685b6f46a07d525a0" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_d827c005628dcab444ad7aa4f1e0d2cc" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstd827c005628dcab444ad7aa4f1e0d2cc" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_8f370de4373621dd7404ca1d7a2e25a8" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_9c74d75415dd9764cb7af3582c663884" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_624c032ecf7e8bbf68ce1e04f4bbb430"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_3e7d7362e953e514d949c7d9741b59d0" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst3e7d7362e953e514d949c7d9741b59d0" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_5ae58e965029f9bb60d068b362326b6a" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_035201e81e9b41653e25672046a251ce" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_cf385f1f71ca109b9ac36c596c7c61ea"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_4e2b5f7b225d9aba7f5e35f03e30567d" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst4e2b5f7b225d9aba7f5e35f03e30567d" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_7865f3273a1cf9b687e7838843563907" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_0bce2d5b86a251d33e4034e12bc42e60" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_42a373c3f3ecb29036fb4b04d2901971"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_d827c005628dcab444ad7aa4f1e0d2cc" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstd827c005628dcab444ad7aa4f1e0d2cc"/><memberEnd xmi:idref="EAID_srcd827c005628dcab444ad7aa4f1e0d2cc"/><ownedEnd aggregation="none" association="EAID_d827c005628dcab444ad7aa4f1e0d2cc" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcd827c005628dcab444ad7aa4f1e0d2cc" xmi:type="uml:Property"><type xmi:idref="EAID_246116c073f4615685b6f46a07d525a0"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_3e7d7362e953e514d949c7d9741b59d0" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst3e7d7362e953e514d949c7d9741b59d0"/><memberEnd xmi:idref="EAID_src3e7d7362e953e514d949c7d9741b59d0"/><ownedEnd aggregation="none" association="EAID_3e7d7362e953e514d949c7d9741b59d0" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src3e7d7362e953e514d949c7d9741b59d0" xmi:type="uml:Property"><type xmi:idref="EAID_246116c073f4615685b6f46a07d525a0"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_4e2b5f7b225d9aba7f5e35f03e30567d" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst4e2b5f7b225d9aba7f5e35f03e30567d"/><memberEnd xmi:idref="EAID_src4e2b5f7b225d9aba7f5e35f03e30567d"/><ownedEnd aggregation="none" association="EAID_4e2b5f7b225d9aba7f5e35f03e30567d" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src4e2b5f7b225d9aba7f5e35f03e30567d" xmi:type="uml:Property"><type xmi:idref="EAID_246116c073f4615685b6f46a07d525a0"/></ownedEnd></packagedElement><packagedElement name="ExclusiveAreaRom:ConstStruct" visibility="public" xmi:id="EAID_1f152a97dc071ceae1b8714a07a7e8b2" xmi:type="uml:InstanceSpecification"/><packagedElement name="FmFifoInstanceRom:ConstStruct" visibility="public" xmi:id="EAID_da88b58e5fb558917c7fd7ec0171ae7e" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_a51d1d44b8917d03c57bf2654d4f420b" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dsta51d1d44b8917d03c57bf2654d4f420b" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_015a529ffe70fe4965dec1ee1d2528ed" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_71f35e477a14eccf08d63063e1a5ade2" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_9c46d1fe89ae9eec3af3b950598487b5"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_a51d1d44b8917d03c57bf2654d4f420b" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dsta51d1d44b8917d03c57bf2654d4f420b"/><memberEnd xmi:idref="EAID_srca51d1d44b8917d03c57bf2654d4f420b"/><ownedEnd aggregation="none" association="EAID_a51d1d44b8917d03c57bf2654d4f420b" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srca51d1d44b8917d03c57bf2654d4f420b" xmi:type="uml:Property"><type xmi:idref="EAID_da88b58e5fb558917c7fd7ec0171ae7e"/></ownedEnd></packagedElement><packagedElement name="FmFifoRom:ConstStruct" visibility="public" xmi:id="EAID_9c46d1fe89ae9eec3af3b950598487b5" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_5c99108e3e9b854a84b37159c113c2cc" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst5c99108e3e9b854a84b37159c113c2cc" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_fd561186a7163f503a34a21db76ccab6" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_e158b1abb265e15db2d2c63f97e440d0" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_1969ac3e9bae6b0e03744b6ff04754a6"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_203431f398328fdb2be8ae423e4ffb32" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst203431f398328fdb2be8ae423e4ffb32" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_a8a728facae10de6fa78b6c04aefa66d" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_eedd6e67d75db700f3f9329d5a930e88" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_ee5e575cfac73ac43d3903e69fae8a14"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_381078806b92be9b5ebea9b9249e2119" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst381078806b92be9b5ebea9b9249e2119" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_3f800fc9268dfeb5e9d8616919396470" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_52adca2a6d87a712eb53889c4ab90cea" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_46776a359f0d8191bf9118726beaaa45"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_5c99108e3e9b854a84b37159c113c2cc" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst5c99108e3e9b854a84b37159c113c2cc"/><memberEnd xmi:idref="EAID_src5c99108e3e9b854a84b37159c113c2cc"/><ownedEnd aggregation="none" association="EAID_5c99108e3e9b854a84b37159c113c2cc" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src5c99108e3e9b854a84b37159c113c2cc" xmi:type="uml:Property"><type xmi:idref="EAID_9c46d1fe89ae9eec3af3b950598487b5"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_203431f398328fdb2be8ae423e4ffb32" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst203431f398328fdb2be8ae423e4ffb32"/><memberEnd xmi:idref="EAID_src203431f398328fdb2be8ae423e4ffb32"/><ownedEnd aggregation="none" association="EAID_203431f398328fdb2be8ae423e4ffb32" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src203431f398328fdb2be8ae423e4ffb32" xmi:type="uml:Property"><type xmi:idref="EAID_9c46d1fe89ae9eec3af3b950598487b5"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_381078806b92be9b5ebea9b9249e2119" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst381078806b92be9b5ebea9b9249e2119"/><memberEnd xmi:idref="EAID_src381078806b92be9b5ebea9b9249e2119"/><ownedEnd aggregation="none" association="EAID_381078806b92be9b5ebea9b9249e2119" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src381078806b92be9b5ebea9b9249e2119" xmi:type="uml:Property"><type xmi:idref="EAID_9c46d1fe89ae9eec3af3b950598487b5"/></ownedEnd></packagedElement><packagedElement name="GeneralPropertiesRom:ConstStruct" visibility="public" xmi:id="EAID_4283cbcb2908aea6b13a851f4aa6800d" xmi:type="uml:InstanceSpecification"/><packagedElement name="InterfaceFifoQueueRom:ConstStruct" visibility="public" xmi:id="EAID_87c915abc9c5e5ab37d73383fe969ff5" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_dde9ae6da66ec000836378abc808800f" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstdde9ae6da66ec000836378abc808800f" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_7485f015e34fcf1d4d54f8249446b020" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_2d7291541e7a4cc83906ab1e41c7b7af" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_b3a723d4b5036b9c36e217c7db701110"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_dbfc1d3d3c864d29850188977333d0e7" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstdbfc1d3d3c864d29850188977333d0e7" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_917d4d59ad7114f6635970948c25dcab" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_677dce14f2f6e31ea1cb74745a0a1152" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_bc2a2eef55c5b6c390d689695c6c065d"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_27cab97755d70143c1b3835c40367b6c" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst27cab97755d70143c1b3835c40367b6c" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_f565e58608496b0f8459873b89bdf3e7" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_9157b4ce4e7ff61603aab61faf200976" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_dde9ae6da66ec000836378abc808800f" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstdde9ae6da66ec000836378abc808800f"/><memberEnd xmi:idref="EAID_srcdde9ae6da66ec000836378abc808800f"/><ownedEnd aggregation="none" association="EAID_dde9ae6da66ec000836378abc808800f" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcdde9ae6da66ec000836378abc808800f" xmi:type="uml:Property"><type xmi:idref="EAID_87c915abc9c5e5ab37d73383fe969ff5"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_dbfc1d3d3c864d29850188977333d0e7" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstdbfc1d3d3c864d29850188977333d0e7"/><memberEnd xmi:idref="EAID_srcdbfc1d3d3c864d29850188977333d0e7"/><ownedEnd aggregation="none" association="EAID_dbfc1d3d3c864d29850188977333d0e7" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcdbfc1d3d3c864d29850188977333d0e7" xmi:type="uml:Property"><type xmi:idref="EAID_87c915abc9c5e5ab37d73383fe969ff5"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_27cab97755d70143c1b3835c40367b6c" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst27cab97755d70143c1b3835c40367b6c"/><memberEnd xmi:idref="EAID_src27cab97755d70143c1b3835c40367b6c"/><ownedEnd aggregation="none" association="EAID_27cab97755d70143c1b3835c40367b6c" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src27cab97755d70143c1b3835c40367b6c" xmi:type="uml:Property"><type xmi:idref="EAID_87c915abc9c5e5ab37d73383fe969ff5"/></ownedEnd></packagedElement><packagedElement name="LockRom:ConstStruct" visibility="public" xmi:id="EAID_46776a359f0d8191bf9118726beaaa45" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_13f3650a29a87b1d43673ff16283f11a" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst13f3650a29a87b1d43673ff16283f11a" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_092e563418d11f70b4f1cdcb6af6e787" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_32794766dde6093a8781ca8cfdd63913" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_1f152a97dc071ceae1b8714a07a7e8b2"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_ae5c10aa1043e67b3af61df1001b416a" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" name="SpinlockRam" visibility="public" xmi:id="EAID_dstae5c10aa1043e67b3af61df1001b416a" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_264a4fc4a80f8cf01825e1478b3b1f11" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_504d1645bffae1b44feddb5829ac691e" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_d9aa7d92241e260643b1358dffacb978"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_13f3650a29a87b1d43673ff16283f11a" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst13f3650a29a87b1d43673ff16283f11a"/><memberEnd xmi:idref="EAID_src13f3650a29a87b1d43673ff16283f11a"/><ownedEnd aggregation="none" association="EAID_13f3650a29a87b1d43673ff16283f11a" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src13f3650a29a87b1d43673ff16283f11a" xmi:type="uml:Property"><type xmi:idref="EAID_46776a359f0d8191bf9118726beaaa45"/></ownedEnd></packagedElement><packagedElement name="multi indirection" visibility="public" xmi:id="EAID_ae5c10aa1043e67b3af61df1001b416a" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstae5c10aa1043e67b3af61df1001b416a"/><memberEnd xmi:idref="EAID_srcae5c10aa1043e67b3af61df1001b416a"/><ownedEnd aggregation="none" association="EAID_ae5c10aa1043e67b3af61df1001b416a" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcae5c10aa1043e67b3af61df1001b416a" xmi:type="uml:Property"><type xmi:idref="EAID_46776a359f0d8191bf9118726beaaa45"/></ownedEnd></packagedElement><packagedElement name="McQBufferRom:ConstStruct" visibility="public" xmi:id="EAID_b6ed023068eb5bfcc230f923085ee524" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_db579899ff759aad4a339099f1ba4d39" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstdb579899ff759aad4a339099f1ba4d39" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_84abc492c062dedb2e54d98e362796a0" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_1dacdd2353475c69b7fef07b09f2cc84" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_98ead75a4e649dab381a9d3c11c70cf3"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_db579899ff759aad4a339099f1ba4d39" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstdb579899ff759aad4a339099f1ba4d39"/><memberEnd xmi:idref="EAID_srcdb579899ff759aad4a339099f1ba4d39"/><ownedEnd aggregation="none" association="EAID_db579899ff759aad4a339099f1ba4d39" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcdb579899ff759aad4a339099f1ba4d39" xmi:type="uml:Property"><type xmi:idref="EAID_b6ed023068eb5bfcc230f923085ee524"/></ownedEnd></packagedElement><packagedElement name="MmRom:ConstStruct" visibility="public" xmi:id="EAID_cf385f1f71ca109b9ac36c596c7c61ea" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_3d252f71416ab2a602c1bce513b67228" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst3d252f71416ab2a602c1bce513b67228" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_14488902659de452b0022647d9ae60db" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_be0f40b676e254c7b45d69f385851ac3" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_c753b8257c1177208da906dd979acb10"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_774427d189f31852220c1b4dd51e1281" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst774427d189f31852220c1b4dd51e1281" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_45fd30751d18bc280256cc199345b394" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_24bea7eac7dbcad76de8b9c661ebe3e4" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_246116c073f4615685b6f46a07d525a0"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_3d252f71416ab2a602c1bce513b67228" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst3d252f71416ab2a602c1bce513b67228"/><memberEnd xmi:idref="EAID_src3d252f71416ab2a602c1bce513b67228"/><ownedEnd aggregation="none" association="EAID_3d252f71416ab2a602c1bce513b67228" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src3d252f71416ab2a602c1bce513b67228" xmi:type="uml:Property"><type xmi:idref="EAID_cf385f1f71ca109b9ac36c596c7c61ea"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_774427d189f31852220c1b4dd51e1281" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst774427d189f31852220c1b4dd51e1281"/><memberEnd xmi:idref="EAID_src774427d189f31852220c1b4dd51e1281"/><ownedEnd aggregation="none" association="EAID_774427d189f31852220c1b4dd51e1281" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src774427d189f31852220c1b4dd51e1281" xmi:type="uml:Property"><type xmi:idref="EAID_cf385f1f71ca109b9ac36c596c7c61ea"/></ownedEnd></packagedElement><packagedElement name="QueueFctPtrRom:ConstStruct" visibility="public" xmi:id="EAID_c565d59b2faa599cd32de993a7ad0395" xmi:type="uml:InstanceSpecification"/><packagedElement name="RmBufferedIfPropertiesRom:ConstStruct" visibility="public" xmi:id="EAID_45ca483d77f9f774d388da7e12e67d82" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_1141591835a460e3ddd8762eb7bb4656" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst1141591835a460e3ddd8762eb7bb4656" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_91ad540b29b43113cc1b9cc8cb796ac5" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_f8bf2fe4623733d32b37478062d4935f" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_1094eee4236ecf35e177133cdc6264b3"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_30b7b60297f136c318438d156a8a1628" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst30b7b60297f136c318438d156a8a1628" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_da3a278b9d644a38626c3ca13dc0ec86" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_21e42a907a5ccb02dc9443e276a29ded" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_87c915abc9c5e5ab37d73383fe969ff5"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_d4a01b9398db8aac32dc4ac7b064b45a" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstd4a01b9398db8aac32dc4ac7b064b45a" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_416d9f7b958cf4df765ec099a624f362" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_a9d48d659887f2f0949fffc094180fdc" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_c565d59b2faa599cd32de993a7ad0395"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_1141591835a460e3ddd8762eb7bb4656" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst1141591835a460e3ddd8762eb7bb4656"/><memberEnd xmi:idref="EAID_src1141591835a460e3ddd8762eb7bb4656"/><ownedEnd aggregation="none" association="EAID_1141591835a460e3ddd8762eb7bb4656" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src1141591835a460e3ddd8762eb7bb4656" xmi:type="uml:Property"><type xmi:idref="EAID_45ca483d77f9f774d388da7e12e67d82"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_30b7b60297f136c318438d156a8a1628" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst30b7b60297f136c318438d156a8a1628"/><memberEnd xmi:idref="EAID_src30b7b60297f136c318438d156a8a1628"/><ownedEnd aggregation="none" association="EAID_30b7b60297f136c318438d156a8a1628" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src30b7b60297f136c318438d156a8a1628" xmi:type="uml:Property"><type xmi:idref="EAID_45ca483d77f9f774d388da7e12e67d82"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_d4a01b9398db8aac32dc4ac7b064b45a" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstd4a01b9398db8aac32dc4ac7b064b45a"/><memberEnd xmi:idref="EAID_srcd4a01b9398db8aac32dc4ac7b064b45a"/><ownedEnd aggregation="none" association="EAID_d4a01b9398db8aac32dc4ac7b064b45a" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcd4a01b9398db8aac32dc4ac7b064b45a" xmi:type="uml:Property"><type xmi:idref="EAID_45ca483d77f9f774d388da7e12e67d82"/></ownedEnd></packagedElement><packagedElement name="RmBufferedTpPropertiesRom:ConstStruct" visibility="public" xmi:id="EAID_d09498d242e10c71f0708b3ae5d8c375" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_e708b7011b2cf0808542f044575a06a5" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dste708b7011b2cf0808542f044575a06a5" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_d2d6804baf3a7d6dea3a1228c6038087" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_3d6c73fb58cd4e0bad0e992741fb33d5" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_9c46d1fe89ae9eec3af3b950598487b5"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_e708b7011b2cf0808542f044575a06a5" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dste708b7011b2cf0808542f044575a06a5"/><memberEnd xmi:idref="EAID_srce708b7011b2cf0808542f044575a06a5"/><ownedEnd aggregation="none" association="EAID_e708b7011b2cf0808542f044575a06a5" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srce708b7011b2cf0808542f044575a06a5" xmi:type="uml:Property"><type xmi:idref="EAID_d09498d242e10c71f0708b3ae5d8c375"/></ownedEnd></packagedElement><packagedElement name="RmDestRom:ConstStruct" visibility="public" xmi:id="EAID_47af84a5d0862ebb47b8ab0049f666a5" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_ccf0bc21f51ed7ace275d1e4819fa593" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" name="RmDestRpgRom" visibility="public" xmi:id="EAID_dstccf0bc21f51ed7ace275d1e4819fa593" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_dd2f34d6e71031b2f8816b5e06b4d268" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_b03b0414d4916f0eb3853f2d29a08812" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_2f3d49b44ebddb55363af41561fd35a6"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_b18dbb9cb2897f0102d9ef1823c5a348" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstb18dbb9cb2897f0102d9ef1823c5a348" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_f394bc87520b35f3b13ed0547466eaba" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_14812a9aea838ec3d21a1cf555f7b667" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_c753b8257c1177208da906dd979acb10"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_9f9308775d173cba59331c14c4e75502" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst9f9308775d173cba59331c14c4e75502" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_66743c43f49333a061af8e79ad676249" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_90037f10dcc9b031d5b3bf0174e28f56" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"/></ownedAttribute></packagedElement><packagedElement name="multi indirection" visibility="public" xmi:id="EAID_ccf0bc21f51ed7ace275d1e4819fa593" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstccf0bc21f51ed7ace275d1e4819fa593"/><memberEnd xmi:idref="EAID_srcccf0bc21f51ed7ace275d1e4819fa593"/><ownedEnd aggregation="none" association="EAID_ccf0bc21f51ed7ace275d1e4819fa593" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcccf0bc21f51ed7ace275d1e4819fa593" xmi:type="uml:Property"><type xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_b18dbb9cb2897f0102d9ef1823c5a348" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstb18dbb9cb2897f0102d9ef1823c5a348"/><memberEnd xmi:idref="EAID_srcb18dbb9cb2897f0102d9ef1823c5a348"/><ownedEnd aggregation="none" association="EAID_b18dbb9cb2897f0102d9ef1823c5a348" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcb18dbb9cb2897f0102d9ef1823c5a348" xmi:type="uml:Property"><type xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_9f9308775d173cba59331c14c4e75502" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst9f9308775d173cba59331c14c4e75502"/><memberEnd xmi:idref="EAID_src9f9308775d173cba59331c14c4e75502"/><ownedEnd aggregation="none" association="EAID_9f9308775d173cba59331c14c4e75502" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src9f9308775d173cba59331c14c4e75502" xmi:type="uml:Property"><type xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"/></ownedEnd></packagedElement><packagedElement name="RmDestRpgRom:ConstStruct" visibility="public" xmi:id="EAID_2f3d49b44ebddb55363af41561fd35a6" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_cdc216229b7360bc51755b20ddf7eb99" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstcdc216229b7360bc51755b20ddf7eb99" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_f12d3935664c15905bc8f6d5e2023808" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_6cfae3336adf68927124854bba65d184" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_cdc216229b7360bc51755b20ddf7eb99" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstcdc216229b7360bc51755b20ddf7eb99"/><memberEnd xmi:idref="EAID_srccdc216229b7360bc51755b20ddf7eb99"/><ownedEnd aggregation="none" association="EAID_cdc216229b7360bc51755b20ddf7eb99" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srccdc216229b7360bc51755b20ddf7eb99" xmi:type="uml:Property"><type xmi:idref="EAID_2f3d49b44ebddb55363af41561fd35a6"/></ownedEnd></packagedElement><packagedElement name="RmGDestRom:ConstStruct" visibility="public" xmi:id="EAID_c753b8257c1177208da906dd979acb10" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_a0678b6714d58c3b11593fb3cf3b08f3" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" name="FmFifoInstanceRom" visibility="public" xmi:id="EAID_dsta0678b6714d58c3b11593fb3cf3b08f3" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_407f1bb43bd5612451ab20d768274ac9" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_11416dd265e4df92da5ed3bfa2ae47a2" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_da88b58e5fb558917c7fd7ec0171ae7e"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_d37632adbb87d7325b9dd837c9799189" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" name="SmGDestRom" visibility="public" xmi:id="EAID_dstd37632adbb87d7325b9dd837c9799189" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_d16a7f81c1379a9d27e70eada02c5799" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_982943045235f23587c317e5a3f9130b" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_1aec27c02ae14791f061fd328d15fc4b"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_b817ce47c5d143afb8a543a75d96e23d" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstb817ce47c5d143afb8a543a75d96e23d" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_8eaaa4dcad6e08116f7605e9ab497783" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_574f6aeb3f83597b48df5ef7b6a9db03" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_16f555da32f9c7812530d8a55e5c8f1d" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst16f555da32f9c7812530d8a55e5c8f1d" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_1d88ae3afed764c0d9dfe5ffeece2502" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_19140916ec90e2396ad5c6bf3824dfd0" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_cf385f1f71ca109b9ac36c596c7c61ea"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_17147fe9242479a57ceb30e2ddaf36c4" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" name="RmBufferedIfPropertiesRom" visibility="public" xmi:id="EAID_dst17147fe9242479a57ceb30e2ddaf36c4" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_b867182b701daa47eaf204584f4f65dc" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_ed5d7db1d0e71daa12f5a10b8e430415" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_45ca483d77f9f774d388da7e12e67d82"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_10b8694d3938011217de0e5feddccf51" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" name="RmGDestTpTxStateRam" visibility="public" xmi:id="EAID_dst10b8694d3938011217de0e5feddccf51" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_b9757d91863b5a1588d6c2fb5d3eb51a" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_dc12e953e7c32eba04351b95d23956c4" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_ba1a1d2ef2705f55b52c59a0d0c07899"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_f24a81a1159ad505c0f15bd48f6ed0c8" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" name="RmGDestNto1InfoRam" visibility="public" xmi:id="EAID_dstf24a81a1159ad505c0f15bd48f6ed0c8" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_3642692ac05cc91e52ff0ccbb847a0ac" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_2e8801997c3563a2717fc747fb419f01" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_7b6fe4565a5cc2aeee984f88620ef58d"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_03ff7484cf384ced46945c7cb1b929a4" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst03ff7484cf384ced46945c7cb1b929a4" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_c86eb8dd8cf317fbf90399df3317e140" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_2c45a8c3cd78c270af90844ec83c7ee2" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_46776a359f0d8191bf9118726beaaa45"/></ownedAttribute></packagedElement><packagedElement name="multi indirection" visibility="public" xmi:id="EAID_a0678b6714d58c3b11593fb3cf3b08f3" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dsta0678b6714d58c3b11593fb3cf3b08f3"/><memberEnd xmi:idref="EAID_srca0678b6714d58c3b11593fb3cf3b08f3"/><ownedEnd aggregation="none" association="EAID_a0678b6714d58c3b11593fb3cf3b08f3" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srca0678b6714d58c3b11593fb3cf3b08f3" xmi:type="uml:Property"><type xmi:idref="EAID_c753b8257c1177208da906dd979acb10"/></ownedEnd></packagedElement><packagedElement name="multi indirection" visibility="public" xmi:id="EAID_d37632adbb87d7325b9dd837c9799189" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstd37632adbb87d7325b9dd837c9799189"/><memberEnd xmi:idref="EAID_srcd37632adbb87d7325b9dd837c9799189"/><ownedEnd aggregation="none" association="EAID_d37632adbb87d7325b9dd837c9799189" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcd37632adbb87d7325b9dd837c9799189" xmi:type="uml:Property"><type xmi:idref="EAID_c753b8257c1177208da906dd979acb10"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_b817ce47c5d143afb8a543a75d96e23d" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstb817ce47c5d143afb8a543a75d96e23d"/><memberEnd xmi:idref="EAID_srcb817ce47c5d143afb8a543a75d96e23d"/><ownedEnd aggregation="none" association="EAID_b817ce47c5d143afb8a543a75d96e23d" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcb817ce47c5d143afb8a543a75d96e23d" xmi:type="uml:Property"><type xmi:idref="EAID_c753b8257c1177208da906dd979acb10"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_16f555da32f9c7812530d8a55e5c8f1d" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst16f555da32f9c7812530d8a55e5c8f1d"/><memberEnd xmi:idref="EAID_src16f555da32f9c7812530d8a55e5c8f1d"/><ownedEnd aggregation="none" association="EAID_16f555da32f9c7812530d8a55e5c8f1d" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src16f555da32f9c7812530d8a55e5c8f1d" xmi:type="uml:Property"><type xmi:idref="EAID_c753b8257c1177208da906dd979acb10"/></ownedEnd></packagedElement><packagedElement name="multi indirection" visibility="public" xmi:id="EAID_17147fe9242479a57ceb30e2ddaf36c4" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst17147fe9242479a57ceb30e2ddaf36c4"/><memberEnd xmi:idref="EAID_src17147fe9242479a57ceb30e2ddaf36c4"/><ownedEnd aggregation="none" association="EAID_17147fe9242479a57ceb30e2ddaf36c4" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src17147fe9242479a57ceb30e2ddaf36c4" xmi:type="uml:Property"><type xmi:idref="EAID_c753b8257c1177208da906dd979acb10"/></ownedEnd></packagedElement><packagedElement name="multi indirection" visibility="public" xmi:id="EAID_10b8694d3938011217de0e5feddccf51" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst10b8694d3938011217de0e5feddccf51"/><memberEnd xmi:idref="EAID_src10b8694d3938011217de0e5feddccf51"/><ownedEnd aggregation="none" association="EAID_10b8694d3938011217de0e5feddccf51" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src10b8694d3938011217de0e5feddccf51" xmi:type="uml:Property"><type xmi:idref="EAID_c753b8257c1177208da906dd979acb10"/></ownedEnd></packagedElement><packagedElement name="multi indirection" visibility="public" xmi:id="EAID_f24a81a1159ad505c0f15bd48f6ed0c8" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstf24a81a1159ad505c0f15bd48f6ed0c8"/><memberEnd xmi:idref="EAID_srcf24a81a1159ad505c0f15bd48f6ed0c8"/><ownedEnd aggregation="none" association="EAID_f24a81a1159ad505c0f15bd48f6ed0c8" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcf24a81a1159ad505c0f15bd48f6ed0c8" xmi:type="uml:Property"><type xmi:idref="EAID_c753b8257c1177208da906dd979acb10"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_03ff7484cf384ced46945c7cb1b929a4" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst03ff7484cf384ced46945c7cb1b929a4"/><memberEnd xmi:idref="EAID_src03ff7484cf384ced46945c7cb1b929a4"/><ownedEnd aggregation="none" association="EAID_03ff7484cf384ced46945c7cb1b929a4" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src03ff7484cf384ced46945c7cb1b929a4" xmi:type="uml:Property"><type xmi:idref="EAID_c753b8257c1177208da906dd979acb10"/></ownedEnd></packagedElement><packagedElement name="RmSrcRom:ConstStruct" visibility="public" xmi:id="EAID_1d8e04381a4a0122c954c32f57b97bde" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_a641c59fe8166729cb8802c6604abd76" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dsta641c59fe8166729cb8802c6604abd76" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_91977a9f00b837ddc6d4bd2a2540055f" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_3cbac4607e34cea184a0999407919958" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_673d91c2ca235e07ae1891881989f6ae" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" name="SmSrcRom" visibility="public" xmi:id="EAID_dst673d91c2ca235e07ae1891881989f6ae" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_6439ec8d27d05f18898d09005d8e2872" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_2a070a979ce68797a5ffcdae832e24a5" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_9461196ea56528836d05cc4b6730392c"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_0b9ef36cf91d071d3d45824c92bd4d32" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst0b9ef36cf91d071d3d45824c92bd4d32" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_6b87ad3eeb2d80253adb84bf47a5ec14" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_e04f93f633c52269370f26255166096d" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_cf385f1f71ca109b9ac36c596c7c61ea"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_08311f9ba92be36886736868a3058b92" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" name="RmBufferedTpPropertiesRom" visibility="public" xmi:id="EAID_dst08311f9ba92be36886736868a3058b92" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_c1f73b5b23bc2e91130f189637ae79e7" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_5b2ef336d5bde0d86bb1e7bf7773cff7" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_d09498d242e10c71f0708b3ae5d8c375"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_d440f12708a8de182e19d5b933c303bb" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstd440f12708a8de182e19d5b933c303bb" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_813a0528368ccefee4d7484e7f1e75ba" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_6872af714f89675148a5457ac64f8fb0" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_46776a359f0d8191bf9118726beaaa45"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_a641c59fe8166729cb8802c6604abd76" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dsta641c59fe8166729cb8802c6604abd76"/><memberEnd xmi:idref="EAID_srca641c59fe8166729cb8802c6604abd76"/><ownedEnd aggregation="none" association="EAID_a641c59fe8166729cb8802c6604abd76" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srca641c59fe8166729cb8802c6604abd76" xmi:type="uml:Property"><type xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"/></ownedEnd></packagedElement><packagedElement name="multi indirection" visibility="public" xmi:id="EAID_673d91c2ca235e07ae1891881989f6ae" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst673d91c2ca235e07ae1891881989f6ae"/><memberEnd xmi:idref="EAID_src673d91c2ca235e07ae1891881989f6ae"/><ownedEnd aggregation="none" association="EAID_673d91c2ca235e07ae1891881989f6ae" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src673d91c2ca235e07ae1891881989f6ae" xmi:type="uml:Property"><type xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_0b9ef36cf91d071d3d45824c92bd4d32" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst0b9ef36cf91d071d3d45824c92bd4d32"/><memberEnd xmi:idref="EAID_src0b9ef36cf91d071d3d45824c92bd4d32"/><ownedEnd aggregation="none" association="EAID_0b9ef36cf91d071d3d45824c92bd4d32" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src0b9ef36cf91d071d3d45824c92bd4d32" xmi:type="uml:Property"><type xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"/></ownedEnd></packagedElement><packagedElement name="multi indirection" visibility="public" xmi:id="EAID_08311f9ba92be36886736868a3058b92" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst08311f9ba92be36886736868a3058b92"/><memberEnd xmi:idref="EAID_src08311f9ba92be36886736868a3058b92"/><ownedEnd aggregation="none" association="EAID_08311f9ba92be36886736868a3058b92" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src08311f9ba92be36886736868a3058b92" xmi:type="uml:Property"><type xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_d440f12708a8de182e19d5b933c303bb" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstd440f12708a8de182e19d5b933c303bb"/><memberEnd xmi:idref="EAID_srcd440f12708a8de182e19d5b933c303bb"/><ownedEnd aggregation="none" association="EAID_d440f12708a8de182e19d5b933c303bb" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcd440f12708a8de182e19d5b933c303bb" xmi:type="uml:Property"><type xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"/></ownedEnd></packagedElement><packagedElement name="RpgExtIdRom:ConstStruct" visibility="public" xmi:id="EAID_10d0028e2d69ffb1cc13eec66d424cb4" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_b9bf3e8b9ef1f6e24ebf80d00ab8bfcb" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" name="RpgRom" visibility="public" xmi:id="EAID_dstb9bf3e8b9ef1f6e24ebf80d00ab8bfcb" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_c11cf35a0e2df4878b6f877215423c68" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_53c067beb9bfe6bd02d3dcdabdd958f7" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_b3f09b00ce1f1c35f117db4c29088408"/></ownedAttribute></packagedElement><packagedElement name="multi indirection" visibility="public" xmi:id="EAID_b9bf3e8b9ef1f6e24ebf80d00ab8bfcb" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstb9bf3e8b9ef1f6e24ebf80d00ab8bfcb"/><memberEnd xmi:idref="EAID_srcb9bf3e8b9ef1f6e24ebf80d00ab8bfcb"/><ownedEnd aggregation="none" association="EAID_b9bf3e8b9ef1f6e24ebf80d00ab8bfcb" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcb9bf3e8b9ef1f6e24ebf80d00ab8bfcb" xmi:type="uml:Property"><type xmi:idref="EAID_10d0028e2d69ffb1cc13eec66d424cb4"/></ownedEnd></packagedElement><packagedElement name="RpgRom:ConstStruct" visibility="public" xmi:id="EAID_b3f09b00ce1f1c35f117db4c29088408" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_92124387d4aa35f25e5841956e34ca11" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst92124387d4aa35f25e5841956e34ca11" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_b950c380165409df35abf9471210fae5" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_d2129c71c15e3c9ae02d71ac86ffedb2" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_76c65e39cb1673bcdf0b4c0aadafd675"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_d421a039dd4e3a8d28499a2a0f2e29f4" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstd421a039dd4e3a8d28499a2a0f2e29f4" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_41bd3aadd436e7656f5cd642802b7880" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_01e57142963be4bdb5405c186bafbc08" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_2f3d49b44ebddb55363af41561fd35a6"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_92124387d4aa35f25e5841956e34ca11" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst92124387d4aa35f25e5841956e34ca11"/><memberEnd xmi:idref="EAID_src92124387d4aa35f25e5841956e34ca11"/><ownedEnd aggregation="none" association="EAID_92124387d4aa35f25e5841956e34ca11" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src92124387d4aa35f25e5841956e34ca11" xmi:type="uml:Property"><type xmi:idref="EAID_b3f09b00ce1f1c35f117db4c29088408"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_d421a039dd4e3a8d28499a2a0f2e29f4" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstd421a039dd4e3a8d28499a2a0f2e29f4"/><memberEnd xmi:idref="EAID_srcd421a039dd4e3a8d28499a2a0f2e29f4"/><ownedEnd aggregation="none" association="EAID_d421a039dd4e3a8d28499a2a0f2e29f4" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcd421a039dd4e3a8d28499a2a0f2e29f4" xmi:type="uml:Property"><type xmi:idref="EAID_b3f09b00ce1f1c35f117db4c29088408"/></ownedEnd></packagedElement><packagedElement name="RxIf2Dest:ConstStruct" visibility="public" xmi:id="EAID_138f302e49968a3d5bd92e5e64e5cd88" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_d9627ad31d5900738e3e2056ef30dee9" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstd9627ad31d5900738e3e2056ef30dee9" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_258e15c8fcafddf86b03af5bd6522dc3" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_af9a5245da6c6663ec79a49d6221f980" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_d9627ad31d5900738e3e2056ef30dee9" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstd9627ad31d5900738e3e2056ef30dee9"/><memberEnd xmi:idref="EAID_srcd9627ad31d5900738e3e2056ef30dee9"/><ownedEnd aggregation="none" association="EAID_d9627ad31d5900738e3e2056ef30dee9" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcd9627ad31d5900738e3e2056ef30dee9" xmi:type="uml:Property"><type xmi:idref="EAID_138f302e49968a3d5bd92e5e64e5cd88"/></ownedEnd></packagedElement><packagedElement name="RxTp2Dest:ConstStruct" visibility="public" xmi:id="EAID_2d16a949452f3e8f68f8eef2a24a09f3" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_7cfe881fe1a15754e2fbbf6e450e2093" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst7cfe881fe1a15754e2fbbf6e450e2093" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_4c0283f947336b3dc08580508d34efb9" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_689671c7b99cfd021a64c0c4d79e4211" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_7cfe881fe1a15754e2fbbf6e450e2093" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst7cfe881fe1a15754e2fbbf6e450e2093"/><memberEnd xmi:idref="EAID_src7cfe881fe1a15754e2fbbf6e450e2093"/><ownedEnd aggregation="none" association="EAID_7cfe881fe1a15754e2fbbf6e450e2093" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src7cfe881fe1a15754e2fbbf6e450e2093" xmi:type="uml:Property"><type xmi:idref="EAID_2d16a949452f3e8f68f8eef2a24a09f3"/></ownedEnd></packagedElement><packagedElement name="RxTp2Src:ConstStruct" visibility="public" xmi:id="EAID_19888a57d3bbef12b32188523d597738" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_dcce7b235763a1c36ece8cf6a85639fc" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstdcce7b235763a1c36ece8cf6a85639fc" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_ae0f18e33024597ae56fcdfcfebed924" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_ac6cdb59f4addc299acd4793d2e40edc" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_dcce7b235763a1c36ece8cf6a85639fc" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstdcce7b235763a1c36ece8cf6a85639fc"/><memberEnd xmi:idref="EAID_srcdcce7b235763a1c36ece8cf6a85639fc"/><ownedEnd aggregation="none" association="EAID_dcce7b235763a1c36ece8cf6a85639fc" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcdcce7b235763a1c36ece8cf6a85639fc" xmi:type="uml:Property"><type xmi:idref="EAID_19888a57d3bbef12b32188523d597738"/></ownedEnd></packagedElement><packagedElement name="SingleBufferRom:ConstStruct" visibility="public" xmi:id="EAID_1094eee4236ecf35e177133cdc6264b3" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_525b7ab2f81edb7bc6fb104126a7f73b" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst525b7ab2f81edb7bc6fb104126a7f73b" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_3f3b8ea7816d984a3055a3f7c9c61f3d" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_91562780b2a9ededadafcd671ec1f9f9" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_2b5839f711308fa5f78c48a05d874fb7"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_b1c21dac2beb755c4ff8bb3240c2626c" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstb1c21dac2beb755c4ff8bb3240c2626c" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_0167f3c63c2db8a6c9acdebd95ed17b5" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_e2aca0cd416cc936d7b66c092f50aaff" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_8296be806b543bbc3f27974973f54c80"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_f7167d18e42032c0592a926f22c0459c" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstf7167d18e42032c0592a926f22c0459c" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_ac5993ccc8968965584fac752a9e0cd0" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_1aa70788b4549bb4cb723c09375af753" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_47af84a5d0862ebb47b8ab0049f666a5"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_525b7ab2f81edb7bc6fb104126a7f73b" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst525b7ab2f81edb7bc6fb104126a7f73b"/><memberEnd xmi:idref="EAID_src525b7ab2f81edb7bc6fb104126a7f73b"/><ownedEnd aggregation="none" association="EAID_525b7ab2f81edb7bc6fb104126a7f73b" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src525b7ab2f81edb7bc6fb104126a7f73b" xmi:type="uml:Property"><type xmi:idref="EAID_1094eee4236ecf35e177133cdc6264b3"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_b1c21dac2beb755c4ff8bb3240c2626c" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstb1c21dac2beb755c4ff8bb3240c2626c"/><memberEnd xmi:idref="EAID_srcb1c21dac2beb755c4ff8bb3240c2626c"/><ownedEnd aggregation="none" association="EAID_b1c21dac2beb755c4ff8bb3240c2626c" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcb1c21dac2beb755c4ff8bb3240c2626c" xmi:type="uml:Property"><type xmi:idref="EAID_1094eee4236ecf35e177133cdc6264b3"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_f7167d18e42032c0592a926f22c0459c" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstf7167d18e42032c0592a926f22c0459c"/><memberEnd xmi:idref="EAID_srcf7167d18e42032c0592a926f22c0459c"/><ownedEnd aggregation="none" association="EAID_f7167d18e42032c0592a926f22c0459c" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcf7167d18e42032c0592a926f22c0459c" xmi:type="uml:Property"><type xmi:idref="EAID_1094eee4236ecf35e177133cdc6264b3"/></ownedEnd></packagedElement><packagedElement name="SmDataPlaneRom:ConstStruct" visibility="public" xmi:id="EAID_d222d5322d686549e7faf96327a0aa3e" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_2cc39486d56b4aaf136e471a8e41e33b" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst2cc39486d56b4aaf136e471a8e41e33b" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_66eca3e78457f234eea0ccba928a8680" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_1bb04c6efa6d03862a6dd0d80e15c532" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_daa8b6f32a84c888e6a6c7b47d00f205"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_62e202d303415e5837b85ea3b1c60826" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst62e202d303415e5837b85ea3b1c60826" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_8013b8d4f9f888132c0b83311abc6a38" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_243ac0bf4f6f4b165f60771cbf1a315b" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_f2bf0e63135beaf024228fbeb019b7ca"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_e8e52e63b342f336eec5bf1b991f2ceb" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dste8e52e63b342f336eec5bf1b991f2ceb" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_6f634794d51c3cd51a3db1753d63b499" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_43cc711dbb51b63883f5516eacda74e1" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_70e88e757e7bdabd5b0eede309663127"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_2cc39486d56b4aaf136e471a8e41e33b" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst2cc39486d56b4aaf136e471a8e41e33b"/><memberEnd xmi:idref="EAID_src2cc39486d56b4aaf136e471a8e41e33b"/><ownedEnd aggregation="none" association="EAID_2cc39486d56b4aaf136e471a8e41e33b" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src2cc39486d56b4aaf136e471a8e41e33b" xmi:type="uml:Property"><type xmi:idref="EAID_d222d5322d686549e7faf96327a0aa3e"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_62e202d303415e5837b85ea3b1c60826" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst62e202d303415e5837b85ea3b1c60826"/><memberEnd xmi:idref="EAID_src62e202d303415e5837b85ea3b1c60826"/><ownedEnd aggregation="none" association="EAID_62e202d303415e5837b85ea3b1c60826" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src62e202d303415e5837b85ea3b1c60826" xmi:type="uml:Property"><type xmi:idref="EAID_d222d5322d686549e7faf96327a0aa3e"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_e8e52e63b342f336eec5bf1b991f2ceb" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dste8e52e63b342f336eec5bf1b991f2ceb"/><memberEnd xmi:idref="EAID_srce8e52e63b342f336eec5bf1b991f2ceb"/><ownedEnd aggregation="none" association="EAID_e8e52e63b342f336eec5bf1b991f2ceb" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srce8e52e63b342f336eec5bf1b991f2ceb" xmi:type="uml:Property"><type xmi:idref="EAID_d222d5322d686549e7faf96327a0aa3e"/></ownedEnd></packagedElement><packagedElement name="SmGDestRom:ConstStruct" visibility="public" xmi:id="EAID_1aec27c02ae14791f061fd328d15fc4b" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_b0583b6cb4aafcb48816df39b7690f48" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstb0583b6cb4aafcb48816df39b7690f48" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_76eb97515c5d789cb034991892d9468e" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_a6f0e95148c78a5289bfa00d9e815a3f" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_d222d5322d686549e7faf96327a0aa3e"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_47078b0e11aa41e2aeb4782b635611bb" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst47078b0e11aa41e2aeb4782b635611bb" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_8464709cd80e4601cfa62681b975b672" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_09adab6b64b3f78c00f88f4bcf9b57ad" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_d5a103e5a6cc0475c1ec3dc8e1dbd083"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_b0583b6cb4aafcb48816df39b7690f48" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstb0583b6cb4aafcb48816df39b7690f48"/><memberEnd xmi:idref="EAID_srcb0583b6cb4aafcb48816df39b7690f48"/><ownedEnd aggregation="none" association="EAID_b0583b6cb4aafcb48816df39b7690f48" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcb0583b6cb4aafcb48816df39b7690f48" xmi:type="uml:Property"><type xmi:idref="EAID_1aec27c02ae14791f061fd328d15fc4b"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_47078b0e11aa41e2aeb4782b635611bb" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst47078b0e11aa41e2aeb4782b635611bb"/><memberEnd xmi:idref="EAID_src47078b0e11aa41e2aeb4782b635611bb"/><ownedEnd aggregation="none" association="EAID_47078b0e11aa41e2aeb4782b635611bb" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src47078b0e11aa41e2aeb4782b635611bb" xmi:type="uml:Property"><type xmi:idref="EAID_1aec27c02ae14791f061fd328d15fc4b"/></ownedEnd></packagedElement><packagedElement name="SmLinearTaToSaCalculationStrategyRom:ConstStruct" visibility="public" xmi:id="EAID_f2bf0e63135beaf024228fbeb019b7ca" xmi:type="uml:InstanceSpecification"/><packagedElement name="SmSaTaFromMetaDataCalculationStrategyRom:ConstStruct" visibility="public" xmi:id="EAID_70e88e757e7bdabd5b0eede309663127" xmi:type="uml:InstanceSpecification"/><packagedElement name="SmSrcRom:ConstStruct" visibility="public" xmi:id="EAID_9461196ea56528836d05cc4b6730392c" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_20fe2765a4cb6729b3d9eeb5499ce645" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst20fe2765a4cb6729b3d9eeb5499ce645" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_0e197e8e228ef6937f809fed029770d9" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_0727ceed350d80a753e6fef56c46efab" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_d222d5322d686549e7faf96327a0aa3e"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_d85bfe1b8ef2c3471b98fe16c0a0e018" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstd85bfe1b8ef2c3471b98fe16c0a0e018" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_d17d00cbb4ed975fae85085044a01080" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_6efc82c5937e244c9aa39fddd31792ed" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_93bb0c9746ce5aeb167c4035c538984f"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_f8ce52d2fb8abb4d11c6a29adce01a97" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstf8ce52d2fb8abb4d11c6a29adce01a97" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_0b82fbc45d19e6882d72bde87c21082b" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_6021a802937f721d74854958a039ec58" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_72292af1e228249cb8ec3f91aa7c30ec"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_20fe2765a4cb6729b3d9eeb5499ce645" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst20fe2765a4cb6729b3d9eeb5499ce645"/><memberEnd xmi:idref="EAID_src20fe2765a4cb6729b3d9eeb5499ce645"/><ownedEnd aggregation="none" association="EAID_20fe2765a4cb6729b3d9eeb5499ce645" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src20fe2765a4cb6729b3d9eeb5499ce645" xmi:type="uml:Property"><type xmi:idref="EAID_9461196ea56528836d05cc4b6730392c"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_d85bfe1b8ef2c3471b98fe16c0a0e018" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstd85bfe1b8ef2c3471b98fe16c0a0e018"/><memberEnd xmi:idref="EAID_srcd85bfe1b8ef2c3471b98fe16c0a0e018"/><ownedEnd aggregation="none" association="EAID_d85bfe1b8ef2c3471b98fe16c0a0e018" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcd85bfe1b8ef2c3471b98fe16c0a0e018" xmi:type="uml:Property"><type xmi:idref="EAID_9461196ea56528836d05cc4b6730392c"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_f8ce52d2fb8abb4d11c6a29adce01a97" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstf8ce52d2fb8abb4d11c6a29adce01a97"/><memberEnd xmi:idref="EAID_srcf8ce52d2fb8abb4d11c6a29adce01a97"/><ownedEnd aggregation="none" association="EAID_f8ce52d2fb8abb4d11c6a29adce01a97" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcf8ce52d2fb8abb4d11c6a29adce01a97" xmi:type="uml:Property"><type xmi:idref="EAID_9461196ea56528836d05cc4b6730392c"/></ownedEnd></packagedElement><packagedElement name="SrcApplicationRom:ConstStruct" visibility="public" xmi:id="EAID_42a373c3f3ecb29036fb4b04d2901971" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_9686d56487a779926aeb755d1cbafc48" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" name="DeferredEventCacheRom" visibility="public" xmi:id="EAID_dst9686d56487a779926aeb755d1cbafc48" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_2a399148e940a5aae73f3937285c81f1" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_8f72b8a11d50505da67446262c7a5999" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_aa860125f8f09286c32f05e4ab4216de"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_bfc8afc358392da397211dce03a7a5ed" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" name="McQBufferRom" visibility="public" xmi:id="EAID_dstbfc8afc358392da397211dce03a7a5ed" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_3c48d5eb5a84d4d0be8e97c37c24d701" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_5de40e714a2f77853dbb2c63faaa43a0" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_b6ed023068eb5bfcc230f923085ee524"/></ownedAttribute></packagedElement><packagedElement name="multi indirection" visibility="public" xmi:id="EAID_9686d56487a779926aeb755d1cbafc48" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst9686d56487a779926aeb755d1cbafc48"/><memberEnd xmi:idref="EAID_src9686d56487a779926aeb755d1cbafc48"/><ownedEnd aggregation="none" association="EAID_9686d56487a779926aeb755d1cbafc48" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src9686d56487a779926aeb755d1cbafc48" xmi:type="uml:Property"><type xmi:idref="EAID_42a373c3f3ecb29036fb4b04d2901971"/></ownedEnd></packagedElement><packagedElement name="multi indirection" visibility="public" xmi:id="EAID_bfc8afc358392da397211dce03a7a5ed" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstbfc8afc358392da397211dce03a7a5ed"/><memberEnd xmi:idref="EAID_srcbfc8afc358392da397211dce03a7a5ed"/><ownedEnd aggregation="none" association="EAID_bfc8afc358392da397211dce03a7a5ed" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcbfc8afc358392da397211dce03a7a5ed" xmi:type="uml:Property"><type xmi:idref="EAID_42a373c3f3ecb29036fb4b04d2901971"/></ownedEnd></packagedElement><packagedElement name="Tx2Lo:ConstStruct" visibility="public" xmi:id="EAID_eb784c8f5da6a5e3f35b2dd2468414d1" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_0aee36c9c32633d8d1571d62ad40c603" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst0aee36c9c32633d8d1571d62ad40c603" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_60cd23b4c901d137a1ab0e41cbb4c963" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_3227c302e6469210e9ccf14655ebee11" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_1d8e04381a4a0122c954c32f57b97bde"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_a6237fd892fdd21f52996c491159f780" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dsta6237fd892fdd21f52996c491159f780" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_18f3ca8644d43fb6717dfce8042003cb" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_628559eff1f9ce8257a4006cd100f204" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_7a67a4fb4e819882670e169718e8787b"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_0aee36c9c32633d8d1571d62ad40c603" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst0aee36c9c32633d8d1571d62ad40c603"/><memberEnd xmi:idref="EAID_src0aee36c9c32633d8d1571d62ad40c603"/><ownedEnd aggregation="none" association="EAID_0aee36c9c32633d8d1571d62ad40c603" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src0aee36c9c32633d8d1571d62ad40c603" xmi:type="uml:Property"><type xmi:idref="EAID_eb784c8f5da6a5e3f35b2dd2468414d1"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_a6237fd892fdd21f52996c491159f780" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dsta6237fd892fdd21f52996c491159f780"/><memberEnd xmi:idref="EAID_srca6237fd892fdd21f52996c491159f780"/><ownedEnd aggregation="none" association="EAID_a6237fd892fdd21f52996c491159f780" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srca6237fd892fdd21f52996c491159f780" xmi:type="uml:Property"><type xmi:idref="EAID_eb784c8f5da6a5e3f35b2dd2468414d1"/></ownedEnd></packagedElement><packagedElement name="TxIf2Up:ConstStruct" visibility="public" xmi:id="EAID_6f116412ea5a2891211117bd63b53a88" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_b3a1326635e6a7974eb5b3dca062a699" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstb3a1326635e6a7974eb5b3dca062a699" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_103c8297bddd2cc690a93c677218612a" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_08ef7a00e00f2de90d1a6e66c442034a" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_c753b8257c1177208da906dd979acb10"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_b3a1326635e6a7974eb5b3dca062a699" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstb3a1326635e6a7974eb5b3dca062a699"/><memberEnd xmi:idref="EAID_srcb3a1326635e6a7974eb5b3dca062a699"/><ownedEnd aggregation="none" association="EAID_b3a1326635e6a7974eb5b3dca062a699" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcb3a1326635e6a7974eb5b3dca062a699" xmi:type="uml:Property"><type xmi:idref="EAID_6f116412ea5a2891211117bd63b53a88"/></ownedEnd></packagedElement><packagedElement name="TxTp2Src:ConstStruct" visibility="public" xmi:id="EAID_8e6e97b93852e63959c970b9de0aeedf" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_4834f1560178265da7c0099da85c0656" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst4834f1560178265da7c0099da85c0656" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_c3eed94b82dfb054c6798ec290ef85f9" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_7787e8eb691eecfdf6206bfe13846e51" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_c753b8257c1177208da906dd979acb10"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_4834f1560178265da7c0099da85c0656" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst4834f1560178265da7c0099da85c0656"/><memberEnd xmi:idref="EAID_src4834f1560178265da7c0099da85c0656"/><ownedEnd aggregation="none" association="EAID_4834f1560178265da7c0099da85c0656" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src4834f1560178265da7c0099da85c0656" xmi:type="uml:Property"><type xmi:idref="EAID_8e6e97b93852e63959c970b9de0aeedf"/></ownedEnd></packagedElement><packagedElement name="BmTxBufferInstanceRam:VarStruct" visibility="public" xmi:id="EAID_156adba42865deea679d50265197677d" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_e81304b62134424597e9f179a169e422" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dste81304b62134424597e9f179a169e422"/><ownedEnd aggregation="none" association="EAID_e81304b62134424597e9f179a169e422" visibility="public" xmi:id="EAID_dste81304b62134424597e9f179a169e422" xmi:type="uml:Property"><type xmi:idref="EAID_9c252bf0f9e011d96f8bdba9fe4b5cea"/></ownedEnd><memberEnd xmi:idref="EAID_srce81304b62134424597e9f179a169e422"/><ownedEnd aggregation="none" association="EAID_e81304b62134424597e9f179a169e422" visibility="public" xmi:id="EAID_srce81304b62134424597e9f179a169e422" xmi:type="uml:Property"><type xmi:idref="EAID_156adba42865deea679d50265197677d"/></ownedEnd></packagedElement><packagedElement name="BmTxBufferRam:VarStruct" visibility="public" xmi:id="EAID_eb382f6741e6d8e76e2374d6eb6f9f1a" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_27e66215dbb73274980f23a83e908220" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst27e66215dbb73274980f23a83e908220"/><ownedEnd aggregation="none" association="EAID_27e66215dbb73274980f23a83e908220" visibility="public" xmi:id="EAID_dst27e66215dbb73274980f23a83e908220" xmi:type="uml:Property"><type xmi:idref="EAID_bdac7bdeb331402ce6cbd145cfdd1475"/></ownedEnd><memberEnd xmi:idref="EAID_src27e66215dbb73274980f23a83e908220"/><ownedEnd aggregation="none" association="EAID_27e66215dbb73274980f23a83e908220" visibility="public" xmi:id="EAID_src27e66215dbb73274980f23a83e908220" xmi:type="uml:Property"><type xmi:idref="EAID_eb382f6741e6d8e76e2374d6eb6f9f1a"/></ownedEnd></packagedElement><packagedElement name="DeferredEventCacheArrayRam:VarStruct" visibility="public" xmi:id="EAID_5731838bfe297a8735ca0072f323f7d1" xmi:type="uml:InstanceSpecification"/><packagedElement name="DeferredEventCacheRam:VarStruct" visibility="public" xmi:id="EAID_5cbb97fb371632541269f6ff4078943e" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_389ec2329ebb58d9c97a654fd4d0054b" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst389ec2329ebb58d9c97a654fd4d0054b"/><ownedEnd aggregation="none" association="EAID_389ec2329ebb58d9c97a654fd4d0054b" visibility="public" xmi:id="EAID_dst389ec2329ebb58d9c97a654fd4d0054b" xmi:type="uml:Property"><type xmi:idref="EAID_aa860125f8f09286c32f05e4ab4216de"/></ownedEnd><memberEnd xmi:idref="EAID_src389ec2329ebb58d9c97a654fd4d0054b"/><ownedEnd aggregation="none" association="EAID_389ec2329ebb58d9c97a654fd4d0054b" visibility="public" xmi:id="EAID_src389ec2329ebb58d9c97a654fd4d0054b" xmi:type="uml:Property"><type xmi:idref="EAID_5cbb97fb371632541269f6ff4078943e"/></ownedEnd></packagedElement><packagedElement name="FmFifoElementRam:VarStruct" visibility="public" xmi:id="EAID_1969ac3e9bae6b0e03744b6ff04754a6" xmi:type="uml:InstanceSpecification"/><packagedElement name="FmFifoInstanceRam:VarStruct" visibility="public" xmi:id="EAID_311e80b8512b8aeb9ca972b84b6f37aa" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_09c2a96c50e61301404c89c690dca93e" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst09c2a96c50e61301404c89c690dca93e"/><ownedEnd aggregation="none" association="EAID_09c2a96c50e61301404c89c690dca93e" visibility="public" xmi:id="EAID_dst09c2a96c50e61301404c89c690dca93e" xmi:type="uml:Property"><type xmi:idref="EAID_da88b58e5fb558917c7fd7ec0171ae7e"/></ownedEnd><memberEnd xmi:idref="EAID_src09c2a96c50e61301404c89c690dca93e"/><ownedEnd aggregation="none" association="EAID_09c2a96c50e61301404c89c690dca93e" visibility="public" xmi:id="EAID_src09c2a96c50e61301404c89c690dca93e" xmi:type="uml:Property"><type xmi:idref="EAID_311e80b8512b8aeb9ca972b84b6f37aa"/></ownedEnd></packagedElement><packagedElement name="FmFifoRam:VarStruct" visibility="public" xmi:id="EAID_092c720570d1fab8a4f6e70d7ae36e74" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_25d4c0fc875a048916c92f6aa57cd6cc" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst25d4c0fc875a048916c92f6aa57cd6cc"/><ownedEnd aggregation="none" association="EAID_25d4c0fc875a048916c92f6aa57cd6cc" visibility="public" xmi:id="EAID_dst25d4c0fc875a048916c92f6aa57cd6cc" xmi:type="uml:Property"><type xmi:idref="EAID_9c46d1fe89ae9eec3af3b950598487b5"/></ownedEnd><memberEnd xmi:idref="EAID_src25d4c0fc875a048916c92f6aa57cd6cc"/><ownedEnd aggregation="none" association="EAID_25d4c0fc875a048916c92f6aa57cd6cc" visibility="public" xmi:id="EAID_src25d4c0fc875a048916c92f6aa57cd6cc" xmi:type="uml:Property"><type xmi:idref="EAID_092c720570d1fab8a4f6e70d7ae36e74"/></ownedEnd></packagedElement><packagedElement name="InterfaceFifoQueueElementRam:VarStruct" visibility="public" xmi:id="EAID_bc2a2eef55c5b6c390d689695c6c065d" xmi:type="uml:InstanceSpecification"/><packagedElement name="InterfaceFifoQueueRam:VarStruct" visibility="public" xmi:id="EAID_4bc8c2a4364454950d3fc9185ff25be7" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_e17308053a459173118d8f44efbda509" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dste17308053a459173118d8f44efbda509"/><ownedEnd aggregation="none" association="EAID_e17308053a459173118d8f44efbda509" visibility="public" xmi:id="EAID_dste17308053a459173118d8f44efbda509" xmi:type="uml:Property"><type xmi:idref="EAID_87c915abc9c5e5ab37d73383fe969ff5"/></ownedEnd><memberEnd xmi:idref="EAID_srce17308053a459173118d8f44efbda509"/><ownedEnd aggregation="none" association="EAID_e17308053a459173118d8f44efbda509" visibility="public" xmi:id="EAID_srce17308053a459173118d8f44efbda509" xmi:type="uml:Property"><type xmi:idref="EAID_4bc8c2a4364454950d3fc9185ff25be7"/></ownedEnd></packagedElement><packagedElement name="McQBufferRam:VarStruct" visibility="public" xmi:id="EAID_92d7a62d66b9994ec2d11f1b3f4ce883" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_fc31071b000ed7779054c4bf1ce1bf9a" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstfc31071b000ed7779054c4bf1ce1bf9a"/><ownedEnd aggregation="none" association="EAID_fc31071b000ed7779054c4bf1ce1bf9a" visibility="public" xmi:id="EAID_dstfc31071b000ed7779054c4bf1ce1bf9a" xmi:type="uml:Property"><type xmi:idref="EAID_b6ed023068eb5bfcc230f923085ee524"/></ownedEnd><memberEnd xmi:idref="EAID_srcfc31071b000ed7779054c4bf1ce1bf9a"/><ownedEnd aggregation="none" association="EAID_fc31071b000ed7779054c4bf1ce1bf9a" visibility="public" xmi:id="EAID_srcfc31071b000ed7779054c4bf1ce1bf9a" xmi:type="uml:Property"><type xmi:idref="EAID_92d7a62d66b9994ec2d11f1b3f4ce883"/></ownedEnd></packagedElement><packagedElement name="PartitionLookupTableRam:VarStruct" visibility="public" xmi:id="EAID_e6e65771c0ea0439fca00096f15349f6" xmi:type="uml:InstanceSpecification"/><packagedElement name="RmBufferedIfPropertiesRam:VarStruct" visibility="public" xmi:id="EAID_1acf8bf38bb57644a40632c9dc8897a2" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_7fe1e50996a14b222a4536ad2f53e617" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst7fe1e50996a14b222a4536ad2f53e617"/><ownedEnd aggregation="none" association="EAID_7fe1e50996a14b222a4536ad2f53e617" visibility="public" xmi:id="EAID_dst7fe1e50996a14b222a4536ad2f53e617" xmi:type="uml:Property"><type xmi:idref="EAID_45ca483d77f9f774d388da7e12e67d82"/></ownedEnd><memberEnd xmi:idref="EAID_src7fe1e50996a14b222a4536ad2f53e617"/><ownedEnd aggregation="none" association="EAID_7fe1e50996a14b222a4536ad2f53e617" visibility="public" xmi:id="EAID_src7fe1e50996a14b222a4536ad2f53e617" xmi:type="uml:Property"><type xmi:idref="EAID_1acf8bf38bb57644a40632c9dc8897a2"/></ownedEnd></packagedElement><packagedElement name="RmBufferedTpPropertiesRam:VarStruct" visibility="public" xmi:id="EAID_752bb397ec1390a1388d1f37405ae8ea" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_4e51cd79316f880384574bf22f807ac8" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst4e51cd79316f880384574bf22f807ac8"/><ownedEnd aggregation="none" association="EAID_4e51cd79316f880384574bf22f807ac8" visibility="public" xmi:id="EAID_dst4e51cd79316f880384574bf22f807ac8" xmi:type="uml:Property"><type xmi:idref="EAID_d09498d242e10c71f0708b3ae5d8c375"/></ownedEnd><memberEnd xmi:idref="EAID_src4e51cd79316f880384574bf22f807ac8"/><ownedEnd aggregation="none" association="EAID_4e51cd79316f880384574bf22f807ac8" visibility="public" xmi:id="EAID_src4e51cd79316f880384574bf22f807ac8" xmi:type="uml:Property"><type xmi:idref="EAID_752bb397ec1390a1388d1f37405ae8ea"/></ownedEnd></packagedElement><packagedElement name="RmDestRpgRam:VarStruct" visibility="public" xmi:id="EAID_4032c8e6f38d693bf11cf65dcf952897" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_3b0353fecd8b0154696372df3a5d0c9a" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst3b0353fecd8b0154696372df3a5d0c9a"/><ownedEnd aggregation="none" association="EAID_3b0353fecd8b0154696372df3a5d0c9a" visibility="public" xmi:id="EAID_dst3b0353fecd8b0154696372df3a5d0c9a" xmi:type="uml:Property"><type xmi:idref="EAID_2f3d49b44ebddb55363af41561fd35a6"/></ownedEnd><memberEnd xmi:idref="EAID_src3b0353fecd8b0154696372df3a5d0c9a"/><ownedEnd aggregation="none" association="EAID_3b0353fecd8b0154696372df3a5d0c9a" visibility="public" xmi:id="EAID_src3b0353fecd8b0154696372df3a5d0c9a" xmi:type="uml:Property"><type xmi:idref="EAID_4032c8e6f38d693bf11cf65dcf952897"/></ownedEnd></packagedElement><packagedElement name="RmGDestNto1InfoRam:VarStruct" visibility="public" xmi:id="EAID_7b6fe4565a5cc2aeee984f88620ef58d" xmi:type="uml:InstanceSpecification"/><packagedElement name="RmGDestTpTxStateRam:VarStruct" visibility="public" xmi:id="EAID_ba1a1d2ef2705f55b52c59a0d0c07899" xmi:type="uml:InstanceSpecification"/><packagedElement name="RpgRam:VarStruct" visibility="public" xmi:id="EAID_0af0b029b9c2a583f13c3e45c8e8ad4d" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_82f8a45cf31057d0b5e8c64b14f7ec5d" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst82f8a45cf31057d0b5e8c64b14f7ec5d"/><ownedEnd aggregation="none" association="EAID_82f8a45cf31057d0b5e8c64b14f7ec5d" visibility="public" xmi:id="EAID_dst82f8a45cf31057d0b5e8c64b14f7ec5d" xmi:type="uml:Property"><type xmi:idref="EAID_b3f09b00ce1f1c35f117db4c29088408"/></ownedEnd><memberEnd xmi:idref="EAID_src82f8a45cf31057d0b5e8c64b14f7ec5d"/><ownedEnd aggregation="none" association="EAID_82f8a45cf31057d0b5e8c64b14f7ec5d" visibility="public" xmi:id="EAID_src82f8a45cf31057d0b5e8c64b14f7ec5d" xmi:type="uml:Property"><type xmi:idref="EAID_0af0b029b9c2a583f13c3e45c8e8ad4d"/></ownedEnd></packagedElement><packagedElement name="SingleBufferRam:VarStruct" visibility="public" xmi:id="EAID_33747b7b74f574b918549d35b4ae5f52" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_173bfe5e6f8ff3d96881966a06169184" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst173bfe5e6f8ff3d96881966a06169184"/><ownedEnd aggregation="none" association="EAID_173bfe5e6f8ff3d96881966a06169184" visibility="public" xmi:id="EAID_dst173bfe5e6f8ff3d96881966a06169184" xmi:type="uml:Property"><type xmi:idref="EAID_1094eee4236ecf35e177133cdc6264b3"/></ownedEnd><memberEnd xmi:idref="EAID_src173bfe5e6f8ff3d96881966a06169184"/><ownedEnd aggregation="none" association="EAID_173bfe5e6f8ff3d96881966a06169184" visibility="public" xmi:id="EAID_src173bfe5e6f8ff3d96881966a06169184" xmi:type="uml:Property"><type xmi:idref="EAID_33747b7b74f574b918549d35b4ae5f52"/></ownedEnd></packagedElement><packagedElement name="SmFibRam:VarStruct" visibility="public" xmi:id="EAID_daa8b6f32a84c888e6a6c7b47d00f205" xmi:type="uml:InstanceSpecification"/><packagedElement name="SpinlockRam:VarStruct" visibility="public" xmi:id="EAID_d9aa7d92241e260643b1358dffacb978" xmi:type="uml:InstanceSpecification"/><packagedElement name="RmTransmitFctPtr:ConstArray" visibility="public" xmi:id="EAID_7a67a4fb4e819882670e169718e8787b" xmi:type="uml:InstanceSpecification"/><packagedElement name="SingleBufferInitValuesRom:ConstArray" visibility="public" xmi:id="EAID_8296be806b543bbc3f27974973f54c80" xmi:type="uml:InstanceSpecification"/><packagedElement name="SmGDestFilterFctPtr:ConstArray" visibility="public" xmi:id="EAID_d5a103e5a6cc0475c1ec3dc8e1dbd083" xmi:type="uml:InstanceSpecification"/><packagedElement name="SmLinearTaToSaCalculationStrategyGetSaFctPtr:ConstArray" visibility="public" xmi:id="EAID_93bb0c9746ce5aeb167c4035c538984f" xmi:type="uml:InstanceSpecification"/><packagedElement name="SmSrcFilterFctPtr:ConstArray" visibility="public" xmi:id="EAID_72292af1e228249cb8ec3f91aa7c30ec" xmi:type="uml:InstanceSpecification"/><packagedElement name="ConfigId:ConstVar" visibility="public" xmi:id="EAID_cf7a45675a928b9a1174720d2d0459e7" xmi:type="uml:InstanceSpecification"/><packagedElement name="BmTxBufferArrayRam:VarArray" visibility="public" xmi:id="EAID_0b3bb79055f64547aff63ad1c9af60aa" xmi:type="uml:InstanceSpecification"/><packagedElement name="InterfaceFifoQueueArrayRam:VarArray" visibility="public" xmi:id="EAID_b3a723d4b5036b9c36e217c7db701110" xmi:type="uml:InstanceSpecification"/><packagedElement name="McQBufferArrayRam:VarArray" visibility="public" xmi:id="EAID_98ead75a4e649dab381a9d3c11c70cf3" xmi:type="uml:InstanceSpecification"/><packagedElement name="SingleBufferArrayRam:VarArray" visibility="public" xmi:id="EAID_2b5839f711308fa5f78c48a05d874fb7" xmi:type="uml:InstanceSpecification"/><packagedElement name="Initialized:Var" visibility="public" xmi:id="EAID_045c79f73ab47665923017576a0b630d" xmi:type="uml:InstanceSpecification"/></uml:Model></xmi:XMI>
