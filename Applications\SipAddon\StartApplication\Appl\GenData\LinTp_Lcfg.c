/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: LinTp
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: LinTp_Lcfg.c
 *   Generation Time: 2025-08-05 10:37:17
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/


#define LINTP_LCFG_SOURCE

/**********************************************************************************************************************
 *  MISRA
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  Includes
 *********************************************************************************************************************/
#include "LinIf.h"
#include "PduR_LinTp.h"

/**********************************************************************************************************************
  LOCAL DATA PROTOTYPES
**********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: LOCAL DATA PROTOTYPES
**********************************************************************************************************************/

/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: LOCAL DATA PROTOTYPES
**********************************************************************************************************************/



/**********************************************************************************************************************
  LOCAL DATA
**********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: LOCAL DATA
**********************************************************************************************************************/

/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: LOCAL DATA
**********************************************************************************************************************/



/**********************************************************************************************************************
  GLOBAL DATA
**********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL DATA
**********************************************************************************************************************/
/**********************************************************************************************************************
  LinTp_ChannelConfig
**********************************************************************************************************************/
/** 
  \var    LinTp_ChannelConfig
  \details
  Element                    Description
  LinTp_SchedChangeNotify
  LinTp_StrictNADCheck       My comment /ActiveEcuC/LinTp/LinTpGlobalConfig/CHNL_45618847
*/ 
#define LINTP_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(LinTp_ChannelConfigType, LINTP_CONST) LinTp_ChannelConfig[1] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    LinTp_SchedChangeNotify  LinTp_StrictNADCheck        Referable Keys */
  { /*     0 */                   FALSE,                FALSE }   /* [/ActiveEcuC/LinTp/LinTpGlobalConfig/CHNL_45618847] */
};
#define LINTP_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  LinTp_LinIfToLinTpChannel
**********************************************************************************************************************/
/** 
  \var    LinTp_LinIfToLinTpChannel
  \details
  Element    Description
  Channel    Index into LinTp_ChannelConfig structure
*/ 
#define LINTP_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(LinTp_LinIfToLinTpChannelType, LINTP_CONST) LinTp_LinIfToLinTpChannel[1] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    Channel                                          */
  { /*     0 */ 0x00u /* LinIfConf_LinIfChannel_CHNL_45618847 */ }
};
#define LINTP_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  LinTp_RxNSdu
**********************************************************************************************************************/
/** 
  \var    LinTp_RxNSdu
  \brief  List of all LinTp RxNsdus sorted by their PduId
  \details
  Element            Description
  UpperLayerPduId    RxNSdu external ID (SNV)
  CtrlIdx            the index of the 1:1 relation pointing to LinTp_Ctrl
  NAD                NAD
  Ncr                Ncr timeout in ticks
*/ 
#define LINTP_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(LinTp_RxNSduType, LINTP_CONST) LinTp_RxNSdu[2] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    UpperLayerPduId                          CtrlIdx  NAD    Ncr          Referable Keys */
  { /*     0 */ PduRConf_PduRSrcPdu_PduRSrcPdu_e930d1d3,      0u, 0x01u, 0x97u },  /* [/ActiveEcuC/LinTp/LinTpGlobalConfig/SlaveResp_RearECU_oLIN00_c13bc529_Rx, /ActiveEcuC/LinIf/LinIfGlobalConfig/CHNL_45618847] */
  { /*     1 */ PduRConf_PduRSrcPdu_PduRSrcPdu_74fe1f87,      0u, 0x03u, 0x97u }   /* [/ActiveEcuC/LinTp/LinTpGlobalConfig/SlaveResp_Slave3_oLIN00_97811d8d_Rx, /ActiveEcuC/LinIf/LinIfGlobalConfig/CHNL_45618847] */
};
#define LINTP_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  LinTp_TxNSdu
**********************************************************************************************************************/
/** 
  \var    LinTp_TxNSdu
  \brief  List of all LinTp TxNsdus sorted by their PduId
  \details
  Element               Description
  UpperLayerPduId       TxNSdu external ID (SNV)
  AssociatedRxNSduId    Id of associated RxNSdu that is connected to this TxNSdu (only relevant for master nodes)
  CtrlIdx               the index of the 1:1 relation pointing to LinTp_Ctrl
  NAD                   NAD
  Nas                   Nas timeout in ticks
  Ncs                   Ncs timeout in ticks
*/ 
#define LINTP_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(LinTp_TxNSduType, LINTP_CONST) LinTp_TxNSdu[3] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    UpperLayerPduId                                            AssociatedRxNSduId  CtrlIdx  NAD    Nas    Ncs          Comment                                                                  Referable Keys */
  { /*     0 */ PduRConf_PduRDestPdu_MasterReq_RearECU_oLIN00_a4a697e2_Tx,                 0u,      0u, 0x01u, 0x65u, 0x97u },  /* [LinTpConf_LinTpRxNSdu_SlaveResp_RearECU_oLIN00_c13bc529_Rx] */  /* [/ActiveEcuC/LinTp/LinTpGlobalConfig/MasterReq_RearECU_oLIN00_8ed7799b_Tx] */
  { /*     1 */ PduRConf_PduRDestPdu_MasterReq_Slave3_oLIN00_93dc5ed4_Tx ,                 1u,      0u, 0x03u, 0x65u, 0x97u },  /* [LinTpConf_LinTpRxNSdu_SlaveResp_Slave3_oLIN00_97811d8d_Rx]  */  /* [/ActiveEcuC/LinTp/LinTpGlobalConfig/MasterReq_Slave3_oLIN00_a4cffd2e_Tx] */
  { /*     2 */ PduRConf_PduRDestPdu_MasterReq_oLIN00_3234fe1b_Tx        ,               255u,      0u, 0x7Eu, 0x65u, 0x97u }   /* [No referenced RxNSdu]                                       */  /* [/ActiveEcuC/LinTp/LinTpGlobalConfig/MasterReq_oLIN00_4a2bb011_Tx] */
};
#define LINTP_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  LinTp_Ctrl
**********************************************************************************************************************/
#define LINTP_START_SEC_VAR_NOINIT_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(LinTp_CtrlUType, LINTP_VAR_NOINIT) LinTp_Ctrl;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */  /* Data structure per LinIf channel */
  /* Index        Referable Keys */
  /*     0 */  /* [CHNL_45618847] */

#define LINTP_STOP_SEC_VAR_NOINIT_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */


/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: GLOBAL DATA
**********************************************************************************************************************/





