/*********************************************************************************************************************
 *  COPYRIGHT                                                                                                        *
 *  ---------------------------------------------------------------------------------------------------------------- *
 *  Copyright (c) 2013 - 2025 by Vector Informatik GmbH.                                        All rights reserved. *
 *                                                                                                                   *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.                    *
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.       *
 *                All other rights remain with Vector Informatik GmbH.                                               *
 *  ---------------------------------------------------------------------------------------------------------------- *
 *  FILE DESCRIPTION                                                                                                 *
 *  ---------------------------------------------------------------------------------------------------------------- *
 *             File: McData.a2l                                                                                      *
 *      Description: MICROSAR A2L file                                                                               *
 *     Generated at: 2025-08-05 10:49:26                                                                             *
 *        Generator: MICROSAR McDataConverter, Version ********                                                      *
 *  ---------------------------------------------------------------------------------------------------------------- *
 *  CUSTOMER INFORMATION                                                                                             *
 *  ---------------------------------------------------------------------------------------------------------------- *
 *         Customer: AUTOSAR 4 Evaluation Bundle                                                                     *
 *    Serial Number: CBD2000456                                                                                      *
 *  Delivery Number: 2                                                                                               *
 *      Expiry Date:                                                                                                 *
 *********************************************************************************************************************/


/*****************************************************************************
 * RECORD_LAYOUT                                                             *
 *****************************************************************************/

/begin RECORD_LAYOUT 
    VECTOR_RL_BOOLEAN                             /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        UBYTE                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_UBYTE                               /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        UBYTE                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_UWORD                               /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        UWORD                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_ULONG                               /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        ULONG                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_SBYTE                               /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        SBYTE                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_SWORD                               /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        SWORD                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_SLONG                               /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        SLONG                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_FLOAT32_IEEE                        /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        FLOAT32_IEEE                         /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_FLOAT64_IEEE                        /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        FLOAT64_IEEE                         /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT


/*****************************************************************************
 * CHARACTERISTIC                                                            *
 *****************************************************************************/


/*****************************************************************************
 * MEASUREMENT                                                               *
 *****************************************************************************/


/*****************************************************************************
 * COMPU_METHOD                                                              *
 *****************************************************************************/


/*****************************************************************************
 * COMPU_VTAB_RANGE                                                          *
 *****************************************************************************/


/*****************************************************************************
 * GROUP                                                                     *
 *****************************************************************************/

/begin GROUP 
    Fr                                            /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    Lin                                           /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    BswM                                          /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    CanIf                                         /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    CanNm                                         /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    CanSM                                         /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    CanTp                                         /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    Com                                           /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    ComM                                          /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    EcuM                                          /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    FrIf                                          /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    FrNm                                          /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    FrSM                                          /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    LinIf                                         /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    LinTp                                         /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    LinNm                                         /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    LinSM                                         /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    Nm                                            /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    NvM                                           /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    PduR                                          /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

