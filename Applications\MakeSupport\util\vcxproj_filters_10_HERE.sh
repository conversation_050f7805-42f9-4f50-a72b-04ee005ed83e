
set -e
# set -x

########################################################################
#
#  HERE script to generate Visual studio project file.
#
#  Input Param as environment variables:
#  -------------------------------------
#  - VC_INC_LIST        : ';' separated list of include paths
#  - VC_DEF_LIST        : ';' separated list of defines
#  - VC_SRC_LIST        : space separated list of source files
#  - LST_PATH           : $(LST_PATH) from Makefile
#  - APPL_DIR           : Application directory. Like 'Appl'
#  - MAKE_FILES         : MakeSupport files.
#  - LIBRARIES_TO_BUILD : list of AutoSar libs
#  - GLOBAL_COMP_DIR    : GLOBAL_COMP_DIR from Makefile
#  - GENDATA_DIR        : GENDATA_DIR from Makefile
#
#  Call example:
#  -------------
#  VC_INC_LIST="c:/;d:/" \
#  VC_DEF_LIST="BRS_01;BRS_02" \
#  VC_SRC_LIST="file1.c file2.c" \
#  vcxproj_10_HERE.sh
#
########################################################################

global_filters=

###############################
function file2filter()
#
#  @param  $1   filename
#  @return      filtername or "" if none
#
#  1. try to detect from module in BSW directory -> BSW\mod
#  2. use shared -> subdir
#  3. use gendata and filename -> BSW\mod
#  4. use Source
#  --- if not found  ---
#  5. check gendata -> gendata
#
{
  filter=`sed -nE -e 's#^.*'"${GLOBAL_COMP_DIR}"'(\\\\|/)([^\\\\/]+).*#'"${GLOBAL_COMP_DIR}"'\\\\\2#p' \
                  -e 's#^.*shared(\\\\|/)([^\\\\/]+).*#shared\\\\\2#p' \
                  -e 's#.*'"${GENDATA_DIR}"'/(\\w+)_(init_cfg|cfg|init_lcfg|lcfg|init_pbcfg|pbcfg)\\.(c|h|cpp)$#'"${GLOBAL_COMP_DIR}"'\\\\\1#Ip' \
                  -e 's#^.*Source/.*$#Source#p'<<<${1}`
  if [[ -n ${filter} ]]; then
    echo ${filter}
    return 0
  fi

  filter=`sed -nE -e 's#.*'"${GENDATA_DIR}"'/(\\w+)\\.(c|h|cpp)$#'"${GENDATA_DIR}"'#Ip' <<<${1}`
  if [[ -n ${filter} ]]; then
    echo ${filter}
    return 0
  fi
}

########################################################################
#
#   MAIN
#
########################################################################

verbose=0

[[ $verbose == 1 ]] && echo "Start" >&2

#
#  Header
#
cat <<+++
﻿<?xml version="1.0" encoding="utf-8"?>
    <Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <!-- generated by MakeSupport -->
+++

#
#  Source Files
#
if [[ -n ${VC_SRC_LIST} ]]; then
  echo "      <ItemGroup>"
  for file in ${VC_SRC_LIST}; do
    filter=`file2filter ${file}`
    [[ $verbose == 1 ]] && echo "${file} -> ${filter}" >&2
    global_filters="${global_filters} ${filter}"
    if [[ -n "${filter}" ]]; then
      echo "        <ClCompile Include=\"${file}\" >"
      echo "          <Filter>${filter}</Filter>"
      echo "        </ClCompile>"
    else
      echo "        <ClCompile Include=\"${file}\" />"
    fi
  done
  echo "      </ItemGroup>"
fi

[[ $verbose == 1 ]] && echo "Files done" >&2

#
#  Makefile
#
if [[ -n ${MAKE_FILES} ]]; then
  echo "      <ItemGroup>"
  for file in ${MAKE_FILES}; do
    cat <<+++
        <None Include="${file}">
          <Filter>MakeSupport</Filter>
        </None>
+++
  done
  echo "      </ItemGroup>"
fi

[[ $verbose == 1 ]] && echo "Makefiles done" >&2

#
#  Filter
#
if [[ -n ${global_filters} ]]; then
  echo "      <ItemGroup>"
  if [[ ${global_filters} =~ ${GLOBAL_COMP_DIR} ]]; then
    echo "      <Filter Include=\"BSW\">"
    echo "      </Filter>"
    echo "      <Filter Include=\"shared\">"
    echo "      </Filter>"
  fi
    echo "      <Filter Include=\"MakeSupport\">"
    echo "      </Filter>"
  for filter in `tr " " "\n" <<<${global_filters} | sort -u | tr "\n" " "`; do
    cat <<+++
        <Filter Include="${filter}">
          <Extensions>c;cpp;h</Extensions>
        </Filter>
+++
  done
  echo "      </ItemGroup>"
fi

[[ $verbose == 1 ]] && echo "Filter done" >&2

#
#  Terminate XML
#
cat <<+++
    </Project>
+++
