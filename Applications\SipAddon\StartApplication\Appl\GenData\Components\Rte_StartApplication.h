/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_StartApplication.h
 *           Config:  Demo.dpa
 *      ECU-Project:  Demo
 *
 *        Generator:  MICROSAR RTE Generator Version 4.22.1
 *                    RTE Core Version 1.22.1
 *          License:  CBD2000456
 *
 *      Description:  Application header file for SW-C <StartApplication>
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_STARTAPPLICATION_H
# define RTE_STARTAPPLICATION_H

# ifndef RTE_CORE
#  ifdef RTE_APPLICATION_HEADER_FILE
#   error Multiple application header files included.
#  endif
#  define RTE_APPLICATION_HEADER_FILE
#  ifndef RTE_PTR2ARRAYBASETYPE_PASSING
#   define RTE_PTR2ARRAYBASETYPE_PASSING
#  endif
# endif

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

/* include files */

# include "Rte_StartApplication_Type.h"
# include "Rte_DataHandleType.h"


# ifndef RTE_CORE
/**********************************************************************************************************************
 * Init Values for unqueued S/R communication (primitive types only)
 *********************************************************************************************************************/

#  define Rte_InitValue_PpSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx_DeSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx (0U)
#  define Rte_InitValue_PpSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx_DeSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx (0U)
#  define Rte_InitValue_PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_DeSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx (1U)
#  define Rte_InitValue_PpSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx_DeSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx (0U)
#  define Rte_InitValue_PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx_DeSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx (0U)
#  define Rte_InitValue_PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx_DeSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx (0U)
#  define Rte_InitValue_PpStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx_DeStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx (0U)
# endif


# ifndef RTE_CORE
/**********************************************************************************************************************
 * Buffers for inter-runnable variables
 *********************************************************************************************************************/

#  define RTE_START_SEC_VAR_INIT_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(uint16, RTE_VAR_INIT) Rte_Irv_StartApplication_IrvOccuranceCounterDid;

#  define RTE_STOP_SEC_VAR_INIT_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
# endif /* !defined(RTE_CORE) */


# define RTE_START_SEC_CODE
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * API prototypes
 *********************************************************************************************************************/
FUNC(Std_ReturnType, RTE_CODE) Rte_Read_StartApplication_PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx_DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx(P2VAR(Dt_SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) data); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Read_StartApplication_PpSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx_DeSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx(P2VAR(uint8, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) data); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Read_StartApplication_PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_DeSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx(P2VAR(uint32, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) data); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Read_StartApplication_PpStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx_DeStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx(P2VAR(uint16, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) data); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Write_StartApplication_PpSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx_DeSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx(uint8 data); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Write_StartApplication_PpSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx_DeSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx(uint32 data); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Write_StartApplication_PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx_DeSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx(uint16 data); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Write_StartApplication_PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx_DeSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx(uint32 data); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Call_StartApplication_PpDemOpCycle_PowerCycle_SetOperationCycleState(Dem_OperationCycleStateType CycleState); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Call_StartApplication_PpDiagnosticMonitor_DEM_EVENT_StartApplication_SetEventStatus(Dem_EventStatusType EventStatus); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Call_StartApplication_PpPS_StartApplication_NvMBlock1_ReadBlock(dtRef_VOID DstPtr); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Call_StartApplication_PpPS_StartApplication_NvMBlock1_WriteBlock(dtRef_const_VOID SrcPtr); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Call_StartApplication_PpPS_StartApplication_NvMBlock2_ReadBlock(dtRef_VOID DstPtr); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Call_StartApplication_PpPS_StartApplication_NvMBlock2_WriteBlock(dtRef_const_VOID SrcPtr); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Call_StartApplication_UR_CN_CAN_52ce3533_GetCurrentComMode(P2VAR(ComM_ModeType, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) ComMode); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Call_StartApplication_UR_CN_CAN_52ce3533_RequestComMode(ComM_ModeType ComMode); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Call_StartApplication_UR_CN_FlexRay_oChannel_A_8b187a93_GetCurrentComMode(P2VAR(ComM_ModeType, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) ComMode); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Call_StartApplication_UR_CN_FlexRay_oChannel_A_8b187a93_RequestComMode(ComM_ModeType ComMode); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Call_StartApplication_UR_CN_LIN00_0a7bdc9c_GetCurrentComMode(P2VAR(ComM_ModeType, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) ComMode); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(Std_ReturnType, RTE_CODE) Rte_Call_StartApplication_UR_CN_LIN00_0a7bdc9c_RequestComMode(ComM_ModeType ComMode); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */

# define RTE_STOP_SEC_CODE
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Rte_Read_<p>_<d> (explicit S/R communication with isQueued = false)
 *********************************************************************************************************************/
#  define Rte_Read_PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx_DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx Rte_Read_StartApplication_PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx_DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx
#  define Rte_Read_PpSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx_DeSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx Rte_Read_StartApplication_PpSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx_DeSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx
#  define Rte_Read_PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_DeSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx Rte_Read_StartApplication_PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_DeSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx
#  define Rte_Read_PpStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx_DeStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx Rte_Read_StartApplication_PpStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx_DeStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx


/**********************************************************************************************************************
 * Rte_Write_<p>_<d> (explicit S/R communication with isQueued = false)
 *********************************************************************************************************************/
#  define Rte_Write_PpSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx_DeSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx Rte_Write_StartApplication_PpSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx_DeSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx
#  define Rte_Write_PpSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx_DeSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx Rte_Write_StartApplication_PpSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx_DeSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx
#  define Rte_Write_PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx_DeSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx Rte_Write_StartApplication_PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx_DeSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx
#  define Rte_Write_PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx_DeSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx Rte_Write_StartApplication_PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx_DeSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx


/**********************************************************************************************************************
 * Rte_Call_<p>_<o> (unmapped) for synchronous C/S communication
 *********************************************************************************************************************/
#  define Rte_Call_PpDemOpCycle_PowerCycle_SetOperationCycleState Rte_Call_StartApplication_PpDemOpCycle_PowerCycle_SetOperationCycleState
#  define Rte_Call_PpDiagnosticMonitor_DEM_EVENT_StartApplication_SetEventStatus Rte_Call_StartApplication_PpDiagnosticMonitor_DEM_EVENT_StartApplication_SetEventStatus
#  define Rte_Call_PpPS_StartApplication_NvMBlock1_ReadBlock Rte_Call_StartApplication_PpPS_StartApplication_NvMBlock1_ReadBlock
#  define Rte_Call_PpPS_StartApplication_NvMBlock1_WriteBlock Rte_Call_StartApplication_PpPS_StartApplication_NvMBlock1_WriteBlock
#  define Rte_Call_PpPS_StartApplication_NvMBlock2_ReadBlock Rte_Call_StartApplication_PpPS_StartApplication_NvMBlock2_ReadBlock
#  define Rte_Call_PpPS_StartApplication_NvMBlock2_WriteBlock Rte_Call_StartApplication_PpPS_StartApplication_NvMBlock2_WriteBlock
#  define Rte_Call_UR_CN_CAN_52ce3533_GetCurrentComMode Rte_Call_StartApplication_UR_CN_CAN_52ce3533_GetCurrentComMode
#  define Rte_Call_UR_CN_CAN_52ce3533_RequestComMode Rte_Call_StartApplication_UR_CN_CAN_52ce3533_RequestComMode
#  define Rte_Call_UR_CN_FlexRay_oChannel_A_8b187a93_GetCurrentComMode Rte_Call_StartApplication_UR_CN_FlexRay_oChannel_A_8b187a93_GetCurrentComMode
#  define Rte_Call_UR_CN_FlexRay_oChannel_A_8b187a93_RequestComMode Rte_Call_StartApplication_UR_CN_FlexRay_oChannel_A_8b187a93_RequestComMode
#  define Rte_Call_UR_CN_LIN00_0a7bdc9c_GetCurrentComMode Rte_Call_StartApplication_UR_CN_LIN00_0a7bdc9c_GetCurrentComMode
#  define Rte_Call_UR_CN_LIN00_0a7bdc9c_RequestComMode Rte_Call_StartApplication_UR_CN_LIN00_0a7bdc9c_RequestComMode


/**********************************************************************************************************************
 * Inter-runnable variables
 *********************************************************************************************************************/

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvRead_StartApplication_Cyclic250ms_IrvOccuranceCounterDid() \
  Rte_Irv_StartApplication_IrvOccuranceCounterDid
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvRead_StartApplication_DIAG_DcmReadData_IrvOccuranceCounterDid() \
  Rte_Irv_StartApplication_IrvOccuranceCounterDid
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvWrite_StartApplication_DIAG_DcmWriteData_IrvOccuranceCounterDid(data) \
  (Rte_Irv_StartApplication_IrvOccuranceCounterDid = (data))
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvRead_StartApplication_DIAG_DemReadData_IrvOccuranceCounterDid() \
  Rte_Irv_StartApplication_IrvOccuranceCounterDid
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvRead_StartApplication_OnDataRec_RxData_IrvOccuranceCounterDid() \
  Rte_Irv_StartApplication_IrvOccuranceCounterDid
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvWrite_StartApplication_OnDataRec_RxData_IrvOccuranceCounterDid(data) \
  (Rte_Irv_StartApplication_IrvOccuranceCounterDid = (data))
/* PRQA L:L1 */


/**********************************************************************************************************************
 * Rte_Pim (Per-Instance Memory)
 *********************************************************************************************************************/

#  ifndef RTE_MICROSAR_PIM_EXPORT
#   define RTE_START_SEC_VAR_DEFAULT_RTE_PIM_GROUP_UNSPECIFIED
#   include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(uint32, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_COM_RxSigValue0;
extern VAR(uint32, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_COM_TxSigValue0;
extern VAR(uint32, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_COM_TxSigValue2;
extern VAR(uint16, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_COM_RxSigValue2;
extern VAR(EnumActiveComponentType, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_ActiveComponent;
extern VAR(uint8, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_COM_RxSigValue1;
extern VAR(uint8, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_COM_TxSigValue1;
extern VAR(uint8, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_DIAG_LastRxData;
extern VAR(uint8, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_MEM_ActiveBlock;
extern VAR(EnumMEM_BlockStateType, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_MEM_BlockState1;
extern VAR(EnumMEM_BlockStateType, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_MEM_BlockState2;
extern VAR(uint8, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_MEM_DataBuffer1;
extern VAR(uint8, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_MEM_DataBuffer2;
extern VAR(uint8, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_TxCtrlSigValue;
extern VAR(uint8, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_TxDataSigValue;
extern VAR(uint8, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_Uptime;
extern VAR(Dt_SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_RxDataSgBuffer;
extern VAR(Dt_SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx, RTE_VAR_DEFAULT_RTE_PIM_GROUP) Rte_StartApplication_Shadow_PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx_DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2_a3f6706d;

#   define RTE_STOP_SEC_VAR_DEFAULT_RTE_PIM_GROUP_UNSPECIFIED
#   include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  endif

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_COM_RxSigValue0() \
  (&Rte_StartApplication_COM_RxSigValue0)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_COM_TxSigValue0() \
  (&Rte_StartApplication_COM_TxSigValue0)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_COM_TxSigValue2() \
  (&Rte_StartApplication_COM_TxSigValue2)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_COM_RxSigValue2() \
  (&Rte_StartApplication_COM_RxSigValue2)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_ActiveComponent() \
  (&Rte_StartApplication_ActiveComponent)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_COM_RxSigValue1() \
  (&Rte_StartApplication_COM_RxSigValue1)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_COM_TxSigValue1() \
  (&Rte_StartApplication_COM_TxSigValue1)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_DIAG_LastRxData() \
  (&Rte_StartApplication_DIAG_LastRxData)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_MEM_ActiveBlock() \
  (&Rte_StartApplication_MEM_ActiveBlock)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_MEM_BlockState1() \
  (&Rte_StartApplication_MEM_BlockState1)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_MEM_BlockState2() \
  (&Rte_StartApplication_MEM_BlockState2)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_MEM_DataBuffer1() \
  (&Rte_StartApplication_MEM_DataBuffer1)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_MEM_DataBuffer2() \
  (&Rte_StartApplication_MEM_DataBuffer2)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_TxCtrlSigValue() \
  (&Rte_StartApplication_TxCtrlSigValue)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_TxDataSigValue() \
  (&Rte_StartApplication_TxDataSigValue)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_Uptime() \
  (&Rte_StartApplication_Uptime)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_RxDataSgBuffer() \
  (&Rte_StartApplication_RxDataSgBuffer)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_Pim_Shadow_PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx_DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2_a3f6706d() \
  (&Rte_StartApplication_Shadow_PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx_DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2_a3f6706d)
/* PRQA L:L1 */


# endif /* !defined(RTE_CORE) */


# define StartApplication_START_SEC_CODE
# include "StartApplication_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * Runnable entities
 *********************************************************************************************************************/

# ifndef RTE_CORE
#  define RTE_RUNNABLE_StartApplication_Cyclic1000ms StartApplication_Cyclic1000ms
#  define RTE_RUNNABLE_StartApplication_Cyclic10ms StartApplication_Cyclic10ms
#  define RTE_RUNNABLE_StartApplication_Cyclic1ms StartApplication_Cyclic1ms
#  define RTE_RUNNABLE_StartApplication_Cyclic250ms StartApplication_Cyclic250ms
#  define RTE_RUNNABLE_StartApplication_DIAG_ConditionCheckRead StartApplication_DIAG_ConditionCheckRead
#  define RTE_RUNNABLE_StartApplication_DIAG_DcmReadData StartApplication_DIAG_DcmReadData
#  define RTE_RUNNABLE_StartApplication_DIAG_DcmWriteData StartApplication_DIAG_DcmWriteData
#  define RTE_RUNNABLE_StartApplication_DIAG_DemReadData StartApplication_DIAG_DemReadData
#  define RTE_RUNNABLE_StartApplication_Init StartApplication_Init
#  define RTE_RUNNABLE_StartApplication_MEM_JobFinished_StartApplication_NvMBlock1 StartApplication_MEM_JobFinished_StartApplication_NvMBlock1
#  define RTE_RUNNABLE_StartApplication_MEM_JobFinished_StartApplication_NvMBlock2 StartApplication_MEM_JobFinished_StartApplication_NvMBlock2
#  define RTE_RUNNABLE_StartApplication_OnDataRec_RxCtrl StartApplication_OnDataRec_RxCtrl
#  define RTE_RUNNABLE_StartApplication_OnDataRec_RxData StartApplication_OnDataRec_RxData
# endif

FUNC(void, StartApplication_CODE) StartApplication_Cyclic1000ms(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, StartApplication_CODE) StartApplication_Cyclic10ms(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, StartApplication_CODE) StartApplication_Cyclic1ms(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, StartApplication_CODE) StartApplication_Cyclic250ms(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, StartApplication_CODE) StartApplication_DIAG_ConditionCheckRead(P2VAR(Dcm_NegativeResponseCodeType, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) ErrorCode); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
# ifdef RTE_PTR2ARRAYBASETYPE_PASSING
FUNC(void, StartApplication_CODE) StartApplication_DIAG_DcmReadData(P2VAR(uint8, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) Data); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
# else
FUNC(void, StartApplication_CODE) StartApplication_DIAG_DcmReadData(P2VAR(Dcm_Data2ByteType, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) Data); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
# endif
# ifdef RTE_PTR2ARRAYBASETYPE_PASSING
FUNC(void, StartApplication_CODE) StartApplication_DIAG_DcmWriteData(P2CONST(uint8, AUTOMATIC, RTE_STARTAPPLICATION_APPL_DATA) Data, P2VAR(Dcm_NegativeResponseCodeType, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) ErrorCode); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
# else
FUNC(void, StartApplication_CODE) StartApplication_DIAG_DcmWriteData(P2CONST(Dcm_Data2ByteType, AUTOMATIC, RTE_STARTAPPLICATION_APPL_DATA) Data, P2VAR(Dcm_NegativeResponseCodeType, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) ErrorCode); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
# endif
# ifdef RTE_PTR2ARRAYBASETYPE_PASSING
FUNC(void, StartApplication_CODE) StartApplication_DIAG_DemReadData(P2VAR(uint8, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) Data); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
# else
FUNC(void, StartApplication_CODE) StartApplication_DIAG_DemReadData(P2VAR(DataArray_Type_2, AUTOMATIC, RTE_STARTAPPLICATION_APPL_VAR) Data); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
# endif
FUNC(void, StartApplication_CODE) StartApplication_Init(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, StartApplication_CODE) StartApplication_MEM_JobFinished_StartApplication_NvMBlock1(NvM_ServiceIdType ServiceId, NvM_RequestResultType JobResult); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(void, StartApplication_CODE) StartApplication_MEM_JobFinished_StartApplication_NvMBlock2(NvM_ServiceIdType ServiceId, NvM_RequestResultType JobResult); /* PRQA S 0786, 3449, 0624 */ /* MD_Rte_0786, MD_Rte_3449, MD_Rte_0624 */
FUNC(void, StartApplication_CODE) StartApplication_OnDataRec_RxCtrl(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, StartApplication_CODE) StartApplication_OnDataRec_RxData(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */

# define StartApplication_STOP_SEC_CODE
# include "StartApplication_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


# ifndef RTE_CORE
/**********************************************************************************************************************
 * Application errors
 *********************************************************************************************************************/

#  define RTE_E_PiDemOpCycle_PowerCycle_E_NOT_OK (1U)

#  define RTE_E_PiDiagnosticMonitor_DEM_EVENT_StartApplication_E_NOT_OK (1U)

#  define RTE_E_PiPS_StartApplication_NvMBlock1_E_NOT_OK (1U)

#  define RTE_E_PiPS_StartApplication_NvMBlock2_E_NOT_OK (1U)

#  define RTE_E_PiUR_CN_CAN_52ce3533_E_MODE_LIMITATION (2U)

#  define RTE_E_PiUR_CN_CAN_52ce3533_E_NOT_OK (1U)

#  define RTE_E_PiUR_CN_FlexRay_oChannel_A_8b187a93_E_MODE_LIMITATION (2U)

#  define RTE_E_PiUR_CN_FlexRay_oChannel_A_8b187a93_E_NOT_OK (1U)

#  define RTE_E_PiUR_CN_LIN00_0a7bdc9c_E_MODE_LIMITATION (2U)

#  define RTE_E_PiUR_CN_LIN00_0a7bdc9c_E_NOT_OK (1U)
# endif /* !defined(RTE_CORE) */

# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_STARTAPPLICATION_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_0624:  MISRA rule: Rule8.3
     Reason:     This MISRA violation is a consequence from the RTE requirements [SWS_Rte_01007] [SWS_Rte_01150].
                 The typedefs are never used in the same context.
     Risk:       No functional risk. Only a cast to uint8* is performed.
     Prevention: Not required.

   MD_Rte_0786:  MISRA rule: Rule5.5
     Reason:     Same macro and idintifier names in first 63 characters are required to meet AUTOSAR spec.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3449:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3451:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

*/
